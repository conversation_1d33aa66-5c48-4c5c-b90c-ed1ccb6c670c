{% extends "base.html" %}

{% block title %}التوقيعات الرقمية: {{ document.title }} - نظام إدارة الأرشيف العام{% endblock %}

{% block extra_css %}
<style>
    .signatures-header {
        background: linear-gradient(135deg, #2ecc71 0%, #27ae60 100%);
        color: white;
        border-radius: var(--border-radius);
        padding: 2rem;
        margin-bottom: 2rem;
        text-align: center;
    }
    
    .signatures-title {
        font-size: 2rem;
        font-weight: 700;
        margin: 0;
    }
    
    .signatures-subtitle {
        opacity: 0.9;
        margin: 0.5rem 0 0 0;
    }
    
    .document-info {
        background: white;
        border-radius: var(--border-radius);
        padding: 1.5rem;
        margin-bottom: 2rem;
        box-shadow: var(--shadow-light);
        border-left: 4px solid #2ecc71;
    }
    
    .signatures-container {
        background: white;
        border-radius: var(--border-radius);
        box-shadow: var(--shadow-light);
        overflow: hidden;
    }
    
    .signature-item {
        padding: 2rem;
        border-bottom: 1px solid #e9ecef;
        transition: all 0.3s ease;
        position: relative;
    }
    
    .signature-item:last-child {
        border-bottom: none;
    }
    
    .signature-item:hover {
        background: #f8f9fa;
    }
    
    .signature-item.invalid {
        background: #fff5f5;
        border-left: 4px solid #e74c3c;
    }
    
    .signature-item.valid {
        border-left: 4px solid #2ecc71;
    }
    
    .signature-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 1rem;
    }
    
    .signer-info {
        display: flex;
        align-items: center;
        gap: 1rem;
    }
    
    .signer-avatar {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        background: linear-gradient(135deg, #2ecc71 0%, #27ae60 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1.5rem;
        font-weight: 700;
    }
    
    .signer-details h5 {
        margin: 0;
        font-weight: 700;
        color: #2c3e50;
    }
    
    .signer-details small {
        color: #7f8c8d;
    }
    
    .signature-status {
        padding: 0.5rem 1rem;
        border-radius: 20px;
        font-size: 0.875rem;
        font-weight: 600;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }
    
    .signature-status.valid {
        background: #d4edda;
        color: #155724;
    }
    
    .signature-status.invalid {
        background: #f8d7da;
        color: #721c24;
    }
    
    .signature-details {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 1rem;
        margin-bottom: 1.5rem;
    }
    
    .detail-item {
        background: #f8f9fa;
        border-radius: var(--border-radius);
        padding: 1rem;
    }
    
    .detail-label {
        font-weight: 600;
        color: #7f8c8d;
        font-size: 0.875rem;
        margin-bottom: 0.25rem;
    }
    
    .detail-value {
        color: #2c3e50;
        font-family: 'Courier New', monospace;
        word-break: break-all;
    }
    
    .signature-actions {
        display: flex;
        gap: 0.5rem;
        flex-wrap: wrap;
    }
    
    .btn-action {
        padding: 0.5rem 1rem;
        border: none;
        border-radius: 5px;
        font-size: 0.875rem;
        cursor: pointer;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 0.25rem;
    }
    
    .btn-verify {
        background: #3498db;
        color: white;
    }
    
    .btn-verify:hover {
        background: #2980b9;
        color: white;
    }
    
    .btn-certificate {
        background: #f39c12;
        color: white;
    }
    
    .btn-certificate:hover {
        background: #e67e22;
        color: white;
    }
    
    .btn-back {
        background: linear-gradient(135deg, #95a5a6 0%, #7f8c8d 100%);
        color: white;
        padding: 0.75rem 1.5rem;
        border: none;
        border-radius: var(--border-radius);
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        margin-bottom: 2rem;
    }
    
    .btn-back:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(149, 165, 166, 0.3);
        color: white;
    }
    
    .empty-signatures {
        text-align: center;
        padding: 4rem 2rem;
        color: #6c757d;
    }
    
    .empty-signatures i {
        font-size: 4rem;
        margin-bottom: 1rem;
        opacity: 0.5;
    }
    
    .signature-hash {
        background: #e9ecef;
        border-radius: 5px;
        padding: 0.5rem;
        font-family: 'Courier New', monospace;
        font-size: 0.75rem;
        word-break: break-all;
        margin-top: 0.5rem;
    }

    .signature-type-badge {
        display: inline-block;
        padding: 0.25rem 0.75rem;
        border-radius: 15px;
        font-size: 0.8rem;
        font-weight: 600;
    }

    .signature-type-badge.normal {
        background: #e3f2fd;
        color: #1976d2;
        border: 1px solid #bbdefb;
    }

    .signature-type-badge.certified {
        background: #e8f5e8;
        color: #2e7d32;
        border: 1px solid #c8e6c9;
    }
    
    @media (max-width: 768px) {
        .signature-header {
            flex-direction: column;
            align-items: flex-start;
            gap: 1rem;
        }
        
        .signature-details {
            grid-template-columns: 1fr;
        }
        
        .signature-actions {
            flex-direction: column;
        }
        
        .signer-info {
            flex-direction: column;
            align-items: flex-start;
        }
    }
</style>
{% endblock %}

{% block content %}
<!-- Header -->
<div class="signatures-header">
    <h1 class="signatures-title">
        <i class="fas fa-signature me-3"></i>
        التوقيعات الرقمية
    </h1>
    <p class="signatures-subtitle">التوقيعات الرقمية للوثيقة وشهادات الأمان</p>
</div>

<!-- Back Button -->
<a href="{{ url_for('view_document', id=document.id) }}" class="btn-back">
    <i class="fas fa-arrow-right"></i>
    العودة للوثيقة
</a>

<!-- Document Info -->
<div class="document-info">
    <h5><i class="fas fa-file-alt me-2"></i>معلومات الوثيقة</h5>
    <div class="row">
        <div class="col-md-6">
            <strong>العنوان:</strong> {{ document.title }}
        </div>
        <div class="col-md-6">
            <strong>النوع:</strong> {{ document.document_type }}
        </div>
        <div class="col-md-6">
            <strong>تاريخ الإنشاء:</strong> {{ document.created_at.strftime('%Y/%m/%d %H:%M') }}
        </div>
        <div class="col-md-6">
            <strong>عدد التوقيعات:</strong> {{ signatures|length }}
        </div>
    </div>
</div>

<!-- Signatures List -->
<div class="signatures-container">
    {% if signatures %}
        {% for signature in signatures %}
        <div class="signature-item {{ 'valid' if signature.is_valid else 'invalid' }}">
            <div class="signature-header">
                <div class="signer-info">
                    <div class="signer-avatar">
                        {{ signature.user.full_name[0] if signature.user.full_name else signature.user.username[0] }}
                    </div>
                    <div class="signer-details">
                        <h5>{{ signature.user.full_name or signature.user.username }}</h5>
                        <small>{{ signature.user.email }}</small>
                        <small>{{ signature.user.department or 'غير محدد' }}</small>
                    </div>
                </div>
                
                <div class="signature-status {{ 'valid' if signature.is_valid else 'invalid' }}">
                    {% if signature.is_valid %}
                        <i class="fas fa-check-circle"></i>
                        توقيع صحيح
                    {% else %}
                        <i class="fas fa-times-circle"></i>
                        توقيع غير صحيح
                    {% endif %}
                </div>
            </div>
            
            <div class="signature-details">
                <div class="detail-item">
                    <div class="detail-label">تاريخ التوقيع</div>
                    <div class="detail-value">{{ signature.timestamp.strftime('%Y/%m/%d %H:%M:%S') }}</div>
                </div>
                
                <div class="detail-item">
                    <div class="detail-label">نوع التوقيع</div>
                    <div class="detail-value">
                        <span class="signature-type-badge {{ signature.signature_type }}">
                            {% if signature.signature_type == 'certified' %}
                                <i class="fas fa-certificate me-1"></i>توقيع معتمد
                            {% else %}
                                <i class="fas fa-pen me-1"></i>توقيع عادي
                            {% endif %}
                        </span>
                    </div>
                </div>

                <div class="detail-item">
                    <div class="detail-label">خوارزمية التوقيع</div>
                    <div class="detail-value">{{ signature.algorithm }}</div>
                </div>
                
                <div class="detail-item">
                    <div class="detail-label">معرف التوقيع</div>
                    <div class="detail-value">#{{ signature.id }}</div>
                </div>
                
                <div class="detail-item">
                    <div class="detail-label">حالة التحقق</div>
                    <div class="detail-value">
                        {% if signature.is_valid %}
                            <span class="text-success">✓ تم التحقق</span>
                        {% else %}
                            <span class="text-danger">✗ فشل التحقق</span>
                        {% endif %}
                    </div>
                </div>
            </div>
            
            <!-- Signature Hash -->
            <div class="detail-item">
                <div class="detail-label">بصمة التوقيع الرقمي</div>
                <div class="signature-hash">{{ signature.signature_hash }}</div>
            </div>
            
            <div class="signature-actions">
                <a href="{{ url_for('verify_signature', signature_id=signature.id) }}" 
                   class="btn-action btn-verify">
                    <i class="fas fa-shield-alt"></i>
                    التحقق من التوقيع
                </a>
                
                <a href="{{ url_for('download_signature_certificate', signature_id=signature.id) }}" 
                   class="btn-action btn-certificate">
                    <i class="fas fa-certificate"></i>
                    تحميل الشهادة
                </a>
            </div>
        </div>
        {% endfor %}
    {% else %}
        <div class="empty-signatures">
            <i class="fas fa-signature"></i>
            <h4>لا توجد توقيعات رقمية</h4>
            <p>لم يتم توقيع هذه الوثيقة رقمياً بعد</p>
            {% if current_user.is_admin() %}
            <a href="{{ url_for('sign_document_page', doc_id=document.id) }}"
               class="btn-action btn-verify">
                <i class="fas fa-pen-fancy me-2"></i>
                توقيع الوثيقة الآن
            </a>
            {% else %}
            <p class="text-muted">
                <i class="fas fa-lock me-2"></i>
                التوقيع الرقمي مقتصر على المديرين فقط
            </p>
            {% endif %}
        </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script>
    // تحسين تجربة المستخدم
    document.addEventListener('DOMContentLoaded', function() {
        // إضافة تأثيرات للتوقيعات
        const signatures = document.querySelectorAll('.signature-item');
        signatures.forEach((signature, index) => {
            setTimeout(() => {
                signature.style.opacity = '0';
                signature.style.transform = 'translateX(20px)';
                signature.style.transition = 'all 0.6s ease';
                
                setTimeout(() => {
                    signature.style.opacity = '1';
                    signature.style.transform = 'translateX(0)';
                }, 50);
            }, index * 100);
        });
        
        // إضافة تأثير نسخ للبصمات
        document.querySelectorAll('.signature-hash').forEach(hash => {
            hash.addEventListener('click', function() {
                navigator.clipboard.writeText(this.textContent).then(() => {
                    const originalText = this.textContent;
                    this.textContent = 'تم النسخ!';
                    this.style.background = '#d4edda';
                    
                    setTimeout(() => {
                        this.textContent = originalText;
                        this.style.background = '#e9ecef';
                    }, 2000);
                });
            });
            
            hash.style.cursor = 'pointer';
            hash.title = 'انقر للنسخ';
        });
    });
</script>
{% endblock %}
