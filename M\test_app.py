#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Test script to check what's preventing the main app from running
"""

import sys
import traceback

print("Starting test script...")
print(f"Python version: {sys.version}")

try:
    print("Testing basic imports...")
    import os
    print("✓ os imported")
    
    from flask import Flask
    print("✓ Flask imported")
    
    from flask_sqlalchemy import SQLAlchemy
    print("✓ Flask-SQLAlchemy imported")
    
    from flask_login import LoginManager
    print("✓ Flask-Login imported")
    
    from flask_bcrypt import Bcrypt
    print("✓ Flask-Bcrypt imported")
    
    from flask_wtf import FlaskForm
    print("✓ Flask-WTF imported")
    
    from flask_babel import Babel
    print("✓ Flask-Babel imported")
    
    from flask_mail import Mail
    print("✓ Flask-Mail imported")
    
    from loguru import logger
    print("✓ loguru imported")
    
    # Test problematic imports
    try:
        from flask_limiter import Limiter
        print("✓ Flask-Limiter imported")
    except ImportError as e:
        print(f"✗ Flask-Limiter import failed: {e}")
    
    try:
        from flask_talisman import Talisman
        print("✓ Flask-Talisman imported")
    except ImportError as e:
        print(f"✗ Flask-Talisman import failed: {e}")
    
    try:
        import bleach
        print("✓ bleach imported")
    except ImportError as e:
        print(f"✗ bleach import failed: {e}")
    
    try:
        import pytesseract
        print("✓ pytesseract imported")
    except ImportError as e:
        print(f"✗ pytesseract import failed: {e}")
    
    try:
        import cv2
        print("✓ opencv-python imported")
    except ImportError as e:
        print(f"✗ opencv-python import failed: {e}")
    
    print("\nTesting Flask app creation...")
    app = Flask(__name__)
    app.config['SECRET_KEY'] = 'test-key'
    print("✓ Flask app created")
    
    print("\nTesting database setup...")
    app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///test.db'
    app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
    db = SQLAlchemy(app)
    print("✓ Database configured")
    
    print("\nAll basic tests passed!")
    print("Now testing the main app import...")
    
    # Try to import the main app
    try:
        import app as main_app
        print("✓ Main app imported successfully")
    except Exception as e:
        print(f"✗ Main app import failed: {e}")
        traceback.print_exc()
    
except Exception as e:
    print(f"Error during testing: {e}")
    traceback.print_exc()

print("Test script completed.")
