#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Simple test version of the app to identify startup issues
"""

import os
import sys
from pathlib import Path
from flask import Flask, render_template, redirect, url_for, flash
from flask_sqlalchemy import SQLAlchemy
from flask_login import LoginManager, UserMixin
from datetime import datetime
import secrets

print("Starting simple app...")

# إعداد المسارات
BASE_DIR = Path(__file__).parent
UPLOAD_FOLDER = BASE_DIR / 'uploads'
UPLOAD_FOLDER.mkdir(exist_ok=True)

# إنشاء التطبيق
app = Flask(__name__)

# إعدادات التطبيق الأساسية
app.config['SECRET_KEY'] = secrets.token_hex(32)
app.config['SQLALCHEMY_DATABASE_URI'] = f'sqlite:///{BASE_DIR}/simple_test.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

print("Flask app created and configured")

# تهيئة الإضافات
db = SQLAlchemy(app)
login_manager = LoginManager()
login_manager.init_app(app)
login_manager.login_view = 'login'

print("Extensions initialized")

# نموذج مستخدم بسيط
class User(UserMixin, db.Model):
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)
    password_hash = db.Column(db.String(128), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

print("User model defined")

@login_manager.user_loader
def load_user(user_id):
    return User.query.get(int(user_id))

# مسارات بسيطة
@app.route('/')
def index():
    return '''
    <html>
    <head>
        <title>نظام إدارة الأرشيف العام - اختبار</title>
        <meta charset="utf-8">
    </head>
    <body>
        <h1>نظام إدارة الأرشيف العام</h1>
        <h2>Archive Management System</h2>
        <p>التطبيق يعمل بنجاح!</p>
        <p>Application is running successfully!</p>
        <hr>
        <p><a href="/login">تسجيل الدخول / Login</a></p>
        <p><a href="/dashboard">لوحة التحكم / Dashboard</a></p>
    </body>
    </html>
    '''

@app.route('/login')
def login():
    return '''
    <html>
    <head>
        <title>تسجيل الدخول</title>
        <meta charset="utf-8">
    </head>
    <body>
        <h1>تسجيل الدخول</h1>
        <p>صفحة تسجيل الدخول قيد التطوير</p>
        <p><a href="/">العودة للصفحة الرئيسية</a></p>
    </body>
    </html>
    '''

@app.route('/dashboard')
def dashboard():
    return '''
    <html>
    <head>
        <title>لوحة التحكم</title>
        <meta charset="utf-8">
    </head>
    <body>
        <h1>لوحة التحكم</h1>
        <p>لوحة التحكم قيد التطوير</p>
        <p><a href="/">العودة للصفحة الرئيسية</a></p>
    </body>
    </html>
    '''

def init_simple_database():
    """تهيئة قاعدة البيانات البسيطة"""
    with app.app_context():
        print("Creating database tables...")
        db.create_all()
        print("Database tables created successfully")

if __name__ == '__main__':
    print("Initializing database...")
    init_simple_database()
    
    print("Starting Flask development server...")
    print("Application will be available at: http://localhost:5000")
    print("Press Ctrl+C to stop the server")
    
    app.run(debug=True, host='127.0.0.1', port=5000, threaded=True)
