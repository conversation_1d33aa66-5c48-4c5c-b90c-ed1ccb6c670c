{% extends "base.html" %}

{% block title %}تعديل الوثيقة: {{ document.title }} - نظام إدارة الأرشيف العام{% endblock %}

{% block extra_css %}
<style>
    .form-container {
        background: white;
        border-radius: var(--border-radius);
        padding: 2rem;
        box-shadow: var(--shadow-light);
        max-width: 800px;
        margin: 0 auto;
    }
    
    .form-header {
        background: var(--gradient-primary);
        color: white;
        padding: 1.5rem;
        margin: -2rem -2rem 2rem -2rem;
        border-radius: var(--border-radius) var(--border-radius) 0 0;
    }
    
    .current-file {
        background: #f8f9fa;
        border-radius: var(--border-radius);
        padding: 1rem;
        margin-bottom: 1rem;
        border: 1px solid #dee2e6;
    }
    
    .current-file-info {
        display: flex;
        align-items: center;
        gap: 1rem;
    }
    
    .file-icon {
        font-size: 2rem;
    }
    
    .file-pdf { color: #e74c3c; }
    .file-doc { color: #2980b9; }
    .file-image { color: #27ae60; }
    .file-txt { color: #f39c12; }
    .file-default { color: #6c757d; }
    
    .replace-file-section {
        border-top: 1px solid #dee2e6;
        padding-top: 1rem;
        margin-top: 1rem;
    }
    
    .form-group {
        margin-bottom: 1.5rem;
    }
    
    .form-label {
        font-weight: 600;
        color: var(--primary-color);
        margin-bottom: 0.5rem;
    }
    
    .form-control, .form-select {
        border: 2px solid #e9ecef;
        border-radius: var(--border-radius);
        padding: 0.75rem;
        transition: all 0.3s ease;
    }
    
    .form-control:focus, .form-select:focus {
        border-color: var(--secondary-color);
        box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
    }
    
    .file-upload-area {
        border: 2px dashed #e9ecef;
        border-radius: var(--border-radius);
        padding: 2rem;
        text-align: center;
        transition: all 0.3s ease;
        cursor: pointer;
        position: relative;
    }
    
    .file-upload-area:hover {
        border-color: var(--secondary-color);
        background: rgba(52, 152, 219, 0.05);
    }
    
    .file-upload-input {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        opacity: 0;
        cursor: pointer;
    }
    
    .file-preview {
        display: none;
        background: #f8f9fa;
        border-radius: var(--border-radius);
        padding: 1rem;
        margin-top: 1rem;
    }
    
    .form-actions {
        display: flex;
        gap: 1rem;
        justify-content: flex-end;
        margin-top: 2rem;
        padding-top: 2rem;
        border-top: 1px solid #e9ecef;
    }
    
    .btn-primary {
        background: var(--gradient-primary);
        border: none;
        padding: 0.75rem 2rem;
        border-radius: var(--border-radius);
        font-weight: 600;
        transition: all 0.3s ease;
    }
    
    .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-medium);
    }
    
    .btn-secondary {
        background: #6c757d;
        border: none;
        padding: 0.75rem 2rem;
        border-radius: var(--border-radius);
        font-weight: 600;
        color: white;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        transition: all 0.3s ease;
    }
    
    .btn-secondary:hover {
        background: #5a6268;
        color: white;
        transform: translateY(-2px);
    }
    
    .required {
        color: #e74c3c;
    }
    
    .form-help {
        font-size: 0.875rem;
        color: #6c757d;
        margin-top: 0.25rem;
    }
    
    .invalid-feedback {
        display: block;
        color: #e74c3c;
        font-size: 0.875rem;
        margin-top: 0.25rem;
    }
    
    .document-info {
        background: rgba(52, 152, 219, 0.1);
        border-radius: var(--border-radius);
        padding: 1rem;
        margin-bottom: 2rem;
    }
    
    .info-row {
        display: flex;
        justify-content: space-between;
        margin-bottom: 0.5rem;
    }
    
    .info-row:last-child {
        margin-bottom: 0;
    }
    
    .info-label {
        font-weight: 600;
        color: #2c3e50;
    }
    
    .info-value {
        color: #34495e;
    }
</style>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">
        <i class="fas fa-edit me-2"></i>
        تعديل الوثيقة
    </h1>
    <div>
        <a href="{{ url_for('view_document', id=document.id) }}" class="btn btn-outline-secondary me-2">
            <i class="fas fa-eye me-2"></i>
            عرض الوثيقة
        </a>
        <a href="{{ url_for('documents') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-right me-2"></i>
            العودة للقائمة
        </a>
    </div>
</div>

<div class="form-container">
    <div class="form-header">
        <h4 class="mb-0">
            <i class="fas fa-file-edit me-2"></i>
            تعديل بيانات الوثيقة
        </h4>
    </div>
    
    <!-- Document Information -->
    <div class="document-info">
        <div class="info-row">
            <span class="info-label">رقم الوثيقة:</span>
            <span class="info-value">#{{ document.id }}</span>
        </div>
        <div class="info-row">
            <span class="info-label">تاريخ الإنشاء:</span>
            <span class="info-value">{{ document.created_at.strftime('%Y/%m/%d %H:%M') }}</span>
        </div>
        <div class="info-row">
            <span class="info-label">آخر تحديث:</span>
            <span class="info-value">{{ document.updated_at.strftime('%Y/%m/%d %H:%M') }}</span>
        </div>
        <div class="info-row">
            <span class="info-label">المنشئ:</span>
            <span class="info-value">{{ document.creator.full_name if document.creator else 'غير محدد' }}</span>
        </div>
    </div>
    
    <form method="POST" enctype="multipart/form-data" id="documentForm">
        {{ form.hidden_tag() }}
        
        <!-- عنوان الوثيقة -->
        <div class="form-group">
            {{ form.title.label(class="form-label") }}
            <span class="required">*</span>
            {{ form.title(class="form-control", placeholder="أدخل عنوان الوثيقة") }}
            {% if form.title.errors %}
                {% for error in form.title.errors %}
                    <div class="invalid-feedback">{{ error }}</div>
                {% endfor %}
            {% endif %}
            <div class="form-help">أدخل عنوان واضح ومفصل للوثيقة</div>
        </div>
        
        <!-- وصف الوثيقة -->
        <div class="form-group">
            {{ form.description.label(class="form-label") }}
            {{ form.description(class="form-control", rows="4", placeholder="أدخل وصف مفصل للوثيقة (اختياري)") }}
            {% if form.description.errors %}
                {% for error in form.description.errors %}
                    <div class="invalid-feedback">{{ error }}</div>
                {% endfor %}
            {% endif %}
            <div class="form-help">وصف مختصر يوضح محتوى الوثيقة وأهميتها</div>
        </div>
        
        <!-- نوع الوثيقة -->
        <div class="form-group">
            {{ form.document_type.label(class="form-label") }}
            <span class="required">*</span>
            {{ form.document_type(class="form-select") }}
            {% if form.document_type.errors %}
                {% for error in form.document_type.errors %}
                    <div class="invalid-feedback">{{ error }}</div>
                {% endfor %}
            {% endif %}
            <div class="form-help">اختر نوع الوثيقة المناسب للتصنيف</div>
        </div>
        
        <!-- الكلمات المفتاحية -->
        <div class="form-group">
            {{ form.tags.label(class="form-label") }}
            {{ form.tags(class="form-control", placeholder="مثال: عقد، مالية، إدارة (افصل بفاصلة)") }}
            {% if form.tags.errors %}
                {% for error in form.tags.errors %}
                    <div class="invalid-feedback">{{ error }}</div>
                {% endfor %}
            {% endif %}
            <div class="form-help">كلمات مفتاحية تساعد في البحث والتصنيف (افصل بفاصلة)</div>
        </div>
        
        <!-- الملف المرفق -->
        <div class="form-group">
            <label class="form-label">الملف المرفق</label>
            
            {% if document.file_name %}
            <div class="current-file">
                <h6>الملف الحالي:</h6>
                <div class="current-file-info">
                    {% set file_ext = document.file_name.split('.')[-1].lower() %}
                    {% if file_ext == 'pdf' %}
                        <i class="fas fa-file-pdf file-icon file-pdf"></i>
                    {% elif file_ext in ['doc', 'docx'] %}
                        <i class="fas fa-file-word file-icon file-doc"></i>
                    {% elif file_ext in ['jpg', 'jpeg', 'png', 'gif'] %}
                        <i class="fas fa-file-image file-icon file-image"></i>
                    {% elif file_ext == 'txt' %}
                        <i class="fas fa-file-alt file-icon file-txt"></i>
                    {% else %}
                        <i class="fas fa-file file-icon file-default"></i>
                    {% endif %}
                    
                    <div>
                        <div><strong>{{ document.file_name }}</strong></div>
                        {% if document.file_size %}
                            <small class="text-muted">الحجم: {{ "%.2f"|format(document.file_size / 1024 / 1024) }} ميجابايت</small>
                        {% endif %}
                    </div>
                </div>
                
                <div class="replace-file-section">
                    <h6>استبدال الملف:</h6>
                    <div class="file-upload-area" id="fileUploadArea">
                        <div class="file-upload-icon">
                            <i class="fas fa-cloud-upload-alt"></i>
                        </div>
                        <div class="file-upload-text">
                            <strong>اسحب الملف الجديد هنا أو انقر للاختيار</strong>
                        </div>
                        <div class="form-help">
                            اتركه فارغاً للاحتفاظ بالملف الحالي
                        </div>
                        {{ form.file(class="file-upload-input", id="fileInput", accept=".pdf,.doc,.docx,.txt,.jpg,.jpeg,.png,.gif") }}
                    </div>
                </div>
            </div>
            {% else %}
            <div class="file-upload-area" id="fileUploadArea">
                <div class="file-upload-icon">
                    <i class="fas fa-cloud-upload-alt"></i>
                </div>
                <div class="file-upload-text">
                    <strong>اسحب الملف هنا أو انقر للاختيار</strong>
                </div>
                <div class="form-help">
                    الملفات المدعومة: PDF, DOC, DOCX, TXT, JPG, PNG (حد أقصى 50 ميجابايت)
                </div>
                {{ form.file(class="file-upload-input", id="fileInput", accept=".pdf,.doc,.docx,.txt,.jpg,.jpeg,.png,.gif") }}
            </div>
            {% endif %}
            
            <div class="file-preview" id="filePreview">
                <div class="file-info">
                    <div class="file-details">
                        <i class="fas fa-file file-icon" id="fileIcon"></i>
                        <div>
                            <div id="fileName"></div>
                            <small class="text-muted" id="fileSize"></small>
                        </div>
                    </div>
                    <button type="button" class="btn-remove-file" onclick="removeFile()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
            
            {% if form.file.errors %}
                {% for error in form.file.errors %}
                    <div class="invalid-feedback">{{ error }}</div>
                {% endfor %}
            {% endif %}
        </div>
        
        <!-- أزرار الإجراءات -->
        <div class="form-actions">
            <a href="{{ url_for('view_document', id=document.id) }}" class="btn-secondary">
                <i class="fas fa-times me-2"></i>
                إلغاء
            </a>
            <button type="submit" class="btn btn-primary">
                <i class="fas fa-save me-2"></i>
                حفظ التغييرات
            </button>
        </div>
    </form>
</div>
{% endblock %}

{% block extra_js %}
<script>
    const fileInput = document.getElementById('fileInput');
    const fileUploadArea = document.getElementById('fileUploadArea');
    const filePreview = document.getElementById('filePreview');
    const fileName = document.getElementById('fileName');
    const fileSize = document.getElementById('fileSize');
    const fileIcon = document.getElementById('fileIcon');
    
    // File upload handling
    fileInput.addEventListener('change', handleFileSelect);
    
    // Drag and drop
    fileUploadArea.addEventListener('dragover', function(e) {
        e.preventDefault();
        fileUploadArea.classList.add('dragover');
    });
    
    fileUploadArea.addEventListener('dragleave', function(e) {
        e.preventDefault();
        fileUploadArea.classList.remove('dragover');
    });
    
    fileUploadArea.addEventListener('drop', function(e) {
        e.preventDefault();
        fileUploadArea.classList.remove('dragover');
        
        const files = e.dataTransfer.files;
        if (files.length > 0) {
            fileInput.files = files;
            handleFileSelect();
        }
    });
    
    function handleFileSelect() {
        const file = fileInput.files[0];
        if (file) {
            // Show file preview
            fileName.textContent = file.name;
            fileSize.textContent = formatFileSize(file.size);
            
            // Set appropriate icon
            const extension = file.name.split('.').pop().toLowerCase();
            let iconClass = 'fas fa-file';
            let iconColor = '#6c757d';
            
            if (extension === 'pdf') {
                iconClass = 'fas fa-file-pdf';
                iconColor = '#e74c3c';
            } else if (['doc', 'docx'].includes(extension)) {
                iconClass = 'fas fa-file-word';
                iconColor = '#2980b9';
            } else if (['jpg', 'jpeg', 'png', 'gif'].includes(extension)) {
                iconClass = 'fas fa-file-image';
                iconColor = '#27ae60';
            } else if (extension === 'txt') {
                iconClass = 'fas fa-file-alt';
                iconColor = '#f39c12';
            }
            
            fileIcon.className = iconClass;
            fileIcon.style.color = iconColor;
            
            filePreview.style.display = 'block';
        }
    }
    
    function removeFile() {
        fileInput.value = '';
        filePreview.style.display = 'none';
    }
    
    function formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
    
    // Form validation
    document.getElementById('documentForm').addEventListener('submit', function(e) {
        const title = document.getElementById('title').value.trim();
        const documentType = document.getElementById('document_type').value;
        
        if (!title) {
            e.preventDefault();
            alert('يرجى إدخال عنوان الوثيقة');
            document.getElementById('title').focus();
            return;
        }
        
        if (!documentType) {
            e.preventDefault();
            alert('يرجى اختيار نوع الوثيقة');
            document.getElementById('document_type').focus();
            return;
        }
        
        // Show loading
        showLoading();
    });
    
    // Auto-resize textarea
    const textarea = document.querySelector('textarea');
    if (textarea) {
        textarea.addEventListener('input', function() {
            this.style.height = 'auto';
            this.style.height = this.scrollHeight + 'px';
        });
    }
</script>
{% endblock %}
