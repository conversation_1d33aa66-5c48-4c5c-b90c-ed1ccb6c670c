{% extends "base.html" %}

{% block title %}إعدادات النظام - نظام إدارة الأرشيف العام{% endblock %}

{% block extra_css %}
<style>
    .settings-header {
        background: linear-gradient(135deg, #34495e 0%, #2c3e50 100%);
        color: white;
        border-radius: var(--border-radius);
        padding: 2rem;
        margin-bottom: 2rem;
        position: relative;
        overflow: hidden;
    }
    
    .settings-header::before {
        content: '';
        position: absolute;
        top: -50%;
        right: -50%;
        width: 200%;
        height: 200%;
        background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
        animation: float 6s ease-in-out infinite;
    }
    
    .settings-content {
        position: relative;
        z-index: 2;
    }
    
    .settings-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 2rem;
        margin-bottom: 3rem;
    }
    
    .settings-category {
        background: white;
        border-radius: var(--border-radius);
        box-shadow: var(--shadow-light);
        overflow: hidden;
        transition: all 0.3s ease;
    }
    
    .settings-category:hover {
        transform: translateY(-5px);
        box-shadow: var(--shadow-medium);
    }
    
    .category-header {
        background: #f8f9fa;
        padding: 1.5rem;
        border-bottom: 1px solid #dee2e6;
    }
    
    .category-title {
        font-size: 1.25rem;
        font-weight: 600;
        color: var(--primary-color);
        margin: 0;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }
    
    .category-description {
        color: #6c757d;
        margin: 0.5rem 0 0 0;
        font-size: 0.9rem;
    }
    
    .category-body {
        padding: 1.5rem;
    }
    
    .setting-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0.75rem 0;
        border-bottom: 1px solid #f8f9fa;
    }
    
    .setting-item:last-child {
        border-bottom: none;
    }
    
    .setting-info {
        flex: 1;
    }
    
    .setting-label {
        font-weight: 600;
        color: #495057;
        margin-bottom: 0.25rem;
    }
    
    .setting-description {
        font-size: 0.875rem;
        color: #6c757d;
        margin: 0;
    }
    
    .setting-value {
        font-weight: 500;
        color: var(--primary-color);
        background: #f8f9fa;
        padding: 0.25rem 0.75rem;
        border-radius: 15px;
        font-size: 0.875rem;
        max-width: 200px;
        text-align: center;
        word-break: break-word;
    }
    
    .quick-actions {
        background: white;
        border-radius: var(--border-radius);
        padding: 2rem;
        box-shadow: var(--shadow-light);
        margin-bottom: 2rem;
    }
    
    .actions-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 1.5rem;
    }
    
    .action-card {
        background: #f8f9fa;
        border-radius: var(--border-radius);
        padding: 1.5rem;
        text-align: center;
        transition: all 0.3s ease;
        border: 2px solid transparent;
    }
    
    .action-card:hover {
        border-color: var(--primary-color);
        background: white;
        transform: translateY(-2px);
    }
    
    .action-icon {
        font-size: 3rem;
        margin-bottom: 1rem;
        color: var(--primary-color);
    }
    
    .action-title {
        font-size: 1.1rem;
        font-weight: 600;
        margin-bottom: 0.5rem;
        color: var(--primary-color);
    }
    
    .action-description {
        color: #6c757d;
        margin-bottom: 1.5rem;
        font-size: 0.9rem;
    }
    
    .btn-action {
        background: linear-gradient(135deg, var(--primary-color) 0%, #2980b9 100%);
        color: white;
        border: none;
        padding: 0.75rem 1.5rem;
        border-radius: var(--border-radius);
        font-weight: 600;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        transition: all 0.3s ease;
    }
    
    .btn-action:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-medium);
        color: white;
    }
    
    .btn-action.danger {
        background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
    }
    
    .btn-action.warning {
        background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
    }
    
    .system-info {
        background: white;
        border-radius: var(--border-radius);
        padding: 2rem;
        box-shadow: var(--shadow-light);
    }
    
    .info-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
    }
    
    .info-item {
        display: flex;
        justify-content: space-between;
        padding: 0.75rem 0;
        border-bottom: 1px solid #f8f9fa;
    }
    
    .info-item:last-child {
        border-bottom: none;
    }
    
    .info-label {
        font-weight: 600;
        color: #6c757d;
    }
    
    .info-value {
        color: var(--primary-color);
        font-weight: 500;
    }
    
    @media (max-width: 768px) {
        .settings-grid {
            grid-template-columns: 1fr;
        }
        
        .actions-grid {
            grid-template-columns: 1fr;
        }
        
        .info-grid {
            grid-template-columns: 1fr;
        }
    }
</style>
{% endblock %}

{% block content %}
<!-- Settings Header -->
<div class="settings-header">
    <div class="settings-content">
        <h1 class="mb-3">
            <i class="fas fa-cog me-3"></i>
            إعدادات النظام
        </h1>
        <p class="mb-0 opacity-75">
            إدارة وتخصيص إعدادات نظام إدارة الأرشيف العام
        </p>
    </div>
</div>

<!-- Quick Actions -->
<div class="quick-actions">
    <h3 class="mb-4">
        <i class="fas fa-bolt me-2"></i>
        إجراءات سريعة
    </h3>
    
    <div class="actions-grid">
        <div class="action-card">
            <div class="action-icon">
                <i class="fas fa-edit"></i>
            </div>
            <h5 class="action-title">تعديل الإعدادات</h5>
            <p class="action-description">تحديث إعدادات النظام العامة والأمان</p>
            <a href="{{ url_for('edit_settings') }}" class="btn-action">
                <i class="fas fa-edit me-2"></i>
                تعديل
            </a>
        </div>
        
        <div class="action-card">
            <div class="action-icon">
                <i class="fas fa-download"></i>
            </div>
            <h5 class="action-title">نسخة احتياطية</h5>
            <p class="action-description">إنشاء نسخة احتياطية من النظام والبيانات</p>
            <a href="{{ url_for('create_backup') }}" class="btn-action warning" 
               onclick="return confirm('هل أنت متأكد من إنشاء نسخة احتياطية؟')">
                <i class="fas fa-download me-2"></i>
                إنشاء نسخة
            </a>
        </div>
        
        <div class="action-card">
            <div class="action-icon">
                <i class="fas fa-chart-bar"></i>
            </div>
            <h5 class="action-title">إحصائيات النظام</h5>
            <p class="action-description">عرض إحصائيات الأداء والاستخدام</p>
            <a href="{{ url_for('statistics') }}" class="btn-action">
                <i class="fas fa-chart-bar me-2"></i>
                عرض الإحصائيات
            </a>
        </div>
    </div>
</div>

<!-- Settings Categories -->
<div class="settings-grid">
    {% for category, category_settings in settings.items() %}
    <div class="settings-category">
        <div class="category-header">
            <h4 class="category-title">
                {% if category == 'general' %}
                <i class="fas fa-cog"></i>
                الإعدادات العامة
                {% elif category == 'security' %}
                <i class="fas fa-shield-alt"></i>
                إعدادات الأمان
                {% elif category == 'archive' %}
                <i class="fas fa-archive"></i>
                إعدادات الأرشفة
                {% elif category == 'backup' %}
                <i class="fas fa-save"></i>
                إعدادات النسخ الاحتياطي
                {% else %}
                <i class="fas fa-cog"></i>
                {{ category }}
                {% endif %}
            </h4>
            <p class="category-description">
                {% if category == 'general' %}
                الإعدادات الأساسية للنظام والمؤسسة
                {% elif category == 'security' %}
                إعدادات الأمان وحماية النظام
                {% elif category == 'archive' %}
                إعدادات إدارة الملفات والأرشفة
                {% elif category == 'backup' %}
                إعدادات النسخ الاحتياطي والاستعادة
                {% endif %}
            </p>
        </div>
        
        <div class="category-body">
            {% for setting in category_settings %}
            <div class="setting-item">
                <div class="setting-info">
                    <div class="setting-label">{{ setting.description or setting.setting_key }}</div>
                    <p class="setting-description">{{ setting.setting_key }}</p>
                </div>
                <div class="setting-value">
                    {% if setting.setting_type == 'boolean' %}
                        {% if setting.setting_value.lower() == 'true' %}
                            <span class="text-success">مفعل</span>
                        {% else %}
                            <span class="text-danger">معطل</span>
                        {% endif %}
                    {% else %}
                        {{ setting.setting_value or 'غير محدد' }}
                    {% endif %}
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
    {% endfor %}
</div>

<!-- System Information -->
<div class="system-info">
    <h3 class="mb-4">
        <i class="fas fa-info-circle me-2"></i>
        معلومات النظام
    </h3>
    
    <div class="info-grid">
        <div class="info-item">
            <span class="info-label">إصدار النظام:</span>
            <span class="info-value">1.0.0</span>
        </div>
        
        <div class="info-item">
            <span class="info-label">تاريخ التثبيت:</span>
            <span class="info-value">2024/01/01</span>
        </div>

        <div class="info-item">
            <span class="info-label">آخر تحديث:</span>
            <span class="info-value">2024/01/01</span>
        </div>
        
        <div class="info-item">
            <span class="info-label">حالة النظام:</span>
            <span class="info-value text-success">يعمل بشكل طبيعي</span>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Animation for settings cards
    document.addEventListener('DOMContentLoaded', function() {
        const settingsCards = document.querySelectorAll('.settings-category');
        
        settingsCards.forEach((card, index) => {
            setTimeout(() => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(20px)';
                card.style.transition = 'all 0.6s ease';
                
                setTimeout(() => {
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, 100);
            }, index * 100);
        });
    });
</script>
{% endblock %}
