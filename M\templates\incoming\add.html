{% extends "base.html" %}

{% block title %}إضافة كتاب وارد جديد - نظام إدارة الأرشيف العام{% endblock %}

{% block extra_css %}
<style>
    .form-container {
        background: white;
        border-radius: var(--border-radius);
        padding: 2rem;
        box-shadow: var(--shadow-light);
        max-width: 800px;
        margin: 0 auto;
    }
    
    .form-header {
        background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
        color: white;
        padding: 1.5rem;
        margin: -2rem -2rem 2rem -2rem;
        border-radius: var(--border-radius) var(--border-radius) 0 0;
    }
    
    .form-group {
        margin-bottom: 1.5rem;
    }
    
    .form-label {
        font-weight: 600;
        color: var(--primary-color);
        margin-bottom: 0.5rem;
    }
    
    .form-control, .form-select {
        border: 2px solid #e9ecef;
        border-radius: var(--border-radius);
        padding: 0.75rem;
        transition: all 0.3s ease;
    }
    
    .form-control:focus, .form-select:focus {
        border-color: #27ae60;
        box-shadow: 0 0 0 0.2rem rgba(39, 174, 96, 0.25);
    }
    
    .form-actions {
        display: flex;
        gap: 1rem;
        justify-content: flex-end;
        margin-top: 2rem;
        padding-top: 2rem;
        border-top: 1px solid #e9ecef;
    }
    
    .btn-primary {
        background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
        border: none;
        padding: 0.75rem 2rem;
        border-radius: var(--border-radius);
        font-weight: 600;
        transition: all 0.3s ease;
    }
    
    .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-medium);
    }
    
    .btn-secondary {
        background: #6c757d;
        border: none;
        padding: 0.75rem 2rem;
        border-radius: var(--border-radius);
        font-weight: 600;
        color: white;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        transition: all 0.3s ease;
    }
    
    .btn-secondary:hover {
        background: #5a6268;
        color: white;
        transform: translateY(-2px);
    }
    
    .required {
        color: #e74c3c;
    }
    
    .form-help {
        font-size: 0.875rem;
        color: #6c757d;
        margin-top: 0.25rem;
    }
    
    .invalid-feedback {
        display: block;
        color: #e74c3c;
        font-size: 0.875rem;
        margin-top: 0.25rem;
    }
    
    .auto-number {
        background: rgba(39, 174, 96, 0.1);
        border-radius: var(--border-radius);
        padding: 1rem;
        margin-bottom: 1rem;
        border: 1px solid rgba(39, 174, 96, 0.2);
    }
    
    .auto-number-text {
        color: #27ae60;
        font-weight: 600;
        margin-bottom: 0.5rem;
    }
    
    .auto-number-value {
        font-family: 'Courier New', monospace;
        font-size: 1.1rem;
        font-weight: 700;
        color: #2c3e50;
    }
    
    .form-row {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 1rem;
    }
    
    .file-upload-area {
        border: 2px dashed #e9ecef;
        border-radius: var(--border-radius);
        padding: 2rem;
        text-align: center;
        transition: all 0.3s ease;
        cursor: pointer;
        position: relative;
    }

    .file-upload-area:hover {
        border-color: #27ae60;
        background: rgba(39, 174, 96, 0.05);
    }

    .file-upload-area.dragover {
        border-color: #27ae60;
        background: rgba(39, 174, 96, 0.1);
    }

    .file-upload-input {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        opacity: 0;
        cursor: pointer;
    }

    .file-upload-icon {
        font-size: 3rem;
        color: #27ae60;
        margin-bottom: 1rem;
    }

    .file-preview {
        background: #f8f9fa;
        border-radius: var(--border-radius);
        padding: 1rem;
        margin-top: 1rem;
        border: 1px solid #dee2e6;
    }

    .file-info {
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .file-details {
        display: flex;
        align-items: center;
        gap: 1rem;
    }

    .file-icon {
        font-size: 2rem;
    }

    .btn-remove-file {
        background: #e74c3c;
        color: white;
        border: none;
        border-radius: 50%;
        width: 30px;
        height: 30px;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .btn-remove-file:hover {
        background: #c0392b;
    }

    @media (max-width: 768px) {
        .form-row {
            grid-template-columns: 1fr;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">
        <i class="fas fa-plus-circle me-2"></i>
        إضافة كتاب وارد جديد
    </h1>
    <a href="{{ url_for('incoming_documents') }}" class="btn btn-outline-secondary">
        <i class="fas fa-arrow-right me-2"></i>
        العودة للقائمة
    </a>
</div>

<div class="form-container">
    <div class="form-header">
        <h4 class="mb-0">
            <i class="fas fa-inbox me-2"></i>
            بيانات الكتاب الوارد الجديد
        </h4>
    </div>
    
    <!-- Auto Number Display -->
    <div class="auto-number">
        <div class="auto-number-text">
            <i class="fas fa-hashtag me-2"></i>
            رقم الوارد التلقائي المقترح:
        </div>
        <div class="auto-number-value" id="autoNumber">
            {{ generate_auto_number('IN') }}
        </div>
        <small class="text-muted">يمكنك تعديل الرقم أو تركه فارغاً لاستخدام الرقم التلقائي</small>
    </div>
    
    <form method="POST" id="incomingForm" enctype="multipart/form-data">
        {{ form.hidden_tag() }}
        
        <div class="form-row">
            <!-- رقم الوارد -->
            <div class="form-group">
                {{ form.incoming_number.label(class="form-label") }}
                {{ form.incoming_number(class="form-control", placeholder="اتركه فارغاً للترقيم التلقائي") }}
                {% if form.incoming_number.errors %}
                    {% for error in form.incoming_number.errors %}
                        <div class="invalid-feedback">{{ error }}</div>
                    {% endfor %}
                {% endif %}
                <div class="form-help">رقم فريد للكتاب الوارد (سيتم توليده تلقائياً إذا ترك فارغاً)</div>
            </div>
            
            <!-- تاريخ الاستلام -->
            <div class="form-group">
                {{ form.received_date.label(class="form-label") }}
                <span class="required">*</span>
                {{ form.received_date(class="form-control", type="date", value=get_current_date()) }}
                {% if form.received_date.errors %}
                    {% for error in form.received_date.errors %}
                        <div class="invalid-feedback">{{ error }}</div>
                    {% endfor %}
                {% endif %}
                <div class="form-help">تاريخ استلام الكتاب</div>
            </div>
        </div>
        
        <!-- الجهة المرسلة -->
        <div class="form-group">
            {{ form.sender_name.label(class="form-label") }}
            <span class="required">*</span>
            {{ form.sender_name(class="form-control", placeholder="أدخل اسم الجهة أو الشخص المرسل") }}
            {% if form.sender_name.errors %}
                {% for error in form.sender_name.errors %}
                    <div class="invalid-feedback">{{ error }}</div>
                {% endfor %}
            {% endif %}
            <div class="form-help">اسم الجهة أو الشخص الذي أرسل الكتاب</div>
        </div>
        
        <!-- موضوع الكتاب -->
        <div class="form-group">
            {{ form.subject.label(class="form-label") }}
            <span class="required">*</span>
            {{ form.subject(class="form-control", placeholder="أدخل موضوع الكتاب") }}
            {% if form.subject.errors %}
                {% for error in form.subject.errors %}
                    <div class="invalid-feedback">{{ error }}</div>
                {% endfor %}
            {% endif %}
            <div class="form-help">موضوع أو عنوان الكتاب الوارد</div>
        </div>
        
        <div class="form-row">
            <!-- الأولوية -->
            <div class="form-group">
                {{ form.priority.label(class="form-label") }}
                <span class="required">*</span>
                {{ form.priority(class="form-select") }}
                {% if form.priority.errors %}
                    {% for error in form.priority.errors %}
                        <div class="invalid-feedback">{{ error }}</div>
                    {% endfor %}
                {% endif %}
                <div class="form-help">مستوى أولوية الكتاب</div>
            </div>
            
            <!-- الحالة -->
            <div class="form-group">
                <label class="form-label">الحالة</label>
                <select class="form-select" name="status" id="status">
                    <option value="جديد" selected>جديد</option>
                    <option value="قيد المراجعة">قيد المراجعة</option>
                    <option value="مكتمل">مكتمل</option>
                    <option value="مؤرشف">مؤرشف</option>
                </select>
                <div class="form-help">حالة معالجة الكتاب</div>
            </div>
        </div>
        
        <!-- ملاحظات -->
        <div class="form-group">
            {{ form.notes.label(class="form-label") }}
            {{ form.notes(class="form-control", rows="4", placeholder="أدخل أي ملاحظات إضافية (اختياري)") }}
            {% if form.notes.errors %}
                {% for error in form.notes.errors %}
                    <div class="invalid-feedback">{{ error }}</div>
                {% endfor %}
            {% endif %}
            <div class="form-help">ملاحظات أو تفاصيل إضافية حول الكتاب</div>
        </div>

        <!-- الملف المرفق -->
        <div class="form-group">
            {{ form.file.label(class="form-label") }}
            <div class="file-upload-area" id="fileUploadArea">
                <div class="file-upload-icon">
                    <i class="fas fa-cloud-upload-alt"></i>
                </div>
                <div class="file-upload-text">
                    <strong>اسحب الملف هنا أو انقر للاختيار</strong>
                </div>
                <div class="form-help">
                    الملفات المدعومة: PDF, DOC, DOCX, JPG, PNG, GIF (حد أقصى 50 ميجابايت)
                </div>
                {{ form.file(class="file-upload-input", id="fileInput", accept=".pdf,.doc,.docx,.jpg,.jpeg,.png,.gif") }}
            </div>

            <div class="file-preview" id="filePreview" style="display: none;">
                <div class="file-info">
                    <div class="file-details">
                        <i class="fas fa-file file-icon" id="fileIcon"></i>
                        <div>
                            <div id="fileName"></div>
                            <small class="text-muted" id="fileSize"></small>
                        </div>
                    </div>
                    <button type="button" class="btn-remove-file" onclick="removeFile()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>

            {% if form.file.errors %}
                {% for error in form.file.errors %}
                    <div class="invalid-feedback">{{ error }}</div>
                {% endfor %}
            {% endif %}
        </div>
        
        <!-- أزرار الإجراءات -->
        <div class="form-actions">
            <a href="{{ url_for('incoming_documents') }}" class="btn-secondary">
                <i class="fas fa-times me-2"></i>
                إلغاء
            </a>
            <button type="submit" class="btn btn-primary">
                <i class="fas fa-save me-2"></i>
                حفظ الكتاب الوارد
            </button>
        </div>
    </form>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Form validation
    document.getElementById('incomingForm').addEventListener('submit', function(e) {
        const senderName = document.getElementById('sender_name').value.trim();
        const subject = document.getElementById('subject').value.trim();
        const receivedDate = document.getElementById('received_date').value;
        const priority = document.getElementById('priority').value;
        
        if (!senderName) {
            e.preventDefault();
            alert('يرجى إدخال اسم الجهة المرسلة');
            document.getElementById('sender_name').focus();
            return;
        }
        
        if (!subject) {
            e.preventDefault();
            alert('يرجى إدخال موضوع الكتاب');
            document.getElementById('subject').focus();
            return;
        }
        
        if (!receivedDate) {
            e.preventDefault();
            alert('يرجى تحديد تاريخ الاستلام');
            document.getElementById('received_date').focus();
            return;
        }
        
        if (!priority) {
            e.preventDefault();
            alert('يرجى اختيار أولوية الكتاب');
            document.getElementById('priority').focus();
            return;
        }
        
        // Show loading
        showLoading();
    });
    
    // Auto-resize textarea
    const textarea = document.querySelector('textarea');
    if (textarea) {
        textarea.addEventListener('input', function() {
            this.style.height = 'auto';
            this.style.height = this.scrollHeight + 'px';
        });
    }
    
    // Update auto number display
    function updateAutoNumber() {
        const today = new Date();
        const year = today.getFullYear();
        const dayOfYear = Math.floor((today - new Date(year, 0, 0)) / 1000 / 60 / 60 / 24);
        const autoNumber = `IN-${year}-${dayOfYear.toString().padStart(4, '0')}`;
        document.getElementById('autoNumber').textContent = autoNumber;
    }
    
    // Priority color coding
    document.getElementById('priority').addEventListener('change', function() {
        const priority = this.value;
        this.className = 'form-select';
        
        if (priority === 'عاجل') {
            this.style.borderColor = '#e74c3c';
            this.style.color = '#e74c3c';
        } else if (priority === 'مهم') {
            this.style.borderColor = '#f1c40f';
            this.style.color = '#f1c40f';
        } else {
            this.style.borderColor = '#e9ecef';
            this.style.color = '#495057';
        }
    });
    
    // Status color coding
    document.getElementById('status').addEventListener('change', function() {
        const status = this.value;
        this.className = 'form-select';
        
        if (status === 'جديد') {
            this.style.borderColor = '#3498db';
            this.style.color = '#3498db';
        } else if (status === 'قيد المراجعة') {
            this.style.borderColor = '#f1c40f';
            this.style.color = '#f1c40f';
        } else if (status === 'مكتمل') {
            this.style.borderColor = '#27ae60';
            this.style.color = '#27ae60';
        } else {
            this.style.borderColor = '#95a5a6';
            this.style.color = '#95a5a6';
        }
    });
    
    // Initialize colors
    document.getElementById('priority').dispatchEvent(new Event('change'));
    document.getElementById('status').dispatchEvent(new Event('change'));

    // File upload handling
    const fileInput = document.getElementById('fileInput');
    const fileUploadArea = document.getElementById('fileUploadArea');
    const filePreview = document.getElementById('filePreview');
    const fileName = document.getElementById('fileName');
    const fileSize = document.getElementById('fileSize');
    const fileIcon = document.getElementById('fileIcon');

    fileInput.addEventListener('change', handleFileSelect);

    // Drag and drop
    fileUploadArea.addEventListener('dragover', function(e) {
        e.preventDefault();
        fileUploadArea.classList.add('dragover');
    });

    fileUploadArea.addEventListener('dragleave', function(e) {
        e.preventDefault();
        fileUploadArea.classList.remove('dragover');
    });

    fileUploadArea.addEventListener('drop', function(e) {
        e.preventDefault();
        fileUploadArea.classList.remove('dragover');

        const files = e.dataTransfer.files;
        if (files.length > 0) {
            fileInput.files = files;
            handleFileSelect();
        }
    });

    function handleFileSelect() {
        const file = fileInput.files[0];
        if (file) {
            // Check file size (50MB limit)
            if (file.size > 50 * 1024 * 1024) {
                alert('حجم الملف كبير جداً. الحد الأقصى 50 ميجابايت');
                fileInput.value = '';
                return;
            }

            // Show file preview
            fileName.textContent = file.name;
            fileSize.textContent = formatFileSize(file.size);

            // Set appropriate icon
            const extension = file.name.split('.').pop().toLowerCase();
            let iconClass = 'fas fa-file';
            let iconColor = '#6c757d';

            if (extension === 'pdf') {
                iconClass = 'fas fa-file-pdf';
                iconColor = '#e74c3c';
            } else if (['doc', 'docx'].includes(extension)) {
                iconClass = 'fas fa-file-word';
                iconColor = '#2980b9';
            } else if (['jpg', 'jpeg', 'png', 'gif'].includes(extension)) {
                iconClass = 'fas fa-file-image';
                iconColor = '#27ae60';
            }

            fileIcon.className = iconClass;
            fileIcon.style.color = iconColor;

            filePreview.style.display = 'block';
        }
    }

    function removeFile() {
        fileInput.value = '';
        filePreview.style.display = 'none';
    }

    function formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
</script>
{% endblock %}
