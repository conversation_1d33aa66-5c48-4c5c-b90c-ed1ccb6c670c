{% extends "base.html" %}

{% block title %}نسيت كلمة المرور - نظام إدارة الأرشيف العام{% endblock %}

{% block extra_css %}
<style>
    .auth-container {
        min-height: 100vh;
        display: flex;
        align-items: center;
        justify-content: center;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        padding: 2rem 0;
    }
    
    .auth-card {
        background: white;
        border-radius: 20px;
        box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        padding: 3rem;
        width: 100%;
        max-width: 450px;
        position: relative;
        overflow: hidden;
    }
    
    .auth-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 5px;
        background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
    }
    
    .auth-header {
        text-align: center;
        margin-bottom: 2rem;
    }
    
    .auth-logo {
        width: 80px;
        height: 80px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 1rem;
        color: white;
        font-size: 2rem;
    }
    
    .auth-title {
        color: #2c3e50;
        font-size: 1.8rem;
        font-weight: 700;
        margin-bottom: 0.5rem;
    }
    
    .auth-subtitle {
        color: #7f8c8d;
        font-size: 1rem;
        margin: 0;
    }
    
    .form-group {
        margin-bottom: 1.5rem;
    }
    
    .form-label {
        display: block;
        margin-bottom: 0.5rem;
        color: #2c3e50;
        font-weight: 600;
    }
    
    .form-control {
        width: 100%;
        padding: 1rem;
        border: 2px solid #e9ecef;
        border-radius: 10px;
        font-size: 1rem;
        transition: all 0.3s ease;
        background: #f8f9fa;
    }
    
    .form-control:focus {
        outline: none;
        border-color: #667eea;
        background: white;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    }
    
    .btn-primary {
        width: 100%;
        padding: 1rem;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        border-radius: 10px;
        color: white;
        font-size: 1.1rem;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        margin-bottom: 1rem;
    }
    
    .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
    }
    
    .auth-links {
        text-align: center;
        margin-top: 2rem;
    }
    
    .auth-link {
        color: #667eea;
        text-decoration: none;
        font-weight: 600;
        transition: color 0.3s ease;
    }
    
    .auth-link:hover {
        color: #764ba2;
    }
    
    .invalid-feedback {
        color: #e74c3c;
        font-size: 0.875rem;
        margin-top: 0.25rem;
        display: block;
    }
    
    .info-box {
        background: #e8f4fd;
        border: 1px solid #bee5eb;
        border-radius: 10px;
        padding: 1rem;
        margin-bottom: 1.5rem;
        color: #0c5460;
    }
    
    .info-box i {
        margin-left: 0.5rem;
        color: #17a2b8;
    }
    
    @media (max-width: 768px) {
        .auth-card {
            margin: 1rem;
            padding: 2rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="auth-container">
    <div class="auth-card">
        <div class="auth-header">
            <div class="auth-logo">
                <i class="fas fa-key"></i>
            </div>
            <h1 class="auth-title">نسيت كلمة المرور</h1>
            <p class="auth-subtitle">أدخل بريدك الإلكتروني لإعادة تعيين كلمة المرور</p>
        </div>
        
        <div class="info-box">
            <i class="fas fa-info-circle"></i>
            سنرسل لك رابط إعادة تعيين كلمة المرور على بريدك الإلكتروني
        </div>
        
        <form method="POST">
            {{ form.hidden_tag() }}
            
            <div class="form-group">
                {{ form.email.label(class="form-label") }}
                {{ form.email(class="form-control", placeholder="أدخل بريدك الإلكتروني") }}
                {% if form.email.errors %}
                    {% for error in form.email.errors %}
                        <div class="invalid-feedback">{{ error }}</div>
                    {% endfor %}
                {% endif %}
            </div>
            
            {{ form.submit(class="btn btn-primary") }}
        </form>
        
        <div class="auth-links">
            <a href="{{ url_for('login') }}" class="auth-link">
                <i class="fas fa-arrow-right me-2"></i>
                العودة لتسجيل الدخول
            </a>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // تحسين تجربة المستخدم
    document.addEventListener('DOMContentLoaded', function() {
        const emailInput = document.querySelector('input[type="email"]');
        
        if (emailInput) {
            emailInput.addEventListener('input', function() {
                if (this.value && this.checkValidity()) {
                    this.style.borderColor = '#27ae60';
                } else if (this.value) {
                    this.style.borderColor = '#e74c3c';
                } else {
                    this.style.borderColor = '#e9ecef';
                }
            });
        }
        
        // تأثير الرسوم المتحركة
        const authCard = document.querySelector('.auth-card');
        authCard.style.opacity = '0';
        authCard.style.transform = 'translateY(20px)';
        
        setTimeout(() => {
            authCard.style.transition = 'all 0.6s ease';
            authCard.style.opacity = '1';
            authCard.style.transform = 'translateY(0)';
        }, 100);
    });
</script>
{% endblock %}
