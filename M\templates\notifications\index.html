{% extends "base.html" %}

{% block title %}الإشعارات - نظام إدارة الأرشيف العام{% endblock %}

{% block extra_css %}
<style>
    body {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        font-family: 'Cairo', sans-serif;
    }

    .notifications-container {
        max-width: 1200px;
        margin: 2rem auto;
        padding: 0 1rem;
    }

    .notifications-header {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        border-radius: 20px;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        text-align: center;
    }

    .notifications-header h1 {
        color: #2c3e50;
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 0.5rem;
    }

    .notifications-header p {
        color: #6c757d;
        font-size: 1.1rem;
        margin: 0;
    }

    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1.5rem;
        margin-bottom: 2rem;
    }

    .stat-card {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        border-radius: 15px;
        padding: 1.5rem;
        box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        transition: transform 0.3s ease;
        text-align: center;
    }

    .stat-card:hover {
        transform: translateY(-5px);
    }

    .stat-card .icon {
        font-size: 2.5rem;
        margin-bottom: 1rem;
        display: block;
    }

    .stat-card .number {
        font-size: 2rem;
        font-weight: 700;
        color: #2c3e50;
        margin-bottom: 0.5rem;
    }

    .stat-card .label {
        color: #6c757d;
        font-size: 0.9rem;
        margin: 0;
    }

    .notifications-content {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 2rem;
    }

    .notifications-section {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        border-radius: 20px;
        padding: 2rem;
        box-shadow: 0 20px 40px rgba(0,0,0,0.1);
    }

    .section-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1.5rem;
        padding-bottom: 1rem;
        border-bottom: 2px solid #f8f9fa;
    }

    .section-title {
        color: #2c3e50;
        font-size: 1.3rem;
        font-weight: 600;
        margin: 0;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .section-count {
        background: #667eea;
        color: white;
        padding: 0.25rem 0.75rem;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: 600;
    }

    .notifications-list {
        max-height: 500px;
        overflow-y: auto;
    }

    .notification-item {
        display: flex;
        align-items: flex-start;
        gap: 1rem;
        padding: 1rem;
        border-radius: 10px;
        margin-bottom: 0.75rem;
        transition: all 0.3s ease;
        border-left: 4px solid transparent;
        cursor: pointer;
    }

    .notification-item:hover {
        background: #f8f9fa;
        transform: translateX(5px);
    }

    .notification-item.unread {
        background: rgba(102, 126, 234, 0.05);
        border-left-color: #667eea;
    }

    .notification-icon {
        width: 40px;
        height: 40px;
        border-radius: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1rem;
        flex-shrink: 0;
    }

    .notification-content {
        flex: 1;
        min-width: 0;
    }

    .notification-title {
        font-weight: 600;
        color: #2c3e50;
        margin-bottom: 0.25rem;
        font-size: 0.95rem;
    }

    .notification-message {
        color: #6c757d;
        font-size: 0.85rem;
        line-height: 1.4;
        margin-bottom: 0.5rem;
    }

    .notification-time {
        color: #adb5bd;
        font-size: 0.75rem;
    }

    .notification-actions {
        display: flex;
        gap: 0.5rem;
        margin-top: 0.5rem;
    }

    .action-btn {
        padding: 0.25rem 0.75rem;
        border: none;
        border-radius: 5px;
        font-size: 0.75rem;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        text-decoration: none;
    }

    .btn-mark-read {
        background: #28a745;
        color: white;
    }

    .btn-view {
        background: #007bff;
        color: white;
    }

    .btn-dismiss {
        background: #6c757d;
        color: white;
    }

    .action-btn:hover {
        transform: translateY(-1px);
        opacity: 0.9;
        color: white;
        text-decoration: none;
    }

    /* Notification type colors */
    .icon-activity { background: #28a745; }
    .icon-summary { background: #17a2b8; }
    .icon-system_health { background: #dc3545; }
    .icon-storage { background: #ffc107; color: #212529; }
    .icon-expiration { background: #fd7e14; }
    .icon-deadline { background: #6f42c1; }

    .empty-state {
        text-align: center;
        padding: 3rem 1rem;
        color: #6c757d;
    }

    .empty-state i {
        font-size: 4rem;
        margin-bottom: 1rem;
        opacity: 0.5;
    }

    .empty-state h3 {
        margin-bottom: 0.5rem;
        color: #495057;
    }

    .empty-state p {
        margin: 0;
        font-size: 0.9rem;
    }

    .filter-tabs {
        display: flex;
        gap: 0.5rem;
        margin-bottom: 1rem;
        flex-wrap: wrap;
    }

    .filter-tab {
        padding: 0.5rem 1rem;
        border: 2px solid #e9ecef;
        border-radius: 20px;
        background: white;
        color: #6c757d;
        font-size: 0.8rem;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .filter-tab.active {
        border-color: #667eea;
        background: #667eea;
        color: white;
    }

    .filter-tab:hover {
        border-color: #667eea;
        color: #667eea;
    }

    .filter-tab.active:hover {
        color: white;
    }

    @media (max-width: 768px) {
        .notifications-container {
            margin: 1rem;
            padding: 0;
        }

        .notifications-content {
            grid-template-columns: 1fr;
        }

        .stats-grid {
            grid-template-columns: repeat(2, 1fr);
        }

        .section-header {
            flex-direction: column;
            gap: 0.5rem;
            text-align: center;
        }

        .filter-tabs {
            justify-content: center;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="notifications-container">
    <!-- Header -->
    <div class="notifications-header">
        <h1>
            <i class="fas fa-bell me-3"></i>
            مركز الإشعارات
        </h1>
        <p>تابع جميع الإشعارات والتنبيهات المهمة في النظام</p>
    </div>

    <!-- Statistics -->
    <div class="stats-grid">
        <div class="stat-card">
            <i class="fas fa-bell icon" style="color: #667eea;"></i>
            <div class="number">{{ notification_stats.total_sent or 0 }}</div>
            <div class="label">إجمالي الإشعارات</div>
        </div>
        <div class="stat-card">
            <i class="fas fa-calendar-week icon" style="color: #28a745;"></i>
            <div class="number">{{ notification_stats.this_week or 0 }}</div>
            <div class="label">هذا الأسبوع</div>
        </div>
        <div class="stat-card">
            <i class="fas fa-envelope-open icon" style="color: #ffc107;"></i>
            <div class="number">{{ notification_stats.unread_count or 0 }}</div>
            <div class="label">غير مقروءة</div>
        </div>
        <div class="stat-card">
            <i class="fas fa-check-circle icon" style="color: #17a2b8;"></i>
            <div class="number">{{ notification_stats.delivery_rate or 0 }}%</div>
            <div class="label">معدل التسليم</div>
        </div>
    </div>

    <!-- Notifications Content -->
    <div class="notifications-content">
        <!-- User Notifications -->
        <div class="notifications-section">
            <div class="section-header">
                <h2 class="section-title">
                    <i class="fas fa-user"></i>
                    إشعاراتي الشخصية
                </h2>
                <span class="section-count">{{ user_notifications|length }}</span>
            </div>

            <div class="filter-tabs">
                <div class="filter-tab active" onclick="filterNotifications('all', this)">الكل</div>
                <div class="filter-tab" onclick="filterNotifications('unread', this)">غير مقروءة</div>
                <div class="filter-tab" onclick="filterNotifications('activity', this)">الأنشطة</div>
                <div class="filter-tab" onclick="filterNotifications('summary', this)">الملخصات</div>
            </div>

            <div class="notifications-list" id="userNotifications">
                {% if user_notifications %}
                    {% for notification in user_notifications %}
                    <div class="notification-item {{ 'unread' if not notification.read else '' }}" 
                         data-type="{{ notification.type }}" 
                         data-read="{{ notification.read|lower }}">
                        <div class="notification-icon icon-{{ notification.type }}">
                            {% if notification.type == 'activity' %}
                                <i class="fas fa-history"></i>
                            {% elif notification.type == 'summary' %}
                                <i class="fas fa-chart-bar"></i>
                            {% else %}
                                <i class="fas fa-info-circle"></i>
                            {% endif %}
                        </div>
                        <div class="notification-content">
                            <div class="notification-title">{{ notification.title }}</div>
                            <div class="notification-message">{{ notification.message }}</div>
                            <div class="notification-time">
                                {{ notification.created_at.strftime('%Y-%m-%d %H:%M') if notification.created_at else 'الآن' }}
                            </div>
                            {% if not notification.read %}
                            <div class="notification-actions">
                                <button class="action-btn btn-mark-read" 
                                        onclick="markAsRead('{{ notification.id }}', this)">
                                    <i class="fas fa-check"></i> تحديد كمقروء
                                </button>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                    {% endfor %}
                {% else %}
                    <div class="empty-state">
                        <i class="fas fa-bell-slash"></i>
                        <h3>لا توجد إشعارات شخصية</h3>
                        <p>ستظهر إشعاراتك الشخصية هنا عند توفرها</p>
                    </div>
                {% endif %}
            </div>
        </div>

        <!-- System Alerts -->
        <div class="notifications-section">
            <div class="section-header">
                <h2 class="section-title">
                    <i class="fas fa-exclamation-triangle"></i>
                    تنبيهات النظام
                </h2>
                <span class="section-count">{{ system_alerts|length }}</span>
            </div>

            <div class="filter-tabs">
                <div class="filter-tab active" onclick="filterAlerts('all', this)">الكل</div>
                <div class="filter-tab" onclick="filterAlerts('critical', this)">حرجة</div>
                <div class="filter-tab" onclick="filterAlerts('high', this)">عالية</div>
                <div class="filter-tab" onclick="filterAlerts('medium', this)">متوسطة</div>
            </div>

            <div class="notifications-list" id="systemAlerts">
                {% if system_alerts %}
                    {% for alert in system_alerts %}
                    <div class="notification-item" 
                         data-priority="{{ alert.priority }}" 
                         data-type="{{ alert.type }}">
                        <div class="notification-icon icon-{{ alert.type }}">
                            {% if alert.type == 'system_health' %}
                                <i class="fas fa-heartbeat"></i>
                            {% elif alert.type == 'storage' %}
                                <i class="fas fa-hdd"></i>
                            {% elif alert.type == 'expiration' %}
                                <i class="fas fa-clock"></i>
                            {% else %}
                                <i class="fas fa-exclamation"></i>
                            {% endif %}
                        </div>
                        <div class="notification-content">
                            <div class="notification-title">{{ alert.title }}</div>
                            <div class="notification-message">{{ alert.message }}</div>
                            <div class="notification-time">
                                {{ alert.created_at.strftime('%Y-%m-%d %H:%M') if alert.created_at else 'الآن' }}
                            </div>
                            <div class="notification-actions">
                                <button class="action-btn btn-view" onclick="viewAlert('{{ alert.id }}')">
                                    <i class="fas fa-eye"></i> عرض التفاصيل
                                </button>
                                <button class="action-btn btn-dismiss" onclick="dismissAlert('{{ alert.id }}', this)">
                                    <i class="fas fa-times"></i> إخفاء
                                </button>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                {% else %}
                    <div class="empty-state">
                        <i class="fas fa-shield-alt"></i>
                        <h3>لا توجد تنبيهات نظام</h3>
                        <p>النظام يعمل بشكل طبيعي</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 NOTIFICATIONS: Notifications page loaded');
    
    // Auto-refresh notifications every 30 seconds
    setInterval(function() {
        updateNotificationCounts();
    }, 30000);
    
    console.log('✅ NOTIFICATIONS: Auto-refresh initialized');
});

function filterNotifications(type, element) {
    // Update active tab
    document.querySelectorAll('#userNotifications').forEach(container => {
        container.parentElement.querySelectorAll('.filter-tab').forEach(tab => {
            tab.classList.remove('active');
        });
    });
    element.classList.add('active');
    
    // Filter notifications
    const notifications = document.querySelectorAll('#userNotifications .notification-item');
    notifications.forEach(notification => {
        const notificationType = notification.dataset.type;
        const isRead = notification.dataset.read === 'true';
        
        let show = false;
        if (type === 'all') {
            show = true;
        } else if (type === 'unread') {
            show = !isRead;
        } else {
            show = notificationType === type;
        }
        
        notification.style.display = show ? 'flex' : 'none';
    });
    
    console.log(`🔍 NOTIFICATIONS: Filtered by ${type}`);
}

function filterAlerts(priority, element) {
    // Update active tab
    document.querySelectorAll('#systemAlerts').forEach(container => {
        container.parentElement.querySelectorAll('.filter-tab').forEach(tab => {
            tab.classList.remove('active');
        });
    });
    element.classList.add('active');
    
    // Filter alerts
    const alerts = document.querySelectorAll('#systemAlerts .notification-item');
    alerts.forEach(alert => {
        const alertPriority = alert.dataset.priority;
        const show = priority === 'all' || alertPriority === priority;
        alert.style.display = show ? 'flex' : 'none';
    });
    
    console.log(`🔍 NOTIFICATIONS: Filtered alerts by ${priority}`);
}

function markAsRead(notificationId, button) {
    fetch(`/api/notifications/mark-read/${notificationId}`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Remove unread class and hide button
            const notificationItem = button.closest('.notification-item');
            notificationItem.classList.remove('unread');
            notificationItem.dataset.read = 'true';
            button.parentElement.style.display = 'none';
            
            console.log(`✅ NOTIFICATIONS: Marked ${notificationId} as read`);
        } else {
            alert('حدث خطأ في تحديد الإشعار كمقروء');
        }
    })
    .catch(error => {
        console.error('❌ NOTIFICATIONS: Error marking as read:', error);
        alert('حدث خطأ في الاتصال');
    });
}

function viewAlert(alertId) {
    // This would typically open a modal or navigate to details page
    console.log(`👁️ NOTIFICATIONS: Viewing alert ${alertId}`);
    alert(`عرض تفاصيل التنبيه: ${alertId}`);
}

function dismissAlert(alertId, button) {
    if (confirm('هل أنت متأكد من إخفاء هذا التنبيه؟')) {
        const alertItem = button.closest('.notification-item');
        alertItem.style.opacity = '0.5';
        alertItem.style.pointerEvents = 'none';
        
        setTimeout(() => {
            alertItem.style.display = 'none';
        }, 300);
        
        console.log(`🗑️ NOTIFICATIONS: Dismissed alert ${alertId}`);
    }
}

function updateNotificationCounts() {
    fetch('/api/notifications/unread-count')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Update unread count in stats
                const unreadStat = document.querySelector('.stat-card .number');
                if (unreadStat && unreadStat.parentElement.querySelector('.label').textContent.includes('غير مقروءة')) {
                    unreadStat.textContent = data.count;
                }
                
                console.log(`🔄 NOTIFICATIONS: Updated unread count: ${data.count}`);
            }
        })
        .catch(error => {
            console.error('❌ NOTIFICATIONS: Error updating counts:', error);
        });
}
</script>
{% endblock %}
