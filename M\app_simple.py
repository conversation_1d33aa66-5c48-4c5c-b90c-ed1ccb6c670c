#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
نظام إدارة الأرشيف العام - نسخة مبسطة للتشغيل العادي
Archive Management System - Simplified Version
"""

import os
from flask import Flask, render_template, request, redirect, url_for, flash, jsonify, send_file, make_response, session
from flask_sqlalchemy import SQLAlchemy
from flask_login import LoginManager, UserMixin, login_user, logout_user, login_required, current_user
from flask_bcrypt import Bcrypt
from flask_wtf import FlaskForm
from wtforms import StringField, PasswordField, SelectField, TextAreaField, FileField, DateField, SubmitField
from wtforms.validators import DataRequired, Email, Length, ValidationError
from flask_wtf.file import FileAllowed
from werkzeug.utils import secure_filename
from flask_babel import Babel, gettext, ngettext, lazy_gettext, get_locale
from flask_mail import Mail, Message
from werkzeug.security import generate_password_hash, check_password_hash
from datetime import datetime, timedelta
import secrets
from pathlib import Path
from loguru import logger
import json
from io import BytesIO
import base64
from sqlalchemy import func, extract
import os
import zipfile
import shutil
import qrcode
from barcode import Code128
from barcode.writer import ImageWriter
import time
from PIL import Image, ImageDraw, ImageFont
import io

# إعداد المسارات
BASE_DIR = Path(__file__).parent
UPLOAD_FOLDER = BASE_DIR / 'uploads'
UPLOAD_FOLDER.mkdir(exist_ok=True)
(UPLOAD_FOLDER / 'incoming').mkdir(exist_ok=True)
(UPLOAD_FOLDER / 'outgoing').mkdir(exist_ok=True)
(UPLOAD_FOLDER / 'documents').mkdir(exist_ok=True)
(UPLOAD_FOLDER / 'profiles').mkdir(exist_ok=True)

# إنشاء التطبيق
app = Flask(__name__)

# إعدادات التطبيق الأساسية
app.config['SECRET_KEY'] = secrets.token_hex(32)
app.config['SQLALCHEMY_DATABASE_URI'] = f'sqlite:///{BASE_DIR}/archive_system.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
app.config['WTF_CSRF_ENABLED'] = False  # تعطيل CSRF للتشغيل العادي
app.config['PERMANENT_SESSION_LIFETIME'] = timedelta(hours=2)
app.config['MAX_CONTENT_LENGTH'] = 50 * 1024 * 1024  # 50MB max file size
app.config['UPLOAD_FOLDER'] = str(UPLOAD_FOLDER)

# إعداد Babel للترجمة
app.config['LANGUAGES'] = {
    'ar': 'العربية',
    'en': 'English'
}
app.config['BABEL_DEFAULT_LOCALE'] = 'ar'
app.config['BABEL_DEFAULT_TIMEZONE'] = 'UTC'

# إعداد البريد الإلكتروني
app.config['MAIL_SERVER'] = 'smtp.gmail.com'
app.config['MAIL_PORT'] = 587
app.config['MAIL_USE_TLS'] = True
app.config['MAIL_USERNAME'] = '<EMAIL>'
app.config['MAIL_PASSWORD'] = 'your-app-password'
app.config['MAIL_DEFAULT_SENDER'] = '<EMAIL>'

# تهيئة الإضافات
db = SQLAlchemy(app)
bcrypt = Bcrypt(app)
login_manager = LoginManager()
login_manager.init_app(app)
login_manager.login_view = 'login'
login_manager.login_message = 'يرجى تسجيل الدخول للوصول إلى هذه الصفحة'
login_manager.login_message_category = 'info'

# تهيئة Babel والبريد الإلكتروني
babel = Babel()
babel.init_app(app)
mail = Mail(app)

def get_locale():
    """تحديد اللغة المطلوبة"""
    if 'language' in session:
        return session['language']
    return request.accept_languages.best_match(app.config['LANGUAGES'].keys()) or 'ar'

babel.locale_selector_func = get_locale

# إعداد السجلات
logger.add("logs/archive_system.log", rotation="1 MB", retention="30 days")

# دوال مساعدة مبسطة
def allowed_file(filename):
    """التحقق من نوع الملف المسموح"""
    ALLOWED_EXTENSIONS = {'pdf', 'doc', 'docx', 'txt', 'jpg', 'jpeg', 'png', 'gif'}
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def get_file_type(filename):
    """تحديد نوع الملف"""
    if not filename:
        return 'unknown'
    extension = filename.rsplit('.', 1)[1].lower() if '.' in filename else ''
    
    file_types = {
        'pdf': 'pdf',
        'doc': 'document',
        'docx': 'document',
        'txt': 'text',
        'jpg': 'image',
        'jpeg': 'image',
        'png': 'image',
        'gif': 'image'
    }
    
    return file_types.get(extension, 'unknown')

# نماذج قاعدة البيانات المبسطة
class User(UserMixin, db.Model):
    """نموذج المستخدم"""
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)
    full_name = db.Column(db.String(200), nullable=False)
    password_hash = db.Column(db.String(128), nullable=False)
    role = db.Column(db.String(20), default='employee')
    department = db.Column(db.String(100))
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.now)
    last_login = db.Column(db.DateTime)
    
    def set_password(self, password):
        """تشفير كلمة المرور"""
        self.password_hash = bcrypt.generate_password_hash(password).decode('utf-8')
    
    def check_password(self, password):
        """التحقق من كلمة المرور"""
        return bcrypt.check_password_hash(self.password_hash, password)
    
    def is_admin(self):
        """التحقق من صلاحيات المدير"""
        return self.role == 'admin'

class Document(db.Model):
    """نموذج الوثيقة"""
    id = db.Column(db.Integer, primary_key=True)
    title = db.Column(db.String(200), nullable=False)
    description = db.Column(db.Text)
    document_type = db.Column(db.String(50), default='عام')
    status = db.Column(db.String(50), default='نشط')
    file_name = db.Column(db.String(255))
    file_path = db.Column(db.String(500))
    file_size = db.Column(db.Integer)
    qr_code = db.Column(db.String(100), unique=True)
    tags = db.Column(db.String(500))
    created_by = db.Column(db.Integer, db.ForeignKey('user.id'))
    created_at = db.Column(db.DateTime, default=datetime.now)
    updated_at = db.Column(db.DateTime, default=datetime.now, onupdate=datetime.now)
    
    creator = db.relationship('User', backref=db.backref('documents', lazy=True))

class IncomingDocument(db.Model):
    """نموذج الكتب الواردة"""
    id = db.Column(db.Integer, primary_key=True)
    incoming_number = db.Column(db.String(50), unique=True, nullable=False)
    sender_name = db.Column(db.String(200), nullable=False)
    subject = db.Column(db.String(500), nullable=False)
    received_date = db.Column(db.DateTime, nullable=False)
    status = db.Column(db.String(50), default='جديد')
    priority = db.Column(db.String(20), default='عادي')
    notes = db.Column(db.Text)
    file_name = db.Column(db.String(255))
    file_path = db.Column(db.String(500))
    file_size = db.Column(db.Integer)
    file_type = db.Column(db.String(50))
    received_by = db.Column(db.Integer, db.ForeignKey('user.id'))
    created_at = db.Column(db.DateTime, default=datetime.now)
    updated_at = db.Column(db.DateTime, default=datetime.now, onupdate=datetime.now)
    
    receiver = db.relationship('User', backref=db.backref('incoming_documents', lazy=True))

class OutgoingDocument(db.Model):
    """نموذج الكتب الصادرة"""
    id = db.Column(db.Integer, primary_key=True)
    outgoing_number = db.Column(db.String(50), unique=True, nullable=False)
    recipient_name = db.Column(db.String(200), nullable=False)
    subject = db.Column(db.String(500), nullable=False)
    sent_date = db.Column(db.DateTime, nullable=False)
    status = db.Column(db.String(50), default='مسودة')
    priority = db.Column(db.String(20), default='عادي')
    notes = db.Column(db.Text)
    file_name = db.Column(db.String(255))
    file_path = db.Column(db.String(500))
    file_size = db.Column(db.Integer)
    file_type = db.Column(db.String(50))
    prepared_by = db.Column(db.Integer, db.ForeignKey('user.id'))
    created_at = db.Column(db.DateTime, default=datetime.now)
    updated_at = db.Column(db.DateTime, default=datetime.now, onupdate=datetime.now)

    preparer = db.relationship('User', backref=db.backref('outgoing_documents', lazy=True), foreign_keys=[prepared_by])

@login_manager.user_loader
def load_user(user_id):
    return User.query.get(int(user_id))

# نماذج الويب المبسطة
class LoginForm(FlaskForm):
    username = StringField('اسم المستخدم', validators=[DataRequired()])
    password = PasswordField('كلمة المرور', validators=[DataRequired()])
    submit = SubmitField('تسجيل الدخول')

# المسارات الأساسية
@app.route('/')
def index():
    """الصفحة الرئيسية"""
    if current_user.is_authenticated:
        return redirect(url_for('dashboard'))
    return redirect(url_for('login'))

@app.route('/login', methods=['GET', 'POST'])
def login():
    """تسجيل الدخول"""
    if current_user.is_authenticated:
        return redirect(url_for('dashboard'))
    
    form = LoginForm()
    if form.validate_on_submit():
        user = User.query.filter_by(username=form.username.data).first()
        if user and user.check_password(form.password.data) and user.is_active:
            login_user(user)
            user.last_login = datetime.now()
            db.session.commit()
            flash(f'مرحباً {user.full_name}', 'success')
            return redirect(url_for('dashboard'))
        else:
            flash('اسم المستخدم أو كلمة المرور غير صحيحة', 'error')
    
    return render_template('login_simple.html', form=form)

@app.route('/logout')
@login_required
def logout():
    """تسجيل الخروج"""
    logout_user()
    flash('تم تسجيل الخروج بنجاح', 'info')
    return redirect(url_for('login'))

@app.route('/dashboard')
@login_required
def dashboard():
    """لوحة التحكم"""
    # إحصائيات بسيطة
    stats = {
        'total_documents': Document.query.count(),
        'total_incoming': IncomingDocument.query.count(),
        'total_outgoing': OutgoingDocument.query.count(),
        'total_users': User.query.count()
    }
    
    # أحدث الوثائق
    recent_documents = Document.query.order_by(Document.created_at.desc()).limit(5).all()
    recent_incoming = IncomingDocument.query.order_by(IncomingDocument.created_at.desc()).limit(5).all()
    recent_outgoing = OutgoingDocument.query.order_by(OutgoingDocument.created_at.desc()).limit(5).all()
    
    return render_template('dashboard_simple.html',
                         stats=stats,
                         recent_documents=recent_documents,
                         recent_incoming=recent_incoming,
                         recent_outgoing=recent_outgoing)

def init_database():
    """تهيئة قاعدة البيانات"""
    with app.app_context():
        db.create_all()
        
        # إنشاء مستخدم مدير افتراضي
        if not User.query.filter_by(username='admin').first():
            admin = User(
                username='admin',
                email='<EMAIL>',
                full_name='مدير النظام',
                role='admin',
                department='إدارة النظام'
            )
            admin.set_password('admin123')
            db.session.add(admin)
            
            # إنشاء مستخدم موظف للاختبار
            employee = User(
                username='employee',
                email='<EMAIL>',
                full_name='موظف الأرشيف',
                role='employee',
                department='قسم الأرشيف'
            )
            employee.set_password('employee123')
            db.session.add(employee)
            
            db.session.commit()
            logger.info("تم إنشاء المستخدمين الافتراضيين")

if __name__ == '__main__':
    # إنشاء مجلد السجلات
    Path('logs').mkdir(exist_ok=True)
    
    # تهيئة قاعدة البيانات
    init_database()
    
    # تشغيل التطبيق
    logger.info("بدء تشغيل نظام إدارة الأرشيف العام - النسخة المبسطة")
    print("========================================")
    print("    نظام إدارة الأرشيف العام")
    print("    Archive Management System")
    print("    النسخة المبسطة - Simplified Version")
    print("========================================")
    print()
    print("بيانات الدخول الافتراضية:")
    print("Default login credentials:")
    print("اسم المستخدم / Username: admin")
    print("كلمة المرور / Password: admin123")
    print()
    print("سيتم فتح التطبيق على:")
    print("Application will open at:")
    print("http://localhost:5000")
    print()
    
    app.run(debug=True, host='127.0.0.1', port=5000, threaded=True)
