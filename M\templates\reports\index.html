{% extends "base.html" %}

{% block title %}التقارير والإحصائيات - نظام إدارة الأرشيف العام{% endblock %}

{% block extra_css %}
<style>
    .reports-header {
        background: linear-gradient(135deg, #8e44ad 0%, #9b59b6 100%);
        color: white;
        border-radius: var(--border-radius);
        padding: 2rem;
        margin-bottom: 2rem;
        position: relative;
        overflow: hidden;
    }
    
    .reports-header::before {
        content: '';
        position: absolute;
        top: -50%;
        right: -50%;
        width: 200%;
        height: 200%;
        background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
        animation: float 6s ease-in-out infinite;
    }
    
    .reports-content {
        position: relative;
        z-index: 2;
    }
    
    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 2rem;
        margin-bottom: 3rem;
    }
    
    .stat-card {
        background: white;
        border-radius: var(--border-radius);
        padding: 2rem;
        box-shadow: var(--shadow-light);
        transition: all 0.3s ease;
        border-left: 4px solid var(--primary-color);
    }
    
    .stat-card:hover {
        transform: translateY(-5px);
        box-shadow: var(--shadow-medium);
    }
    
    .stat-card.documents {
        border-left-color: #3498db;
    }
    
    .stat-card.incoming {
        border-left-color: #27ae60;
    }
    
    .stat-card.outgoing {
        border-left-color: #e67e22;
    }
    
    .stat-card.users {
        border-left-color: #9b59b6;
    }
    
    .stat-icon {
        font-size: 3rem;
        margin-bottom: 1rem;
        opacity: 0.8;
    }
    
    .stat-number {
        font-size: 2.5rem;
        font-weight: 700;
        color: var(--primary-color);
        margin-bottom: 0.5rem;
    }
    
    .stat-label {
        font-size: 1.1rem;
        color: #6c757d;
        font-weight: 600;
    }
    
    .stat-change {
        font-size: 0.9rem;
        margin-top: 0.5rem;
    }
    
    .stat-change.positive {
        color: #27ae60;
    }
    
    .stat-change.negative {
        color: #e74c3c;
    }
    
    .reports-actions {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 2rem;
        margin-bottom: 3rem;
    }
    
    .action-card {
        background: white;
        border-radius: var(--border-radius);
        padding: 2rem;
        box-shadow: var(--shadow-light);
        transition: all 0.3s ease;
        text-align: center;
    }
    
    .action-card:hover {
        transform: translateY(-5px);
        box-shadow: var(--shadow-medium);
    }
    
    .action-icon {
        font-size: 4rem;
        margin-bottom: 1.5rem;
        color: var(--primary-color);
    }
    
    .action-title {
        font-size: 1.5rem;
        font-weight: 600;
        margin-bottom: 1rem;
        color: var(--primary-color);
    }
    
    .action-description {
        color: #6c757d;
        margin-bottom: 2rem;
        line-height: 1.6;
    }
    
    .btn-action {
        background: linear-gradient(135deg, var(--primary-color) 0%, #2980b9 100%);
        color: white;
        border: none;
        padding: 0.75rem 2rem;
        border-radius: var(--border-radius);
        font-weight: 600;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        transition: all 0.3s ease;
    }
    
    .btn-action:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-medium);
        color: white;
    }
    
    .quick-stats {
        background: white;
        border-radius: var(--border-radius);
        padding: 2rem;
        box-shadow: var(--shadow-light);
    }
    
    .quick-stats h3 {
        color: var(--primary-color);
        margin-bottom: 1.5rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }
    
    .stats-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0.75rem 0;
        border-bottom: 1px solid #f8f9fa;
    }
    
    .stats-row:last-child {
        border-bottom: none;
    }
    
    .stats-label {
        font-weight: 600;
        color: #495057;
    }
    
    .stats-value {
        font-weight: 700;
        color: var(--primary-color);
        font-size: 1.1rem;
    }
    
    @media (max-width: 768px) {
        .stats-grid {
            grid-template-columns: 1fr;
        }
        
        .reports-actions {
            grid-template-columns: 1fr;
        }
    }
</style>
{% endblock %}

{% block content %}
<!-- Reports Header -->
<div class="reports-header">
    <div class="reports-content">
        <h1 class="mb-3">
            <i class="fas fa-chart-bar me-3"></i>
            التقارير والإحصائيات
        </h1>
        <p class="mb-0 opacity-75">
            مركز شامل لجميع التقارير والإحصائيات الخاصة بنظام إدارة الأرشيف
        </p>
    </div>
</div>

<!-- Statistics Cards -->
<div class="stats-grid">
    <div class="stat-card documents">
        <div class="stat-icon">
            <i class="fas fa-file-alt" style="color: #3498db;"></i>
        </div>
        <div class="stat-number">{{ stats.total_documents }}</div>
        <div class="stat-label">إجمالي الوثائق</div>
        <div class="stat-change positive">
            <i class="fas fa-arrow-up me-1"></i>
            +{{ stats.documents_this_month }} هذا الشهر
        </div>
    </div>
    
    <div class="stat-card incoming">
        <div class="stat-icon">
            <i class="fas fa-inbox" style="color: #27ae60;"></i>
        </div>
        <div class="stat-number">{{ stats.total_incoming }}</div>
        <div class="stat-label">الكتب الواردة</div>
        <div class="stat-change positive">
            <i class="fas fa-arrow-up me-1"></i>
            +{{ stats.incoming_this_month }} هذا الشهر
        </div>
    </div>
    
    <div class="stat-card outgoing">
        <div class="stat-icon">
            <i class="fas fa-paper-plane" style="color: #e67e22;"></i>
        </div>
        <div class="stat-number">{{ stats.total_outgoing }}</div>
        <div class="stat-label">الكتب الصادرة</div>
        <div class="stat-change positive">
            <i class="fas fa-arrow-up me-1"></i>
            +{{ stats.outgoing_this_month }} هذا الشهر
        </div>
    </div>
    
    <div class="stat-card users">
        <div class="stat-icon">
            <i class="fas fa-users" style="color: #9b59b6;"></i>
        </div>
        <div class="stat-number">{{ stats.total_users }}</div>
        <div class="stat-label">المستخدمون</div>
        <div class="stat-change">
            <i class="fas fa-info-circle me-1"></i>
            مستخدمو النظام
        </div>
    </div>
</div>

<!-- Action Cards -->
<div class="reports-actions">
    <div class="action-card">
        <div class="action-icon">
            <i class="fas fa-file-export"></i>
        </div>
        <h3 class="action-title">توليد التقارير</h3>
        <p class="action-description">
            إنشاء تقارير مخصصة للوثائق والكتب الواردة والصادرة مع إمكانية التصدير
        </p>
        <a href="{{ url_for('generate_report') }}" class="btn-action">
            <i class="fas fa-plus me-2"></i>
            إنشاء تقرير جديد
        </a>
    </div>
    
    <div class="action-card">
        <div class="action-icon">
            <i class="fas fa-chart-line"></i>
        </div>
        <h3 class="action-title">الإحصائيات المتقدمة</h3>
        <p class="action-description">
            عرض إحصائيات تفصيلية ورسوم بيانية للاتجاهات والأنماط
        </p>
        <a href="{{ url_for('statistics') }}" class="btn-action">
            <i class="fas fa-chart-bar me-2"></i>
            عرض الإحصائيات
        </a>
    </div>
</div>

<!-- Quick Statistics -->
<div class="quick-stats">
    <h3>
        <i class="fas fa-tachometer-alt"></i>
        إحصائيات سريعة
    </h3>
    
    <div class="stats-row">
        <span class="stats-label">إجمالي العناصر:</span>
        <span class="stats-value">{{ stats.total_documents + stats.total_incoming + stats.total_outgoing }}</span>
    </div>
    
    <div class="stats-row">
        <span class="stats-label">النشاط هذا الشهر:</span>
        <span class="stats-value">{{ stats.documents_this_month + stats.incoming_this_month + stats.outgoing_this_month }}</span>
    </div>
    
    <div class="stats-row">
        <span class="stats-label">متوسط العناصر اليومية:</span>
        <span class="stats-value">{{ ((stats.documents_this_month + stats.incoming_this_month + stats.outgoing_this_month) / 30) | round(1) }}</span>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Animation for stat cards
    document.addEventListener('DOMContentLoaded', function() {
        const statCards = document.querySelectorAll('.stat-card');
        
        statCards.forEach((card, index) => {
            setTimeout(() => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(20px)';
                card.style.transition = 'all 0.6s ease';
                
                setTimeout(() => {
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, 100);
            }, index * 100);
        });
    });
</script>
{% endblock %}
