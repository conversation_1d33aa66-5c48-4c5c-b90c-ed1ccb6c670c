{% extends "base.html" %}

{% block title %}إضافة حدث جديد - التقويم الذكي{% endblock %}

{% block extra_css %}
<style>
    .event-header {
        background: linear-gradient(135deg, #8e44ad 0%, #9b59b6 100%);
        color: white;
        border-radius: var(--border-radius);
        padding: 2rem;
        margin-bottom: 2rem;
        text-align: center;
    }
    
    .event-form {
        background: white;
        border-radius: var(--border-radius);
        padding: 2rem;
        box-shadow: var(--shadow-light);
        max-width: 800px;
        margin: 0 auto;
    }
    
    .form-section {
        margin-bottom: 2rem;
        padding-bottom: 1.5rem;
        border-bottom: 1px solid #e9ecef;
    }
    
    .form-section:last-child {
        border-bottom: none;
        margin-bottom: 0;
    }
    
    .section-title {
        font-size: 1.25rem;
        font-weight: 700;
        color: #2c3e50;
        margin-bottom: 1rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }
    
    .form-row {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 1rem;
        margin-bottom: 1rem;
    }
    
    .form-group {
        display: flex;
        flex-direction: column;
    }
    
    .form-label {
        font-weight: 600;
        margin-bottom: 0.5rem;
        color: #2c3e50;
    }
    
    .form-control {
        padding: 0.75rem;
        border: 2px solid #e9ecef;
        border-radius: var(--border-radius);
        font-size: 1rem;
        transition: all 0.3s ease;
    }
    
    .form-control:focus {
        outline: none;
        border-color: #8e44ad;
        box-shadow: 0 0 0 3px rgba(142, 68, 173, 0.1);
    }
    
    .form-control.textarea {
        min-height: 100px;
        resize: vertical;
    }
    
    .color-picker {
        display: grid;
        grid-template-columns: repeat(6, 1fr);
        gap: 0.5rem;
        margin-top: 0.5rem;
    }
    
    .color-option {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        border: 3px solid transparent;
        cursor: pointer;
        transition: all 0.3s ease;
    }
    
    .color-option:hover {
        transform: scale(1.1);
        border-color: #2c3e50;
    }
    
    .color-option.selected {
        border-color: #2c3e50;
        transform: scale(1.1);
    }
    
    .time-inputs {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 1rem;
    }
    
    .checkbox-group {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        margin-top: 1rem;
    }
    
    .checkbox-group input[type="checkbox"] {
        width: 20px;
        height: 20px;
        accent-color: #8e44ad;
    }
    
    .btn-submit {
        background: linear-gradient(135deg, #8e44ad 0%, #9b59b6 100%);
        color: white;
        border: none;
        padding: 1rem 2rem;
        border-radius: var(--border-radius);
        font-size: 1.1rem;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        width: 100%;
    }
    
    .btn-submit:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(142, 68, 173, 0.3);
    }
    
    .btn-cancel {
        background: #6c757d;
        color: white;
        border: none;
        padding: 1rem 2rem;
        border-radius: var(--border-radius);
        font-size: 1.1rem;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        width: 100%;
        margin-top: 1rem;
        text-decoration: none;
        display: inline-block;
        text-align: center;
    }
    
    .btn-cancel:hover {
        background: #5a6268;
        color: white;
    }
    
    .link-section {
        background: #f8f9fa;
        border-radius: var(--border-radius);
        padding: 1.5rem;
    }
    
    .link-info {
        background: #e8f4fd;
        border: 1px solid #bee5eb;
        border-radius: var(--border-radius);
        padding: 1rem;
        margin-bottom: 1rem;
        color: #0c5460;
        font-size: 0.9rem;
    }
    
    @media (max-width: 768px) {
        .form-row {
            grid-template-columns: 1fr;
        }
        
        .time-inputs {
            grid-template-columns: 1fr;
        }
        
        .color-picker {
            grid-template-columns: repeat(3, 1fr);
        }
        
        .event-form {
            padding: 1.5rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<!-- Header -->
<div class="event-header">
    <h1>
        <i class="fas fa-plus-circle me-3"></i>
        إضافة حدث جديد
    </h1>
    <p>أضف حدث جديد إلى التقويم الذكي</p>
</div>

<!-- Form -->
<div class="event-form">
    <form method="POST">
        {{ form.hidden_tag() }}
        
        <!-- Basic Information -->
        <div class="form-section">
            <h3 class="section-title">
                <i class="fas fa-info-circle"></i>
                المعلومات الأساسية
            </h3>
            
            <div class="form-group">
                {{ form.title.label(class="form-label") }}
                {{ form.title(class="form-control", placeholder="أدخل عنوان الحدث") }}
                {% if form.title.errors %}
                    <div class="text-danger mt-1">
                        {% for error in form.title.errors %}
                            <small>{{ error }}</small>
                        {% endfor %}
                    </div>
                {% endif %}
            </div>
            
            <div class="form-group">
                {{ form.description.label(class="form-label") }}
                {{ form.description(class="form-control textarea", placeholder="وصف تفصيلي للحدث (اختياري)") }}
            </div>
        </div>
        
        <!-- Date and Time -->
        <div class="form-section">
            <h3 class="section-title">
                <i class="fas fa-clock"></i>
                التاريخ والوقت
            </h3>
            
            <div class="form-row">
                <div class="form-group">
                    {{ form.start_date.label(class="form-label") }}
                    {{ form.start_date(class="form-control") }}
                    {% if form.start_date.errors %}
                        <div class="text-danger mt-1">
                            {% for error in form.start_date.errors %}
                                <small>{{ error }}</small>
                            {% endfor %}
                        </div>
                    {% endif %}
                </div>
                
                <div class="form-group">
                    {{ form.end_date.label(class="form-label") }}
                    {{ form.end_date(class="form-control") }}
                </div>
            </div>
            
            <div class="time-inputs" id="timeInputs">
                <div class="form-group">
                    {{ form.start_time.label(class="form-label") }}
                    {{ form.start_time(class="form-control", type="time") }}
                </div>
                
                <div class="form-group">
                    {{ form.end_time.label(class="form-label") }}
                    {{ form.end_time(class="form-control", type="time") }}
                </div>
            </div>
            
            <div class="checkbox-group">
                {{ form.all_day(id="allDayCheckbox") }}
                <label for="allDayCheckbox">حدث طوال اليوم</label>
            </div>
        </div>
        
        <!-- Color -->
        <div class="form-section">
            <h3 class="section-title">
                <i class="fas fa-palette"></i>
                لون الحدث
            </h3>
            
            {{ form.color(style="display: none;", id="colorInput") }}
            <div class="color-picker">
                <div class="color-option" data-color="#3498db" style="background: #3498db;" title="أزرق"></div>
                <div class="color-option" data-color="#2ecc71" style="background: #2ecc71;" title="أخضر"></div>
                <div class="color-option" data-color="#e74c3c" style="background: #e74c3c;" title="أحمر"></div>
                <div class="color-option" data-color="#f39c12" style="background: #f39c12;" title="برتقالي"></div>
                <div class="color-option" data-color="#9b59b6" style="background: #9b59b6;" title="بنفسجي"></div>
                <div class="color-option" data-color="#1abc9c" style="background: #1abc9c;" title="تركوازي"></div>
            </div>
        </div>
        
        <!-- Link to Documents -->
        <div class="form-section">
            <h3 class="section-title">
                <i class="fas fa-link"></i>
                ربط بالوثائق (اختياري)
            </h3>
            
            <div class="link-section">
                <div class="link-info">
                    <i class="fas fa-info-circle me-2"></i>
                    يمكنك ربط هذا الحدث بوثيقة أو كتاب وارد أو صادر لسهولة المتابعة
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        {{ form.document_id.label(class="form-label") }}
                        {{ form.document_id(class="form-control") }}
                    </div>
                    
                    <div class="form-group">
                        {{ form.incoming_id.label(class="form-label") }}
                        {{ form.incoming_id(class="form-control") }}
                    </div>
                    
                    <div class="form-group">
                        {{ form.outgoing_id.label(class="form-label") }}
                        {{ form.outgoing_id(class="form-control") }}
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Submit Buttons -->
        <div class="form-section">
            {{ form.submit(class="btn-submit") }}
            <a href="{{ url_for('calendar') }}" class="btn-cancel">إلغاء</a>
        </div>
    </form>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Color picker functionality
        const colorOptions = document.querySelectorAll('.color-option');
        const colorInput = document.getElementById('colorInput');
        
        // Set default color
        const defaultColor = colorInput.value || '#3498db';
        document.querySelector(`[data-color="${defaultColor}"]`).classList.add('selected');
        
        colorOptions.forEach(option => {
            option.addEventListener('click', function() {
                // Remove selected class from all options
                colorOptions.forEach(opt => opt.classList.remove('selected'));
                
                // Add selected class to clicked option
                this.classList.add('selected');
                
                // Update hidden input value
                colorInput.value = this.dataset.color;
            });
        });
        
        // All day checkbox functionality
        const allDayCheckbox = document.getElementById('allDayCheckbox');
        const timeInputs = document.getElementById('timeInputs');
        
        allDayCheckbox.addEventListener('change', function() {
            if (this.checked) {
                timeInputs.style.display = 'none';
            } else {
                timeInputs.style.display = 'grid';
            }
        });
        
        // Auto-fill end date when start date changes
        const startDateInput = document.querySelector('input[name="start_date"]');
        const endDateInput = document.querySelector('input[name="end_date"]');
        
        startDateInput.addEventListener('change', function() {
            if (!endDateInput.value) {
                endDateInput.value = this.value;
            }
        });
        
        // Pre-fill date from URL parameter
        const urlParams = new URLSearchParams(window.location.search);
        const dateParam = urlParams.get('date');
        if (dateParam && !startDateInput.value) {
            startDateInput.value = dateParam;
            endDateInput.value = dateParam;
        }
        
        // Form validation
        const form = document.querySelector('form');
        form.addEventListener('submit', function(e) {
            const title = document.querySelector('input[name="title"]').value.trim();
            const startDate = startDateInput.value;
            
            if (!title) {
                e.preventDefault();
                alert('يرجى إدخال عنوان الحدث');
                return;
            }
            
            if (!startDate) {
                e.preventDefault();
                alert('يرجى تحديد تاريخ بداية الحدث');
                return;
            }
            
            // Check if end date is before start date
            const endDate = endDateInput.value;
            if (endDate && endDate < startDate) {
                e.preventDefault();
                alert('تاريخ النهاية لا يمكن أن يكون قبل تاريخ البداية');
                return;
            }
        });
        
        // Animation
        const form_sections = document.querySelectorAll('.form-section');
        form_sections.forEach((section, index) => {
            setTimeout(() => {
                section.style.opacity = '0';
                section.style.transform = 'translateY(20px)';
                section.style.transition = 'all 0.6s ease';
                
                setTimeout(() => {
                    section.style.opacity = '1';
                    section.style.transform = 'translateY(0)';
                }, 50);
            }, index * 100);
        });
    });
</script>
{% endblock %}
