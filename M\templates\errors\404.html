{% extends "base.html" %}

{% block title %}الصفحة غير موجودة - نظام إدارة الأرشيف العام{% endblock %}

{% block extra_css %}
<style>
    .error-container {
        min-height: 70vh;
        display: flex;
        align-items: center;
        justify-content: center;
        text-align: center;
    }
    
    .error-content {
        max-width: 600px;
        padding: 2rem;
    }
    
    .error-icon {
        font-size: 8rem;
        color: #e74c3c;
        margin-bottom: 2rem;
        animation: bounce 2s infinite;
    }
    
    @keyframes bounce {
        0%, 20%, 50%, 80%, 100% {
            transform: translateY(0);
        }
        40% {
            transform: translateY(-30px);
        }
        60% {
            transform: translateY(-15px);
        }
    }
    
    .error-title {
        font-size: 3rem;
        font-weight: 700;
        color: #2c3e50;
        margin-bottom: 1rem;
    }
    
    .error-message {
        font-size: 1.2rem;
        color: #7f8c8d;
        margin-bottom: 2rem;
        line-height: 1.6;
    }
    
    .error-actions {
        display: flex;
        gap: 1rem;
        justify-content: center;
        flex-wrap: wrap;
    }
    
    .btn-error {
        padding: 0.75rem 2rem;
        border-radius: 25px;
        font-weight: 600;
        text-decoration: none;
        transition: all 0.3s ease;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
    }
    
    .btn-primary-error {
        background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
        color: white;
        border: none;
    }
    
    .btn-primary-error:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(52, 152, 219, 0.3);
        color: white;
    }
    
    .btn-secondary-error {
        background: #ecf0f1;
        color: #2c3e50;
        border: 2px solid #bdc3c7;
    }
    
    .btn-secondary-error:hover {
        background: #d5dbdb;
        color: #2c3e50;
        transform: translateY(-2px);
    }
    
    .suggestions {
        background: #f8f9fa;
        border-radius: 10px;
        padding: 2rem;
        margin-top: 3rem;
        text-align: right;
    }
    
    .suggestions h5 {
        color: #2c3e50;
        margin-bottom: 1rem;
    }
    
    .suggestions ul {
        list-style: none;
        padding: 0;
    }
    
    .suggestions li {
        padding: 0.5rem 0;
        border-bottom: 1px solid #ecf0f1;
    }
    
    .suggestions li:last-child {
        border-bottom: none;
    }
    
    .suggestions a {
        color: #3498db;
        text-decoration: none;
        transition: color 0.3s ease;
    }
    
    .suggestions a:hover {
        color: #2980b9;
    }
</style>
{% endblock %}

{% block content %}
<div class="error-container">
    <div class="error-content">
        <div class="error-icon">
            <i class="fas fa-search"></i>
        </div>
        
        <h1 class="error-title">404</h1>
        
        <p class="error-message">
            عذراً، الصفحة التي تبحث عنها غير موجودة.<br>
            قد تكون الصفحة قد تم نقلها أو حذفها أو أن الرابط غير صحيح.
        </p>
        
        <div class="error-actions">
            <a href="{{ url_for('dashboard') }}" class="btn-error btn-primary-error">
                <i class="fas fa-home"></i>
                العودة للرئيسية
            </a>
            
            <button onclick="history.back()" class="btn-error btn-secondary-error">
                <i class="fas fa-arrow-right"></i>
                الصفحة السابقة
            </button>
        </div>
        
        <div class="suggestions">
            <h5><i class="fas fa-lightbulb me-2"></i>اقتراحات مفيدة:</h5>
            <ul>
                <li><a href="{{ url_for('documents') }}"><i class="fas fa-folder me-2"></i>تصفح الوثائق</a></li>
                <li><a href="{{ url_for('incoming_documents') }}"><i class="fas fa-inbox me-2"></i>الكتب الواردة</a></li>
                <li><a href="{{ url_for('outgoing_documents') }}"><i class="fas fa-paper-plane me-2"></i>الكتب الصادرة</a></li>
                <li><a href="{{ url_for('search') }}"><i class="fas fa-search me-2"></i>البحث المتقدم</a></li>
                <li><a href="{{ url_for('reports') }}"><i class="fas fa-chart-bar me-2"></i>التقارير</a></li>
            </ul>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // إضافة تأثيرات تفاعلية
    document.addEventListener('DOMContentLoaded', function() {
        // تأثير الظهور التدريجي
        const content = document.querySelector('.error-content');
        content.style.opacity = '0';
        content.style.transform = 'translateY(30px)';
        content.style.transition = 'all 0.6s ease';
        
        setTimeout(() => {
            content.style.opacity = '1';
            content.style.transform = 'translateY(0)';
        }, 100);
        
        // تأثير hover للأيقونة
        const icon = document.querySelector('.error-icon');
        icon.addEventListener('mouseenter', function() {
            this.style.transform = 'scale(1.1) rotate(10deg)';
        });
        
        icon.addEventListener('mouseleave', function() {
            this.style.transform = 'scale(1) rotate(0deg)';
        });
    });
</script>
{% endblock %}
