{% extends "base.html" %}

{% block title %}إدارة التوقيعات الرقمية - نظام إدارة الأرشيف العام{% endblock %}

{% block extra_css %}
<style>
    .signatures-header {
        background: linear-gradient(135deg, #2ecc71 0%, #27ae60 100%);
        color: white;
        border-radius: var(--border-radius);
        padding: 2rem;
        margin-bottom: 2rem;
        text-align: center;
    }
    
    .signatures-title {
        font-size: 2.5rem;
        font-weight: 700;
        margin: 0;
    }
    
    .signatures-subtitle {
        opacity: 0.9;
        margin: 0.5rem 0 0 0;
        font-size: 1.1rem;
    }
    
    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1.5rem;
        margin-bottom: 2rem;
    }
    
    .stat-card {
        background: white;
        border-radius: var(--border-radius);
        padding: 1.5rem;
        text-align: center;
        box-shadow: var(--shadow-light);
        transition: all 0.3s ease;
        border-left: 4px solid transparent;
    }
    
    .stat-card:hover {
        transform: translateY(-5px);
        box-shadow: var(--shadow-medium);
    }
    
    .stat-card.total {
        border-left-color: #3498db;
    }
    
    .stat-card.valid {
        border-left-color: #2ecc71;
    }
    
    .stat-card.invalid {
        border-left-color: #e74c3c;
    }
    
    .stat-card.mine {
        border-left-color: #f39c12;
    }
    
    .stat-number {
        font-size: 2rem;
        font-weight: 700;
        margin-bottom: 0.5rem;
    }
    
    .stat-number.total {
        color: #3498db;
    }
    
    .stat-number.valid {
        color: #2ecc71;
    }
    
    .stat-number.invalid {
        color: #e74c3c;
    }
    
    .stat-number.mine {
        color: #f39c12;
    }
    
    .stat-label {
        color: #7f8c8d;
        font-weight: 600;
    }
    
    .signatures-table {
        background: white;
        border-radius: var(--border-radius);
        box-shadow: var(--shadow-light);
        overflow: hidden;
    }
    
    .table-header {
        background: linear-gradient(135deg, #2ecc71 0%, #27ae60 100%);
        color: white;
        padding: 1.5rem;
        font-size: 1.2rem;
        font-weight: 600;
    }
    
    .signature-row {
        padding: 1.5rem;
        border-bottom: 1px solid #e9ecef;
        transition: all 0.3s ease;
    }
    
    .signature-row:last-child {
        border-bottom: none;
    }
    
    .signature-row:hover {
        background: #f8f9fa;
    }
    
    .signature-info {
        display: grid;
        grid-template-columns: auto 1fr auto auto;
        gap: 1rem;
        align-items: center;
    }
    
    .signature-status {
        width: 12px;
        height: 12px;
        border-radius: 50%;
        margin-left: 0.5rem;
    }
    
    .signature-status.valid {
        background: #2ecc71;
    }
    
    .signature-status.invalid {
        background: #e74c3c;
    }
    
    .signature-details h6 {
        margin: 0;
        font-weight: 600;
        color: #2c3e50;
    }
    
    .signature-meta {
        color: #7f8c8d;
        font-size: 0.875rem;
        margin-top: 0.25rem;
    }

    .signature-type-badge {
        display: inline-block;
        padding: 0.25rem 0.5rem;
        border-radius: 12px;
        font-size: 0.75rem;
        font-weight: 600;
        text-transform: uppercase;
    }

    .signature-type-badge.normal {
        background: #e3f2fd;
        color: #1976d2;
        border: 1px solid #bbdefb;
    }

    .signature-type-badge.certified {
        background: #e8f5e8;
        color: #2e7d32;
        border: 1px solid #c8e6c9;
    }
    
    .signature-actions {
        display: flex;
        gap: 0.5rem;
    }
    
    .btn-sm {
        padding: 0.375rem 0.75rem;
        font-size: 0.875rem;
        border-radius: 4px;
        border: none;
        cursor: pointer;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 0.25rem;
    }
    
    .btn-view {
        background: #3498db;
        color: white;
    }
    
    .btn-view:hover {
        background: #2980b9;
        color: white;
    }
    
    .btn-verify {
        background: #2ecc71;
        color: white;
    }
    
    .btn-verify:hover {
        background: #27ae60;
        color: white;
    }
    
    .btn-certificate {
        background: #f39c12;
        color: white;
    }
    
    .btn-certificate:hover {
        background: #e67e22;
        color: white;
    }
    
    .empty-state {
        text-align: center;
        padding: 4rem 2rem;
        color: #6c757d;
    }
    
    .empty-state i {
        font-size: 4rem;
        margin-bottom: 1rem;
        opacity: 0.5;
    }
    
    .pagination-container {
        background: white;
        border-radius: var(--border-radius);
        padding: 1rem;
        margin-top: 1rem;
        box-shadow: var(--shadow-light);
    }
    
    .filters-section {
        background: white;
        border-radius: var(--border-radius);
        padding: 1.5rem;
        margin-bottom: 2rem;
        box-shadow: var(--shadow-light);
    }
    
    .filters-title {
        font-size: 1.2rem;
        font-weight: 600;
        color: #2c3e50;
        margin-bottom: 1rem;
    }
    
    .filters-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
        align-items: end;
    }
    
    .filter-group label {
        display: block;
        margin-bottom: 0.5rem;
        font-weight: 500;
        color: #2c3e50;
    }
    
    .form-control, .form-select {
        width: 100%;
        padding: 0.5rem;
        border: 1px solid #ddd;
        border-radius: 4px;
        font-size: 0.875rem;
    }
    
    .btn-filter {
        background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
        color: white;
        padding: 0.5rem 1rem;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        font-weight: 500;
    }
    
    .btn-filter:hover {
        transform: translateY(-1px);
        box-shadow: 0 3px 10px rgba(52, 152, 219, 0.3);
    }
    
    @media (max-width: 768px) {
        .signatures-title {
            font-size: 2rem;
        }
        
        .stats-grid {
            grid-template-columns: repeat(2, 1fr);
        }
        
        .signature-info {
            grid-template-columns: 1fr;
            gap: 0.5rem;
        }
        
        .signature-actions {
            justify-content: flex-start;
            margin-top: 0.5rem;
        }
        
        .filters-grid {
            grid-template-columns: 1fr;
        }
    }
</style>
{% endblock %}

{% block content %}
<!-- Header -->
<div class="signatures-header">
    <h1 class="signatures-title">
        <i class="fas fa-signature me-3"></i>
        إدارة التوقيعات الرقمية
    </h1>
    <p class="signatures-subtitle">مراقبة وإدارة جميع التوقيعات الرقمية في النظام</p>
</div>

<!-- Statistics -->
<div class="stats-grid">
    <div class="stat-card total">
        <div class="stat-number total">{{ stats.total_signatures }}</div>
        <div class="stat-label">إجمالي التوقيعات</div>
    </div>
    
    <div class="stat-card valid">
        <div class="stat-number valid">{{ stats.valid_signatures }}</div>
        <div class="stat-label">التوقيعات الصحيحة</div>
    </div>
    
    <div class="stat-card invalid">
        <div class="stat-number invalid">{{ stats.invalid_signatures }}</div>
        <div class="stat-label">التوقيعات غير الصحيحة</div>
    </div>
    
    <div class="stat-card mine">
        <div class="stat-number mine">{{ stats.my_signatures }}</div>
        <div class="stat-label">توقيعاتي</div>
    </div>
</div>

<!-- Filters -->
<div class="filters-section">
    <h4 class="filters-title">
        <i class="fas fa-filter me-2"></i>
        فلترة التوقيعات
    </h4>
    <form method="GET" class="filters-grid">
        <div class="filter-group">
            <label>حالة التوقيع</label>
            <select name="status" class="form-select">
                <option value="">جميع الحالات</option>
                <option value="valid" {{ 'selected' if request.args.get('status') == 'valid' }}>صحيح</option>
                <option value="invalid" {{ 'selected' if request.args.get('status') == 'invalid' }}>غير صحيح</option>
            </select>
        </div>

        <div class="filter-group">
            <label>نوع التوقيع</label>
            <select name="signature_type" class="form-select">
                <option value="">جميع الأنواع</option>
                <option value="normal" {{ 'selected' if request.args.get('signature_type') == 'normal' }}>توقيع عادي</option>
                <option value="certified" {{ 'selected' if request.args.get('signature_type') == 'certified' }}>توقيع معتمد</option>
            </select>
        </div>
        
        <div class="filter-group">
            <label>المستخدم</label>
            <input type="text" name="user" class="form-control" 
                   placeholder="اسم المستخدم" 
                   value="{{ request.args.get('user', '') }}">
        </div>
        
        <div class="filter-group">
            <label>من تاريخ</label>
            <input type="date" name="date_from" class="form-control" 
                   value="{{ request.args.get('date_from', '') }}">
        </div>
        
        <div class="filter-group">
            <label>إلى تاريخ</label>
            <input type="date" name="date_to" class="form-control" 
                   value="{{ request.args.get('date_to', '') }}">
        </div>
        
        <div class="filter-group">
            <button type="submit" class="btn-filter">
                <i class="fas fa-search me-1"></i>
                بحث
            </button>
        </div>
    </form>
</div>

<!-- Signatures Table -->
<div class="signatures-table">
    <div class="table-header">
        <i class="fas fa-list me-2"></i>
        قائمة التوقيعات الرقمية
    </div>
    
    {% if signatures.items %}
        {% for signature in signatures.items %}
        <div class="signature-row">
            <div class="signature-info">
                <div class="signature-status {{ 'valid' if signature.is_valid else 'invalid' }}"></div>
                
                <div class="signature-details">
                    <h6>{{ signature.document.title }}</h6>
                    <div class="signature-meta">
                        <i class="fas fa-user me-1"></i>
                        {{ signature.user.full_name or signature.user.username }}
                        <span class="mx-2">|</span>
                        <i class="fas fa-clock me-1"></i>
                        {{ signature.timestamp.strftime('%Y/%m/%d %H:%M') }}
                        <span class="mx-2">|</span>
                        <i class="fas fa-shield-alt me-1"></i>
                        {{ signature.algorithm }}
                        <span class="mx-2">|</span>
                        <span class="signature-type-badge {{ signature.signature_type }}">
                            {% if signature.signature_type == 'certified' %}
                                <i class="fas fa-certificate me-1"></i>توقيع معتمد
                            {% else %}
                                <i class="fas fa-pen me-1"></i>توقيع عادي
                            {% endif %}
                        </span>
                    </div>
                </div>
                
                <div class="signature-actions">
                    <a href="{{ url_for('view_signatures', doc_id=signature.document_id) }}" 
                       class="btn-sm btn-view" title="عرض التفاصيل">
                        <i class="fas fa-eye"></i>
                    </a>
                    
                    <a href="{{ url_for('verify_signature', signature_id=signature.id) }}" 
                       class="btn-sm btn-verify" title="التحقق من التوقيع">
                        <i class="fas fa-shield-alt"></i>
                    </a>
                    
                    <a href="{{ url_for('download_signature_certificate', signature_id=signature.id) }}" 
                       class="btn-sm btn-certificate" title="تحميل الشهادة">
                        <i class="fas fa-certificate"></i>
                    </a>
                </div>
            </div>
        </div>
        {% endfor %}
    {% else %}
        <div class="empty-state">
            <i class="fas fa-signature"></i>
            <h4>لا توجد توقيعات رقمية</h4>
            <p>لم يتم العثور على توقيعات رقمية تطابق معايير البحث</p>
        </div>
    {% endif %}
</div>

<!-- Pagination -->
{% if signatures.pages > 1 %}
<div class="pagination-container">
    <nav aria-label="صفحات التوقيعات">
        <ul class="pagination justify-content-center mb-0">
            {% if signatures.has_prev %}
                <li class="page-item">
                    <a class="page-link" href="{{ url_for('digital_signatures_manager', page=signatures.prev_num, **request.args) }}">
                        السابق
                    </a>
                </li>
            {% endif %}
            
            {% for page_num in signatures.iter_pages() %}
                {% if page_num %}
                    {% if page_num != signatures.page %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('digital_signatures_manager', page=page_num, **request.args) }}">
                                {{ page_num }}
                            </a>
                        </li>
                    {% else %}
                        <li class="page-item active">
                            <span class="page-link">{{ page_num }}</span>
                        </li>
                    {% endif %}
                {% else %}
                    <li class="page-item disabled">
                        <span class="page-link">…</span>
                    </li>
                {% endif %}
            {% endfor %}
            
            {% if signatures.has_next %}
                <li class="page-item">
                    <a class="page-link" href="{{ url_for('digital_signatures_manager', page=signatures.next_num, **request.args) }}">
                        التالي
                    </a>
                </li>
            {% endif %}
        </ul>
    </nav>
</div>
{% endif %}
{% endblock %}

{% block extra_js %}
<script>
    // تحسين تجربة المستخدم
    document.addEventListener('DOMContentLoaded', function() {
        // إضافة تأثيرات للبطاقات
        const statCards = document.querySelectorAll('.stat-card');
        statCards.forEach((card, index) => {
            setTimeout(() => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(20px)';
                card.style.transition = 'all 0.6s ease';
                
                setTimeout(() => {
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, 50);
            }, index * 100);
        });
        
        // إضافة تأثيرات للصفوف
        const signatureRows = document.querySelectorAll('.signature-row');
        signatureRows.forEach((row, index) => {
            setTimeout(() => {
                row.style.opacity = '0';
                row.style.transform = 'translateX(20px)';
                row.style.transition = 'all 0.6s ease';
                
                setTimeout(() => {
                    row.style.opacity = '1';
                    row.style.transform = 'translateX(0)';
                }, 50);
            }, index * 50);
        });
    });
</script>
{% endblock %}
