{% extends "base.html" %}

{% block title %}التقويم الذكي - نظام إدارة الأرشيف العام{% endblock %}

{% block extra_css %}
<style>
    .calendar-header {
        background: linear-gradient(135deg, #8e44ad 0%, #9b59b6 100%);
        color: white;
        border-radius: var(--border-radius);
        padding: 2rem;
        margin-bottom: 2rem;
        text-align: center;
    }
    
    .calendar-title {
        font-size: 2rem;
        font-weight: 700;
        margin: 0;
    }
    
    .calendar-subtitle {
        opacity: 0.9;
        margin: 0.5rem 0 0 0;
    }
    
    .calendar-controls {
        background: white;
        border-radius: var(--border-radius);
        padding: 1.5rem;
        margin-bottom: 2rem;
        box-shadow: var(--shadow-light);
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-wrap: wrap;
        gap: 1rem;
    }
    
    .btn-add-event {
        background: linear-gradient(135deg, #8e44ad 0%, #9b59b6 100%);
        color: white;
        border: none;
        padding: 0.75rem 1.5rem;
        border-radius: var(--border-radius);
        font-weight: 600;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        transition: all 0.3s ease;
    }
    
    .btn-add-event:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(142, 68, 173, 0.3);
        color: white;
    }
    
    .calendar-container {
        background: white;
        border-radius: var(--border-radius);
        padding: 2rem;
        box-shadow: var(--shadow-light);
        min-height: 600px;
    }
    
    /* تخصيص FullCalendar */
    .fc {
        direction: ltr;
    }
    
    .fc-toolbar {
        margin-bottom: 1.5rem;
    }
    
    .fc-toolbar-title {
        font-size: 1.5rem;
        font-weight: 700;
        color: #2c3e50;
    }
    
    .fc-button {
        background: var(--primary-color) !important;
        border-color: var(--primary-color) !important;
        border-radius: 5px !important;
        font-weight: 600 !important;
    }
    
    .fc-button:hover {
        background: #2980b9 !important;
        border-color: #2980b9 !important;
    }
    
    .fc-button:focus {
        box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.25) !important;
    }
    
    .fc-event {
        border-radius: 5px !important;
        border: none !important;
        padding: 2px 5px !important;
        font-weight: 600 !important;
        cursor: pointer !important;
    }
    
    .fc-event:hover {
        opacity: 0.8 !important;
    }
    
    .fc-daygrid-day {
        transition: background-color 0.3s ease;
    }
    
    .fc-daygrid-day:hover {
        background-color: #f8f9fa !important;
    }
    
    .fc-day-today {
        background-color: rgba(52, 152, 219, 0.1) !important;
    }
    
    .fc-col-header-cell {
        background-color: #f8f9fa;
        font-weight: 700;
        color: #2c3e50;
    }
    
    .stats-row {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
        margin-bottom: 2rem;
    }
    
    .stat-card {
        background: white;
        border-radius: var(--border-radius);
        padding: 1.5rem;
        text-align: center;
        box-shadow: var(--shadow-light);
        transition: all 0.3s ease;
    }
    
    .stat-card:hover {
        transform: translateY(-5px);
        box-shadow: var(--shadow-medium);
    }
    
    .stat-icon {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 1rem;
        font-size: 1.5rem;
        color: white;
    }
    
    .stat-icon.events {
        background: linear-gradient(135deg, #8e44ad 0%, #9b59b6 100%);
    }
    
    .stat-icon.today {
        background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
    }
    
    .stat-icon.upcoming {
        background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
    }
    
    .stat-icon.completed {
        background: linear-gradient(135deg, #2ecc71 0%, #27ae60 100%);
    }
    
    .stat-number {
        font-size: 2rem;
        font-weight: 700;
        color: #2c3e50;
        margin-bottom: 0.5rem;
    }
    
    .stat-label {
        color: #7f8c8d;
        font-weight: 600;
    }
    
    .legend {
        background: #f8f9fa;
        border-radius: var(--border-radius);
        padding: 1rem;
        margin-bottom: 1rem;
    }
    
    .legend-title {
        font-weight: 700;
        margin-bottom: 0.5rem;
        color: #2c3e50;
    }
    
    .legend-items {
        display: flex;
        flex-wrap: wrap;
        gap: 1rem;
    }
    
    .legend-item {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        font-size: 0.875rem;
    }
    
    .legend-color {
        width: 16px;
        height: 16px;
        border-radius: 3px;
    }
    
    @media (max-width: 768px) {
        .calendar-controls {
            flex-direction: column;
            align-items: stretch;
        }
        
        .stats-row {
            grid-template-columns: repeat(2, 1fr);
        }
        
        .calendar-container {
            padding: 1rem;
        }
        
        .fc-toolbar {
            flex-direction: column;
            gap: 1rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<!-- Header -->
<div class="calendar-header">
    <h1 class="calendar-title">
        <i class="fas fa-calendar-alt me-3"></i>
        التقويم الذكي
    </h1>
    <p class="calendar-subtitle">إدارة الأحداث والمواعيد المرتبطة بالوثائق والكتب</p>
</div>

<!-- Statistics -->
<div class="stats-row">
    <div class="stat-card">
        <div class="stat-icon events">
            <i class="fas fa-calendar-check"></i>
        </div>
        <div class="stat-number" id="totalEvents">0</div>
        <div class="stat-label">إجمالي الأحداث</div>
    </div>
    
    <div class="stat-card">
        <div class="stat-icon today">
            <i class="fas fa-calendar-day"></i>
        </div>
        <div class="stat-number" id="todayEvents">0</div>
        <div class="stat-label">أحداث اليوم</div>
    </div>
    
    <div class="stat-card">
        <div class="stat-icon upcoming">
            <i class="fas fa-clock"></i>
        </div>
        <div class="stat-number" id="upcomingEvents">0</div>
        <div class="stat-label">أحداث قادمة</div>
    </div>
    
    <div class="stat-card">
        <div class="stat-icon completed">
            <i class="fas fa-check-circle"></i>
        </div>
        <div class="stat-number" id="completedEvents">0</div>
        <div class="stat-label">أحداث منتهية</div>
    </div>
</div>

<!-- Controls -->
<div class="calendar-controls">
    <div class="legend">
        <div class="legend-title">دليل الألوان:</div>
        <div class="legend-items">
            <div class="legend-item">
                <div class="legend-color" style="background: #3498db;"></div>
                <span>عام</span>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background: #2ecc71;"></div>
                <span>مهم</span>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background: #e74c3c;"></div>
                <span>عاجل</span>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background: #f39c12;"></div>
                <span>اجتماع</span>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background: #9b59b6;"></div>
                <span>مهمة</span>
            </div>
        </div>
    </div>
    
    <a href="{{ url_for('add_calendar_event') }}" class="btn-add-event">
        <i class="fas fa-plus"></i>
        إضافة حدث جديد
    </a>
</div>

<!-- Calendar -->
<div class="calendar-container">
    <div id="calendar"></div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const calendarEl = document.getElementById('calendar');
        
        const calendar = new FullCalendar.Calendar(calendarEl, {
            initialView: 'dayGridMonth',
            locale: 'ar',
            direction: 'rtl',
            headerToolbar: {
                left: 'prev,next today',
                center: 'title',
                right: 'dayGridMonth,timeGridWeek,timeGridDay'
            },
            buttonText: {
                today: 'اليوم',
                month: 'شهر',
                week: 'أسبوع',
                day: 'يوم'
            },
            events: {
                url: '{{ url_for("calendar_events") }}',
                failure: function() {
                    alert('حدث خطأ في تحميل الأحداث');
                }
            },
            eventClick: function(info) {
                // عرض تفاصيل الحدث
                const event = info.event;
                const eventDetails = `
                    العنوان: ${event.title}
                    الوصف: ${event.extendedProps.description || 'لا يوجد'}
                    البداية: ${event.start.toLocaleString('ar-SA')}
                    النهاية: ${event.end ? event.end.toLocaleString('ar-SA') : 'غير محدد'}
                `;
                
                if (confirm(`${eventDetails}\n\nهل تريد عرض التفاصيل الكاملة؟`)) {
                    window.location.href = `/calendar/event/${event.id}`;
                }
            },
            dateClick: function(info) {
                // إضافة حدث جديد في التاريخ المحدد
                const selectedDate = info.dateStr;
                window.location.href = `{{ url_for('add_calendar_event') }}?date=${selectedDate}`;
            },
            eventDidMount: function(info) {
                // إضافة tooltip للأحداث
                info.el.title = info.event.title + (info.event.extendedProps.description ? '\n' + info.event.extendedProps.description : '');
            }
        });
        
        calendar.render();
        
        // تحديث الإحصائيات
        updateStatistics();
        
        function updateStatistics() {
            fetch('{{ url_for("calendar_events") }}')
                .then(response => response.json())
                .then(events => {
                    const now = new Date();
                    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
                    const tomorrow = new Date(today);
                    tomorrow.setDate(tomorrow.getDate() + 1);
                    
                    let totalEvents = events.length;
                    let todayEvents = 0;
                    let upcomingEvents = 0;
                    let completedEvents = 0;
                    
                    events.forEach(event => {
                        const eventDate = new Date(event.start);
                        const eventDay = new Date(eventDate.getFullYear(), eventDate.getMonth(), eventDate.getDate());
                        
                        if (eventDay.getTime() === today.getTime()) {
                            todayEvents++;
                        } else if (eventDay > today) {
                            upcomingEvents++;
                        } else {
                            completedEvents++;
                        }
                    });
                    
                    document.getElementById('totalEvents').textContent = totalEvents;
                    document.getElementById('todayEvents').textContent = todayEvents;
                    document.getElementById('upcomingEvents').textContent = upcomingEvents;
                    document.getElementById('completedEvents').textContent = completedEvents;
                })
                .catch(error => {
                    console.error('خطأ في تحميل الإحصائيات:', error);
                });
        }
        
        // تحديث الإحصائيات كل دقيقة
        setInterval(updateStatistics, 60000);
    });
</script>
{% endblock %}
