{% extends "base.html" %}

{% block title %}{{ document.title }} - نظام إدارة الأرشيف العام{% endblock %}

{% block extra_css %}
<style>
    .document-header {
        background: var(--gradient-primary);
        color: white;
        border-radius: var(--border-radius);
        padding: 2rem;
        margin-bottom: 2rem;
        position: relative;
        overflow: hidden;
    }
    
    .document-header::before {
        content: '';
        position: absolute;
        top: -50%;
        right: -50%;
        width: 200%;
        height: 200%;
        background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
        animation: float 6s ease-in-out infinite;
    }
    
    .document-content {
        position: relative;
        z-index: 2;
    }
    
    .document-title {
        font-size: 2rem;
        font-weight: 700;
        margin-bottom: 1rem;
    }
    
    .document-meta {
        display: flex;
        flex-wrap: wrap;
        gap: 2rem;
        opacity: 0.9;
    }
    
    .meta-item {
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }
    
    .details-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 2rem;
        margin-bottom: 2rem;
    }
    
    .detail-card {
        background: white;
        border-radius: var(--border-radius);
        padding: 2rem;
        box-shadow: var(--shadow-light);
        transition: all 0.3s ease;
    }
    
    .detail-card:hover {
        transform: translateY(-5px);
        box-shadow: var(--shadow-medium);
    }
    
    .card-title {
        font-size: 1.25rem;
        font-weight: 600;
        color: var(--primary-color);
        margin-bottom: 1.5rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }
    
    .detail-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0.75rem 0;
        border-bottom: 1px solid #f8f9fa;
    }
    
    .detail-row:last-child {
        border-bottom: none;
    }
    
    .detail-label {
        font-weight: 600;
        color: #6c757d;
    }
    
    .detail-value {
        color: var(--primary-color);
        font-weight: 500;
    }
    
    .file-section {
        background: white;
        border-radius: var(--border-radius);
        padding: 2rem;
        box-shadow: var(--shadow-light);
        margin-bottom: 2rem;
    }
    
    .file-preview {
        display: flex;
        align-items: center;
        gap: 1rem;
        padding: 1.5rem;
        background: #f8f9fa;
        border-radius: var(--border-radius);
        margin-bottom: 1rem;
    }
    
    .file-icon {
        font-size: 3rem;
        width: 60px;
        text-align: center;
    }
    
    .file-pdf { color: #e74c3c; }
    .file-doc { color: #2980b9; }
    .file-image { color: #27ae60; }
    .file-txt { color: #f39c12; }
    .file-default { color: #6c757d; }
    
    .file-info h5 {
        margin-bottom: 0.5rem;
        color: var(--primary-color);
    }
    
    .file-details {
        color: #6c757d;
        font-size: 0.9rem;
    }
    
    .file-actions {
        display: flex;
        gap: 1rem;
    }
    
    .btn-download {
        background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
        color: white;
        border: none;
        padding: 0.75rem 1.5rem;
        border-radius: var(--border-radius);
        text-decoration: none;
        font-weight: 600;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        transition: all 0.3s ease;
    }
    
    .btn-download:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-medium);
        color: white;
    }
    
    .action-buttons {
        background: white;
        border-radius: var(--border-radius);
        padding: 2rem;
        box-shadow: var(--shadow-light);
        display: flex;
        gap: 1rem;
        justify-content: center;
        flex-wrap: wrap;
    }
    
    .btn-action {
        padding: 0.75rem 2rem;
        border-radius: var(--border-radius);
        font-weight: 600;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        transition: all 0.3s ease;
        border: none;
        cursor: pointer;
    }
    
    .btn-edit {
        background: linear-gradient(135deg, #f39c12 0%, #f1c40f 100%);
        color: white;
    }
    
    .btn-delete {
        background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
        color: white;
    }
    
    .btn-back {
        background: #6c757d;
        color: white;
    }
    
    .btn-action:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-medium);
        color: white;
    }
    
    .status-badge {
        padding: 0.5rem 1rem;
        border-radius: 20px;
        font-weight: 600;
        font-size: 0.9rem;
    }
    
    .status-active {
        background: rgba(39, 174, 96, 0.1);
        color: #27ae60;
    }
    
    .status-archived {
        background: rgba(149, 165, 166, 0.1);
        color: #95a5a6;
    }
    
    .type-badge {
        background: var(--gradient-primary);
        color: white;
        padding: 0.5rem 1rem;
        border-radius: 20px;
        font-weight: 600;
        font-size: 0.9rem;
    }
    
    .tags-container {
        display: flex;
        flex-wrap: wrap;
        gap: 0.5rem;
    }
    
    .tag {
        background: rgba(52, 152, 219, 0.1);
        color: #3498db;
        padding: 0.25rem 0.75rem;
        border-radius: 15px;
        font-size: 0.8rem;
        font-weight: 500;
    }
    
    .qr-section {
        background: white;
        border-radius: var(--border-radius);
        padding: 2rem;
        box-shadow: var(--shadow-light);
        text-align: center;
    }
    
    .code-item {
        margin-bottom: 1.5rem;
    }
    
    .code-placeholder {
        width: 150px;
        height: 150px;
        background: #f8f9fa;
        border: 2px dashed #dee2e6;
        border-radius: var(--border-radius);
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 1rem auto;
        color: #6c757d;
        font-size: 3rem;
    }
    
    @media (max-width: 768px) {
        .details-grid {
            grid-template-columns: 1fr;
        }
        
        .document-meta {
            flex-direction: column;
            gap: 1rem;
        }
        
        .action-buttons {
            flex-direction: column;
        }
        
        .file-preview {
            flex-direction: column;
            text-align: center;
        }
    }
</style>
{% endblock %}

{% block content %}
<!-- Document Header -->
<div class="document-header">
    <div class="document-content">
        <h1 class="document-title">{{ document.title }}</h1>
        <div class="document-meta">
            <div class="meta-item">
                <i class="fas fa-tag"></i>
                <span class="type-badge">{{ document.document_type }}</span>
            </div>
            <div class="meta-item">
                <i class="fas fa-info-circle"></i>
                <span class="status-badge status-{{ 'active' if document.status == 'نشط' else 'archived' }}">
                    {{ document.status }}
                </span>
            </div>
            <div class="meta-item">
                <i class="fas fa-user"></i>
                <span>{{ document.creator.full_name if document.creator else 'غير محدد' }}</span>
            </div>
            <div class="meta-item">
                <i class="fas fa-calendar"></i>
                <span>{{ document.created_at.strftime('%Y/%m/%d %H:%M') }}</span>
            </div>
        </div>
    </div>
</div>

<div class="details-grid">
    <!-- Document Details -->
    <div class="detail-card">
        <h3 class="card-title">
            <i class="fas fa-info-circle"></i>
            تفاصيل الوثيقة
        </h3>
        
        <div class="detail-row">
            <span class="detail-label">العنوان:</span>
            <span class="detail-value">{{ document.title }}</span>
        </div>
        
        {% if document.description %}
        <div class="detail-row">
            <span class="detail-label">الوصف:</span>
            <span class="detail-value">{{ document.description }}</span>
        </div>
        {% endif %}
        
        <div class="detail-row">
            <span class="detail-label">النوع:</span>
            <span class="detail-value">{{ document.document_type }}</span>
        </div>
        
        <div class="detail-row">
            <span class="detail-label">الحالة:</span>
            <span class="detail-value">{{ document.status }}</span>
        </div>
        
        {% if document.tags %}
        <div class="detail-row">
            <span class="detail-label">الكلمات المفتاحية:</span>
            <div class="tags-container">
                {% for tag in document.tags.split(',') %}
                    <span class="tag">{{ tag.strip() }}</span>
                {% endfor %}
            </div>
        </div>
        {% endif %}
    </div>
    
    <!-- System Information -->
    <div class="detail-card">
        <h3 class="card-title">
            <i class="fas fa-cog"></i>
            معلومات النظام
        </h3>
        
        <div class="detail-row">
            <span class="detail-label">رقم الوثيقة:</span>
            <span class="detail-value">#{{ document.id }}</span>
        </div>
        

        

        
        <div class="detail-row">
            <span class="detail-label">تاريخ الإنشاء:</span>
            <span class="detail-value">{{ document.created_at.strftime('%Y/%m/%d %H:%M') }}</span>
        </div>
        
        <div class="detail-row">
            <span class="detail-label">آخر تحديث:</span>
            <span class="detail-value">{{ document.updated_at.strftime('%Y/%m/%d %H:%M') }}</span>
        </div>
        
        <div class="detail-row">
            <span class="detail-label">المنشئ:</span>
            <span class="detail-value">{{ document.creator.full_name if document.creator else 'غير محدد' }}</span>
        </div>
    </div>
</div>

<!-- File Section -->
{% if document.file_name %}
<div class="file-section">
    <h3 class="card-title">
        <i class="fas fa-file"></i>
        الملف المرفق
    </h3>
    
    <div class="file-preview">
        {% set file_ext = document.file_name.split('.')[-1].lower() %}
        {% if file_ext == 'pdf' %}
            <i class="fas fa-file-pdf file-icon file-pdf"></i>
        {% elif file_ext in ['doc', 'docx'] %}
            <i class="fas fa-file-word file-icon file-doc"></i>
        {% elif file_ext in ['jpg', 'jpeg', 'png', 'gif'] %}
            <i class="fas fa-file-image file-icon file-image"></i>
        {% elif file_ext == 'txt' %}
            <i class="fas fa-file-alt file-icon file-txt"></i>
        {% else %}
            <i class="fas fa-file file-icon file-default"></i>
        {% endif %}
        
        <div class="file-info">
            <h5>{{ document.file_name }}</h5>
            <div class="file-details">
                {% if document.file_size %}
                    <span>الحجم: {{ "%.2f"|format(document.file_size / 1024 / 1024) }} ميجابايت</span>
                {% endif %}
                <span class="mx-2">•</span>
                <span>النوع: {{ file_ext.upper() }}</span>
            </div>
        </div>
        
        <div class="file-actions">
            <a href="#" class="btn-download" onclick="downloadFile({{ document.id }})">
                <i class="fas fa-download"></i>
                تحميل الملف
            </a>
        </div>
    </div>
</div>
{% endif %}

<!-- QR Code Section -->
<div class="qr-section">
    <h3 class="card-title">
        <i class="fas fa-qrcode"></i>
        رمز QR للوثيقة
    </h3>

    <div class="row justify-content-center">
        <div class="col-md-6">
            <div class="code-item">
                <h5>رمز QR</h5>
                <div class="code-placeholder">
                    <i class="fas fa-qrcode"></i>
                </div>
                <small class="text-muted">امسح للوصول المباشر للوثيقة</small>
                <div class="mt-3">
                    <a href="{{ url_for('document_qr', doc_id=document.id) }}" class="btn btn-primary btn-sm">
                        <i class="fas fa-download me-2"></i>
                        تحميل QR Code
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Action Buttons -->
<div class="action-buttons">
    <a href="{{ url_for('documents') }}" class="btn-action btn-back">
        <i class="fas fa-arrow-right"></i>
        العودة للقائمة
    </a>
    
    {% if current_user.is_admin() or document.created_by == current_user.id %}
    <a href="{{ url_for('edit_document', id=document.id) }}" class="btn-action btn-edit">
        <i class="fas fa-edit"></i>
        تعديل الوثيقة
    </a>
    
    <button type="button" class="btn-action btn-delete"
            onclick="confirmDelete({{ document.id }}, '{{ document.title }}')">
        <i class="fas fa-trash"></i>
        حذف الوثيقة
    </button>
    {% endif %}

    <!-- أزرار QR Code وملصق A4 -->
    <div class="btn-group" role="group">
        <button type="button" class="btn-action" style="background: linear-gradient(135deg, #9b59b6 0%, #8e44ad 100%);"
                data-bs-toggle="dropdown">
            <i class="fas fa-qrcode"></i>
            الرموز والملصقات
            <i class="fas fa-chevron-down ms-1"></i>
        </button>
        <ul class="dropdown-menu">
            <li>
                <a class="dropdown-item" href="{{ url_for('qr_preview', doc_id=document.id) }}">
                    <i class="fas fa-eye me-2"></i>
                    معاينة QR Code
                </a>
            </li>
            <li><hr class="dropdown-divider"></li>
            <li>
                <a class="dropdown-item" href="{{ url_for('document_qr', doc_id=document.id) }}">
                    <i class="fas fa-qrcode me-2"></i>
                    تحميل QR Code
                </a>
            </li>
            <li>
                <a class="dropdown-item" href="{{ url_for('document_a4_label', doc_id=document.id) }}">
                    <i class="fas fa-file-alt me-2"></i>
                    ملصق A4 (HTML)
                </a>
            </li>
            <li>
                <a class="dropdown-item" href="{{ url_for('document_a4_label_pdf', doc_id=document.id) }}">
                    <i class="fas fa-file-pdf me-2"></i>
                    ملصق A4 (PDF)
                </a>
            </li>
        </ul>
    </div>

    <!-- أزرار OCR -->
    <div class="btn-group" role="group">
        <button type="button" class="btn-action" style="background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);"
                data-bs-toggle="dropdown">
            <i class="fas fa-eye"></i>
            استخراج النص (OCR)
            <i class="fas fa-chevron-down ms-1"></i>
        </button>
        <ul class="dropdown-menu">
            <li>
                <a class="dropdown-item" href="{{ url_for('process_ocr', doc_id=document.id) }}">
                    <i class="fas fa-magic me-2"></i>
                    استخراج النص من الوثيقة
                </a>
            </li>
            {% if document.extracted_text %}
            <li>
                <a class="dropdown-item" href="{{ url_for('view_extracted_text', doc_id=document.id) }}">
                    <i class="fas fa-file-alt me-2"></i>
                    عرض النص المستخرج
                </a>
            </li>
            {% endif %}
        </ul>
    </div>

    <!-- أزرار التوقيع الرقمي -->
    <div class="btn-group" role="group">
        {% if current_user.is_admin() %}
        <button type="button" class="btn-action" style="background: linear-gradient(135deg, #2ecc71 0%, #27ae60 100%);"
                data-bs-toggle="dropdown">
            <i class="fas fa-signature"></i>
            التوقيع الرقمي
            <i class="fas fa-chevron-down ms-1"></i>
        </button>
        <ul class="dropdown-menu">
            <li><h6 class="dropdown-header">
                <i class="fas fa-info-circle me-2"></i>
                التوقيع الرقمي المتدرج
            </h6></li>
            <li>
                <a class="dropdown-item" href="#" onclick="showSignatureGuide()">
                    <i class="fas fa-question-circle me-2"></i>
                    دليل التوقيع الرقمي
                </a>
            </li>
            <li><hr class="dropdown-divider"></li>
            <li>
                <a class="dropdown-item" href="{{ url_for('sign_document_page', doc_id=document.id) }}">
                    <i class="fas fa-pen-fancy me-2"></i>
                    توقيع الوثيقة رقمياً
                </a>
            </li>
            <li>
                <a class="dropdown-item" href="{{ url_for('view_signatures', doc_id=document.id) }}">
                    <i class="fas fa-certificate me-2"></i>
                    عرض جميع التوقيعات
                    {% set signature_count = document.digital_signatures|length %}
                    {% if signature_count > 0 %}
                        <span class="badge bg-success ms-2">{{ signature_count }}</span>
                    {% endif %}
                </a>
            </li>
        </ul>
        {% else %}
        <button type="button" class="btn-action" style="background: linear-gradient(135deg, #6c757d 0%, #5a6268 100%);"
                disabled title="التوقيع الرقمي مقتصر على المديرين فقط">
            <i class="fas fa-lock"></i>
            التوقيع الرقمي
            <small class="d-block" style="font-size: 0.7rem; opacity: 0.8;">مديرين فقط</small>
        </button>
        {% endif %}
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تأكيد الحذف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من حذف الوثيقة "<span id="documentTitle"></span>"؟</p>
                <p class="text-danger"><strong>تحذير:</strong> هذا الإجراء لا يمكن التراجع عنه وسيتم حذف جميع الملفات المرتبطة.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-trash me-2"></i>
                        حذف نهائياً
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>
<!-- Modal للدليل المرئي للتوقيع الرقمي -->
<div class="modal fade" id="signatureGuideModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-success text-white">
                <h5 class="modal-title">
                    <i class="fas fa-signature me-2"></i>
                    دليل التوقيع الرقمي
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6><i class="fas fa-shield-alt text-success me-2"></i>ما هو التوقيع الرقمي؟</h6>
                        <p class="text-muted">
                            التوقيع الرقمي هو تقنية أمان تضمن صحة الوثيقة وهوية الموقع.
                            يستخدم خوارزميات التشفير المتقدمة لحماية الوثائق من التلاعب.
                        </p>

                        <h6><i class="fas fa-check-circle text-success me-2"></i>مميزات التوقيع الرقمي:</h6>
                        <ul class="list-unstyled">
                            <li><i class="fas fa-check text-success me-2"></i>ضمان عدم التلاعب بالوثيقة</li>
                            <li><i class="fas fa-check text-success me-2"></i>تأكيد هوية الموقع</li>
                            <li><i class="fas fa-check text-success me-2"></i>إثبات وقت التوقيع</li>
                            <li><i class="fas fa-check text-success me-2"></i>قابلية التحقق في أي وقت</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6><i class="fas fa-list-ol text-primary me-2"></i>خطوات التوقيع:</h6>
                        <div class="step-guide">
                            <div class="step-item">
                                <span class="step-number">1</span>
                                <div class="step-content">
                                    <strong>انقر على "توقيع الوثيقة رقمياً"</strong>
                                    <small class="d-block text-muted">سيتم توليد توقيع فريد مرتبط بحسابك</small>
                                </div>
                            </div>
                            <div class="step-item">
                                <span class="step-number">2</span>
                                <div class="step-content">
                                    <strong>تأكيد التوقيع</strong>
                                    <small class="d-block text-muted">راجع بيانات الوثيقة وأكد رغبتك في التوقيع</small>
                                </div>
                            </div>
                            <div class="step-item">
                                <span class="step-number">3</span>
                                <div class="step-content">
                                    <strong>حفظ التوقيع</strong>
                                    <small class="d-block text-muted">سيتم حفظ التوقيع وإنشاء شهادة رقمية</small>
                                </div>
                            </div>
                            <div class="step-item">
                                <span class="step-number">4</span>
                                <div class="step-content">
                                    <strong>التحقق والشهادة</strong>
                                    <small class="d-block text-muted">يمكن التحقق من التوقيع وتحميل الشهادة</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="alert alert-info mt-3">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>ملاحظة مهمة:</strong> التوقيع الرقمي لا يمكن التراجع عنه. تأكد من مراجعة الوثيقة جيداً قبل التوقيع.
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                <button type="button" class="btn btn-success" onclick="proceedToSign({{ document.id }})">
                    <i class="fas fa-pen-fancy me-2"></i>
                    المتابعة للتوقيع
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Modal تأكيد التوقيع -->
<div class="modal fade" id="confirmSignatureModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-warning text-dark">
                <h5 class="modal-title">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    تأكيد التوقيع الرقمي
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="text-center mb-3">
                    <i class="fas fa-file-contract fa-3x text-primary mb-3"></i>
                    <h6>هل أنت متأكد من رغبتك في توقيع هذه الوثيقة رقمياً؟</h6>
                </div>

                <div class="document-summary">
                    <h6><i class="fas fa-info-circle me-2"></i>ملخص الوثيقة:</h6>
                    <table class="table table-sm">
                        <tr>
                            <td><strong>العنوان:</strong></td>
                            <td>{{ document.title }}</td>
                        </tr>
                        <tr>
                            <td><strong>النوع:</strong></td>
                            <td>{{ document.document_type }}</td>
                        </tr>
                        <tr>
                            <td><strong>تاريخ الإنشاء:</strong></td>
                            <td>{{ document.created_at.strftime('%Y/%m/%d %H:%M') }}</td>
                        </tr>
                        <tr>
                            <td><strong>الموقع:</strong></td>
                            <td>{{ current_user.full_name or current_user.username }}</td>
                        </tr>
                    </table>
                </div>

                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>تحذير:</strong> التوقيع الرقمي لا يمكن التراجع عنه ويعتبر ملزماً قانونياً.
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-success" onclick="executeDigitalSignature({{ document.id }})">
                    <i class="fas fa-signature me-2"></i>
                    تأكيد التوقيع
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<style>
    .step-guide {
        padding: 1rem 0;
    }

    .step-item {
        display: flex;
        align-items: flex-start;
        margin-bottom: 1rem;
        padding: 0.75rem;
        border-radius: 8px;
        background: #f8f9fa;
        border-left: 3px solid #28a745;
    }

    .step-number {
        background: #28a745;
        color: white;
        width: 30px;
        height: 30px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
        margin-left: 1rem;
        flex-shrink: 0;
    }

    .step-content {
        flex: 1;
    }

    .document-summary {
        background: #f8f9fa;
        border-radius: 8px;
        padding: 1rem;
        margin-bottom: 1rem;
    }
</style>

<script>
// دوال التوقيع الرقمي
function showSignatureGuide() {
    const modal = new bootstrap.Modal(document.getElementById('signatureGuideModal'));
    modal.show();
}

// تم نقل وظائف التوقيع إلى صفحة منفصلة

function proceedToSign(documentId) {
    // إغلاق modal الدليل والانتقال لصفحة التوقيع
    bootstrap.Modal.getInstance(document.getElementById('signatureGuideModal')).hide();
    setTimeout(() => {
        window.location.href = `/documents/${documentId}/sign`;
    }, 300);
}

function executeDigitalSignature(documentId) {
    // الانتقال لصفحة التوقيع المتقدمة
    window.location.href = `/documents/${documentId}/sign`;
}

<script>
    function confirmDelete(documentId, documentTitle) {
        document.getElementById('documentTitle').textContent = documentTitle;
        document.getElementById('deleteForm').action = `/documents/${documentId}/delete`;
        
        const modal = new bootstrap.Modal(document.getElementById('deleteModal'));
        modal.show();
    }
    
    function downloadFile(documentId) {
        // In a real implementation, this would download the actual file
        alert('سيتم تحميل الملف... (هذه ميزة تجريبية)');
        // window.location.href = `/documents/${documentId}/download`;
    }
    
    // Add smooth scrolling for anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth'
                });
            }
        });
    });
</script>
{% endblock %}
