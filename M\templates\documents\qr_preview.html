{% extends "base.html" %}

{% block title %}معاينة QR Code - {{ document.title }}{% endblock %}

{% block extra_css %}
<style>
    .qr-preview-container {
        max-width: 800px;
        margin: 0 auto;
        padding: 2rem;
    }
    
    .document-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 15px;
        padding: 2rem;
        margin-bottom: 2rem;
        text-align: center;
    }
    
    .document-title {
        font-size: 1.5rem;
        font-weight: 700;
        margin-bottom: 1rem;
    }
    
    .qr-section {
        background: white;
        border-radius: 15px;
        padding: 2rem;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        text-align: center;
        margin-bottom: 2rem;
    }
    
    .qr-code-display {
        display: inline-block;
        padding: 2rem;
        background: #f8f9fa;
        border-radius: 15px;
        border: 3px dashed #dee2e6;
        margin: 2rem 0;
    }
    
    .qr-info {
        background: #e3f2fd;
        border: 1px solid #bbdefb;
        border-radius: 10px;
        padding: 1.5rem;
        margin: 2rem 0;
        text-align: right;
    }
    
    .qr-info h6 {
        color: #1976d2;
        margin-bottom: 1rem;
    }
    
    .qr-info ul {
        margin: 0;
        padding-right: 1.5rem;
    }
    
    .qr-info li {
        margin-bottom: 0.5rem;
        color: #1565c0;
    }
    
    .download-buttons {
        display: flex;
        gap: 1rem;
        justify-content: center;
        margin-top: 2rem;
        flex-wrap: wrap;
    }
    
    .btn-download {
        padding: 1rem 2rem;
        border-radius: 25px;
        font-weight: 600;
        text-decoration: none;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        gap: 0.5rem;
        border: none;
        cursor: pointer;
    }
    
    .btn-primary-download {
        background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
        color: white;
    }
    
    .btn-primary-download:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(52, 152, 219, 0.3);
        color: white;
    }
    
    .btn-secondary-download {
        background: linear-gradient(135deg, #9b59b6 0%, #8e44ad 100%);
        color: white;
    }
    
    .btn-secondary-download:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(155, 89, 182, 0.3);
        color: white;
    }
    
    .btn-success-download {
        background: linear-gradient(135deg, #2ecc71 0%, #27ae60 100%);
        color: white;
    }
    
    .btn-success-download:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(46, 204, 113, 0.3);
        color: white;
    }
    
    .instructions {
        background: #fff3cd;
        border: 1px solid #ffeaa7;
        border-radius: 10px;
        padding: 1.5rem;
        margin: 2rem 0;
        text-align: right;
    }
    
    .instructions h6 {
        color: #856404;
        margin-bottom: 1rem;
    }
    
    .instructions ol {
        margin: 0;
        padding-right: 1.5rem;
    }
    
    .instructions li {
        margin-bottom: 0.5rem;
        color: #856404;
    }
</style>
{% endblock %}

{% block content %}
<div class="qr-preview-container">
    <!-- معلومات الوثيقة -->
    <div class="document-header">
        <div class="document-title">
            <i class="fas fa-file-alt me-2"></i>
            {{ document.title }}
        </div>
        <div class="document-details">
            <span class="me-3">
                <i class="fas fa-tag me-1"></i>
                {{ document.document_type }}
            </span>
            <span class="me-3">
                <i class="fas fa-hashtag me-1"></i>
                {{ document.id }}
            </span>
            <span>
                <i class="fas fa-calendar me-1"></i>
                {{ document.created_at.strftime('%Y/%m/%d') }}
            </span>
        </div>
    </div>
    
    <!-- قسم QR Code -->
    <div class="qr-section">
        <h3>
            <i class="fas fa-qrcode me-2"></i>
            رمز QR للوثيقة
        </h3>
        
        <div class="qr-code-display">
            <img src="data:image/png;base64,{{ qr_image_base64 }}" alt="QR Code" style="max-width: 300px;">
        </div>
        
        <p class="text-muted">
            امسح هذا الرمز للوصول المباشر للوثيقة وتحميل الملف المرفق
        </p>
        
        <!-- أزرار التحميل -->
        <div class="download-buttons">
            <a href="{{ url_for('document_qr', doc_id=document.id) }}" class="btn-download btn-primary-download">
                <i class="fas fa-download"></i>
                تحميل QR Code
            </a>
            
            <a href="{{ url_for('document_a4_label', doc_id=document.id) }}" class="btn-download btn-secondary-download">
                <i class="fas fa-file-image"></i>
                ملصق A4 شامل
            </a>
            
            <a href="{{ url_for('view_document', id=document.id) }}" class="btn-download btn-success-download">
                <i class="fas fa-eye"></i>
                عرض الوثيقة
            </a>
        </div>
    </div>
    
    <!-- معلومات QR Code -->
    <div class="qr-info">
        <h6>
            <i class="fas fa-info-circle me-2"></i>
            ما يحتويه رمز QR:
        </h6>
        <ul>
            <li>رابط مباشر لعرض الوثيقة</li>
            <li>عنوان الوثيقة ورقمها</li>
            <li>نوع الوثيقة</li>
            {% if document.file_name %}
            <li>رابط تحميل الملف المرفق</li>
            {% endif %}
        </ul>
    </div>
    
    <!-- تعليمات الاستخدام -->
    <div class="instructions">
        <h6>
            <i class="fas fa-mobile-alt me-2"></i>
            كيفية استخدام رمز QR:
        </h6>
        <ol>
            <li>افتح تطبيق الكاميرا أو ماسح QR Code في هاتفك</li>
            <li>وجه الكاميرا نحو رمز QR</li>
            <li>انقر على الرابط الذي يظهر</li>
            <li>ستنتقل مباشرة لصفحة الوثيقة</li>
            <li>يمكنك تحميل الملف المرفق من الرابط المباشر</li>
        </ol>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // تأثير الظهور التدريجي
    const sections = document.querySelectorAll('.qr-section, .qr-info, .instructions');
    sections.forEach((section, index) => {
        section.style.opacity = '0';
        section.style.transform = 'translateY(30px)';
        section.style.transition = 'all 0.6s ease';
        
        setTimeout(() => {
            section.style.opacity = '1';
            section.style.transform = 'translateY(0)';
        }, 200 * (index + 1));
    });
});
</script>
{% endblock %}
