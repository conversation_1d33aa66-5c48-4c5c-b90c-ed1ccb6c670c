{% extends "base.html" %}

{% block title %}تعديل المستخدم: {{ user.full_name or user.username }} - إدارة المستخدمين{% endblock %}

{% block extra_css %}
<style>
    .user-header {
        background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
        color: white;
        border-radius: var(--border-radius);
        padding: 2rem;
        margin-bottom: 2rem;
        text-align: center;
    }
    
    .user-form {
        background: white;
        border-radius: var(--border-radius);
        padding: 2rem;
        box-shadow: var(--shadow-light);
        max-width: 600px;
        margin: 0 auto;
    }
    
    .form-section {
        margin-bottom: 2rem;
        padding-bottom: 1.5rem;
        border-bottom: 1px solid #e9ecef;
    }
    
    .form-section:last-child {
        border-bottom: none;
        margin-bottom: 0;
    }
    
    .section-title {
        font-size: 1.25rem;
        font-weight: 700;
        color: #2c3e50;
        margin-bottom: 1rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }
    
    .form-group {
        margin-bottom: 1.5rem;
    }
    
    .form-label {
        font-weight: 600;
        margin-bottom: 0.5rem;
        color: #2c3e50;
        display: block;
    }
    
    .form-control {
        width: 100%;
        padding: 0.75rem;
        border: 2px solid #e9ecef;
        border-radius: var(--border-radius);
        font-size: 1rem;
        transition: all 0.3s ease;
    }
    
    .form-control:focus {
        outline: none;
        border-color: #f39c12;
        box-shadow: 0 0 0 3px rgba(243, 156, 18, 0.1);
    }
    
    .form-control.is-invalid {
        border-color: #e74c3c;
    }
    
    .invalid-feedback {
        color: #e74c3c;
        font-size: 0.875rem;
        margin-top: 0.25rem;
    }
    
    .btn-submit {
        background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
        color: white;
        border: none;
        padding: 1rem 2rem;
        border-radius: var(--border-radius);
        font-size: 1.1rem;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        width: 100%;
    }
    
    .btn-submit:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(243, 156, 18, 0.3);
    }
    
    .btn-cancel {
        background: #6c757d;
        color: white;
        border: none;
        padding: 1rem 2rem;
        border-radius: var(--border-radius);
        font-size: 1.1rem;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        width: 100%;
        margin-top: 1rem;
        text-decoration: none;
        display: inline-block;
        text-align: center;
    }
    
    .btn-cancel:hover {
        background: #5a6268;
        color: white;
    }
    
    .user-info-card {
        background: #f8f9fa;
        border-radius: var(--border-radius);
        padding: 1.5rem;
        margin-bottom: 2rem;
        border-left: 4px solid #f39c12;
    }
    
    .user-avatar {
        width: 80px;
        height: 80px;
        border-radius: 50%;
        background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 2rem;
        font-weight: 700;
        margin: 0 auto 1rem;
    }
    
    .password-note {
        background: #e8f4fd;
        border: 1px solid #bee5eb;
        border-radius: var(--border-radius);
        padding: 1rem;
        margin-bottom: 1rem;
        color: #0c5460;
        font-size: 0.9rem;
    }
    
    .form-help {
        font-size: 0.875rem;
        color: #6c757d;
        margin-top: 0.25rem;
    }
    
    @media (max-width: 768px) {
        .user-form {
            padding: 1.5rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<!-- Header -->
<div class="user-header">
    <h1>
        <i class="fas fa-user-edit me-3"></i>
        تعديل المستخدم
    </h1>
    <p>تعديل بيانات المستخدم: {{ user.full_name or user.username }}</p>
</div>

<!-- User Info Card -->
<div class="user-info-card">
    <div class="user-avatar">
        {{ user.full_name[0] if user.full_name else user.username[0] }}
    </div>
    <div class="text-center">
        <h5>{{ user.full_name or user.username }}</h5>
        <p class="mb-0 text-muted">@{{ user.username }} • {{ user.email }}</p>
        <small class="text-muted">
            عضو منذ {{ user.created_at.strftime('%Y/%m/%d') if user.created_at else 'غير محدد' }}
        </small>
    </div>
</div>

<!-- Form -->
<div class="user-form">
    <form method="POST">
        {{ form.hidden_tag() }}
        
        <!-- Basic Information -->
        <div class="form-section">
            <h3 class="section-title">
                <i class="fas fa-user"></i>
                المعلومات الأساسية
            </h3>
            
            <div class="form-group">
                {{ form.username.label(class="form-label") }}
                {{ form.username(class="form-control" + (" is-invalid" if form.username.errors else "")) }}
                {% if form.username.errors %}
                    <div class="invalid-feedback">
                        {% for error in form.username.errors %}
                            {{ error }}
                        {% endfor %}
                    </div>
                {% endif %}
                <div class="form-help">اسم المستخدم يجب أن يكون فريداً</div>
            </div>
            
            <div class="form-group">
                {{ form.email.label(class="form-label") }}
                {{ form.email(class="form-control" + (" is-invalid" if form.email.errors else "")) }}
                {% if form.email.errors %}
                    <div class="invalid-feedback">
                        {% for error in form.email.errors %}
                            {{ error }}
                        {% endfor %}
                    </div>
                {% endif %}
                <div class="form-help">البريد الإلكتروني للإشعارات</div>
            </div>
            
            <div class="form-group">
                {{ form.full_name.label(class="form-label") }}
                {{ form.full_name(class="form-control") }}
                <div class="form-help">الاسم الكامل كما يظهر في النظام</div>
            </div>
        </div>
        
        <!-- Security -->
        <div class="form-section">
            <h3 class="section-title">
                <i class="fas fa-lock"></i>
                الأمان والصلاحيات
            </h3>
            
            <div class="password-note">
                <i class="fas fa-info-circle me-2"></i>
                اتركي حقل كلمة المرور فارغاً إذا كنت لا تريد تغييرها
            </div>
            
            <div class="form-group">
                {{ form.password.label(class="form-label") }}
                {{ form.password(class="form-control", placeholder="اتركه فارغاً للاحتفاظ بكلمة المرور الحالية") }}
                <div class="form-help">كلمة المرور الجديدة (6 أحرف على الأقل)</div>
            </div>
            
            <div class="form-group">
                {{ form.role.label(class="form-label") }}
                {{ form.role(class="form-control") }}
                <div class="form-help">
                    <strong>موظف:</strong> يمكنه إدارة الوثائق والكتب<br>
                    <strong>مدير:</strong> صلاحيات كاملة في النظام
                </div>
            </div>
            
            <div class="form-group">
                {{ form.is_active.label(class="form-label") }}
                {{ form.is_active(class="form-control") }}
                <div class="form-help">المستخدمين غير النشطين لا يمكنهم تسجيل الدخول</div>
            </div>
        </div>
        
        <!-- Department -->
        <div class="form-section">
            <h3 class="section-title">
                <i class="fas fa-building"></i>
                معلومات العمل
            </h3>
            
            <div class="form-group">
                {{ form.department.label(class="form-label") }}
                {{ form.department(class="form-control") }}
                <div class="form-help">القسم أو الوحدة التي يعمل بها المستخدم</div>
            </div>
        </div>
        
        <!-- Submit Buttons -->
        <div class="form-section">
            {{ form.submit(class="btn-submit", value="حفظ التغييرات") }}
            <a href="{{ url_for('manage_users') }}" class="btn-cancel">إلغاء</a>
        </div>
    </form>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Form validation
        const form = document.querySelector('form');
        form.addEventListener('submit', function(e) {
            const username = document.querySelector('input[name="username"]').value.trim();
            const email = document.querySelector('input[name="email"]').value.trim();
            const password = document.querySelector('input[name="password"]').value;
            
            if (!username) {
                e.preventDefault();
                alert('يرجى إدخال اسم المستخدم');
                return;
            }
            
            if (!email) {
                e.preventDefault();
                alert('يرجى إدخال البريد الإلكتروني');
                return;
            }
            
            if (password && password.length < 6) {
                e.preventDefault();
                alert('كلمة المرور يجب أن تكون 6 أحرف على الأقل');
                return;
            }
        });
        
        // Animation
        const sections = document.querySelectorAll('.form-section');
        sections.forEach((section, index) => {
            setTimeout(() => {
                section.style.opacity = '0';
                section.style.transform = 'translateY(20px)';
                section.style.transition = 'all 0.6s ease';
                
                setTimeout(() => {
                    section.style.opacity = '1';
                    section.style.transform = 'translateY(0)';
                }, 50);
            }, index * 100);
        });
    });
</script>
{% endblock %}
