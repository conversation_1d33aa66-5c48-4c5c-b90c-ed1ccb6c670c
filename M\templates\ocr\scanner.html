{% extends "base.html" %}

{% block title %}ماسح OCR المتقدم - نظام إدارة الأرشيف العام{% endblock %}

{% block extra_css %}
<style>
    .ocr-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: var(--border-radius);
        padding: 2rem;
        margin-bottom: 2rem;
        text-align: center;
    }
    
    .ocr-title {
        font-size: 2.5rem;
        font-weight: 700;
        margin: 0;
    }
    
    .ocr-subtitle {
        opacity: 0.9;
        margin: 0.5rem 0 0 0;
        font-size: 1.1rem;
    }
    
    .upload-area {
        background: white;
        border: 3px dashed #667eea;
        border-radius: var(--border-radius);
        padding: 3rem;
        text-align: center;
        transition: all 0.3s ease;
        cursor: pointer;
        margin-bottom: 2rem;
    }
    
    .upload-area:hover {
        border-color: #764ba2;
        background: #f8f9ff;
        transform: translateY(-2px);
    }
    
    .upload-area.dragover {
        border-color: #2ecc71;
        background: #f0fff4;
    }
    
    .upload-icon {
        font-size: 4rem;
        color: #667eea;
        margin-bottom: 1rem;
    }
    
    .upload-text {
        font-size: 1.2rem;
        color: #2c3e50;
        margin-bottom: 1rem;
    }
    
    .upload-hint {
        color: #7f8c8d;
        font-size: 0.9rem;
    }
    
    .file-input {
        display: none;
    }
    
    .processing {
        display: none;
        text-align: center;
        padding: 2rem;
        background: white;
        border-radius: var(--border-radius);
        box-shadow: var(--shadow-light);
        margin-bottom: 2rem;
    }
    
    .processing-spinner {
        font-size: 3rem;
        color: #667eea;
        margin-bottom: 1rem;
    }
    
    .results-container {
        display: none;
        background: white;
        border-radius: var(--border-radius);
        box-shadow: var(--shadow-light);
        overflow: hidden;
        margin-bottom: 2rem;
    }
    
    .results-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 1.5rem;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    
    .results-title {
        font-size: 1.3rem;
        font-weight: 600;
        margin: 0;
    }
    
    .results-stats {
        display: flex;
        gap: 1rem;
        font-size: 0.9rem;
    }
    
    .stat-item {
        background: rgba(255,255,255,0.2);
        padding: 0.25rem 0.75rem;
        border-radius: 15px;
    }
    
    .results-content {
        padding: 2rem;
    }
    
    .text-output {
        background: #f8f9fa;
        border: 1px solid #e9ecef;
        border-radius: var(--border-radius);
        padding: 1.5rem;
        font-family: 'Courier New', monospace;
        line-height: 1.8;
        white-space: pre-wrap;
        word-wrap: break-word;
        max-height: 400px;
        overflow-y: auto;
        direction: rtl;
        text-align: right;
        margin-bottom: 1.5rem;
    }
    
    .action-buttons {
        display: flex;
        gap: 1rem;
        flex-wrap: wrap;
    }
    
    .btn-action {
        padding: 0.75rem 1.5rem;
        border: none;
        border-radius: var(--border-radius);
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
    }
    
    .btn-copy {
        background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
        color: white;
    }
    
    .btn-copy:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(52, 152, 219, 0.3);
        color: white;
    }
    
    .btn-download {
        background: linear-gradient(135deg, #2ecc71 0%, #27ae60 100%);
        color: white;
    }
    
    .btn-download:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(46, 204, 113, 0.3);
        color: white;
    }
    
    .btn-save {
        background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
        color: white;
    }
    
    .btn-save:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(243, 156, 18, 0.3);
        color: white;
    }
    
    .btn-new {
        background: linear-gradient(135deg, #9b59b6 0%, #8e44ad 100%);
        color: white;
    }
    
    .btn-new:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(155, 89, 182, 0.3);
        color: white;
    }
    
    .features-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 2rem;
        margin-bottom: 2rem;
    }
    
    .feature-card {
        background: white;
        border-radius: var(--border-radius);
        padding: 2rem;
        box-shadow: var(--shadow-light);
        text-align: center;
        transition: all 0.3s ease;
    }
    
    .feature-card:hover {
        transform: translateY(-5px);
        box-shadow: var(--shadow-medium);
    }
    
    .feature-icon {
        font-size: 3rem;
        margin-bottom: 1rem;
    }
    
    .feature-icon.arabic {
        color: #e74c3c;
    }
    
    .feature-icon.english {
        color: #3498db;
    }
    
    .feature-icon.pdf {
        color: #e67e22;
    }
    
    .feature-icon.image {
        color: #2ecc71;
    }
    
    .feature-title {
        font-size: 1.3rem;
        font-weight: 600;
        color: #2c3e50;
        margin-bottom: 1rem;
    }
    
    .feature-description {
        color: #7f8c8d;
        line-height: 1.6;
    }
    
    .error-message {
        background: #f8d7da;
        color: #721c24;
        border: 1px solid #f5c6cb;
        border-radius: var(--border-radius);
        padding: 1rem;
        margin-bottom: 1rem;
        display: none;
    }
    
    .success-message {
        background: #d4edda;
        color: #155724;
        border: 1px solid #c3e6cb;
        border-radius: var(--border-radius);
        padding: 1rem;
        margin-bottom: 1rem;
        display: none;
    }
    
    @media (max-width: 768px) {
        .ocr-title {
            font-size: 2rem;
        }
        
        .upload-area {
            padding: 2rem 1rem;
        }
        
        .action-buttons {
            flex-direction: column;
        }
        
        .results-stats {
            flex-direction: column;
            gap: 0.5rem;
        }
        
        .features-grid {
            grid-template-columns: 1fr;
        }
    }
</style>
{% endblock %}

{% block content %}
<!-- Header -->
<div class="ocr-header">
    <h1 class="ocr-title">
        <i class="fas fa-eye me-3"></i>
        ماسح OCR المتقدم
    </h1>
    <p class="ocr-subtitle">استخراج النص من الصور وملفات PDF باستخدام تقنية التعرف الضوئي على الأحرف</p>
</div>

<!-- Error/Success Messages -->
<div class="error-message" id="errorMessage"></div>
<div class="success-message" id="successMessage"></div>

<!-- Upload Area -->
<div class="upload-area" id="uploadArea" onclick="document.getElementById('fileInput').click()">
    <div class="upload-icon">
        <i class="fas fa-cloud-upload-alt"></i>
    </div>
    <div class="upload-text">
        اسحب الملفات هنا أو انقر للاختيار
    </div>
    <div class="upload-hint">
        يدعم: JPG, PNG, PDF, TIFF (حتى 10 ميجابايت)
    </div>
    <input type="file" id="fileInput" class="file-input" accept=".jpg,.jpeg,.png,.pdf,.tiff,.bmp" onchange="handleFileSelect(this.files[0])">
</div>

<!-- Processing -->
<div class="processing" id="processing">
    <div class="processing-spinner">
        <i class="fas fa-spinner fa-spin"></i>
    </div>
    <h4>جاري معالجة الملف...</h4>
    <p>يتم استخراج النص باستخدام تقنية OCR المتقدمة</p>
</div>

<!-- Results -->
<div class="results-container" id="resultsContainer">
    <div class="results-header">
        <h3 class="results-title">
            <i class="fas fa-file-alt me-2"></i>
            النتائج
        </h3>
        <div class="results-stats">
            <div class="stat-item">
                <i class="fas fa-font me-1"></i>
                <span id="charCount">0</span> حرف
            </div>
            <div class="stat-item">
                <i class="fas fa-spell-check me-1"></i>
                <span id="wordCount">0</span> كلمة
            </div>
            <div class="stat-item">
                <i class="fas fa-list-ol me-1"></i>
                <span id="lineCount">0</span> سطر
            </div>
        </div>
    </div>
    
    <div class="results-content">
        <div class="text-output" id="textOutput"></div>
        
        <div class="action-buttons">
            <button class="btn-action btn-copy" onclick="copyText()">
                <i class="fas fa-copy"></i>
                نسخ النص
            </button>
            
            <button class="btn-action btn-download" onclick="downloadText()">
                <i class="fas fa-download"></i>
                تحميل كملف نصي
            </button>
            
            <button class="btn-action btn-save" onclick="saveAsDocument()">
                <i class="fas fa-save"></i>
                حفظ كوثيقة
            </button>
            
            <button class="btn-action btn-new" onclick="resetScanner()">
                <i class="fas fa-plus"></i>
                ملف جديد
            </button>
        </div>
    </div>
</div>

<!-- Features -->
<div class="features-grid">
    <div class="feature-card">
        <div class="feature-icon arabic">
            <i class="fas fa-language"></i>
        </div>
        <h4 class="feature-title">دعم اللغة العربية</h4>
        <p class="feature-description">
            استخراج النص العربي بدقة عالية مع دعم الخطوط المختلفة والتشكيل
        </p>
    </div>
    
    <div class="feature-card">
        <div class="feature-icon english">
            <i class="fas fa-globe"></i>
        </div>
        <h4 class="feature-title">اللغة الإنجليزية</h4>
        <p class="feature-description">
            معالجة متقدمة للنصوص الإنجليزية مع دعم الخطوط المطبوعة والمكتوبة
        </p>
    </div>
    
    <div class="feature-card">
        <div class="feature-icon pdf">
            <i class="fas fa-file-pdf"></i>
        </div>
        <h4 class="feature-title">ملفات PDF</h4>
        <p class="feature-description">
            استخراج النص من ملفات PDF المصورة والمختلطة مع تحسين الجودة
        </p>
    </div>
    
    <div class="feature-card">
        <div class="feature-icon image">
            <i class="fas fa-image"></i>
        </div>
        <h4 class="feature-title">الصور المتنوعة</h4>
        <p class="feature-description">
            دعم جميع تنسيقات الصور مع تحسين تلقائي للوضوح والتباين
        </p>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let extractedText = '';
let currentFilename = '';

// Drag and drop functionality
const uploadArea = document.getElementById('uploadArea');

uploadArea.addEventListener('dragover', (e) => {
    e.preventDefault();
    uploadArea.classList.add('dragover');
});

uploadArea.addEventListener('dragleave', () => {
    uploadArea.classList.remove('dragover');
});

uploadArea.addEventListener('drop', (e) => {
    e.preventDefault();
    uploadArea.classList.remove('dragover');
    
    const files = e.dataTransfer.files;
    if (files.length > 0) {
        handleFileSelect(files[0]);
    }
});

function handleFileSelect(file) {
    if (!file) return;
    
    // Validate file type
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/tiff', 'image/bmp', 'application/pdf'];
    if (!allowedTypes.includes(file.type)) {
        showError('نوع الملف غير مدعوم. يرجى اختيار صورة أو ملف PDF.');
        return;
    }
    
    // Validate file size (10MB)
    if (file.size > 10 * 1024 * 1024) {
        showError('حجم الملف كبير جداً. الحد الأقصى 10 ميجابايت.');
        return;
    }
    
    currentFilename = file.name;
    uploadFile(file);
}

function uploadFile(file) {
    const formData = new FormData();
    formData.append('file', file);
    
    // Show processing
    document.getElementById('uploadArea').style.display = 'none';
    document.getElementById('processing').style.display = 'block';
    document.getElementById('resultsContainer').style.display = 'none';
    hideMessages();
    
    fetch('/ocr-scanner/upload', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        document.getElementById('processing').style.display = 'none';
        
        if (data.success) {
            extractedText = data.text;
            displayResults(data);
            showSuccess(`تم استخراج النص من "${data.filename}" بنجاح!`);
        } else {
            document.getElementById('uploadArea').style.display = 'block';
            showError(data.message || 'حدث خطأ في معالجة الملف');
        }
    })
    .catch(error => {
        document.getElementById('processing').style.display = 'none';
        document.getElementById('uploadArea').style.display = 'block';
        showError('حدث خطأ في الاتصال بالخادم');
        console.error('Error:', error);
    });
}

function displayResults(data) {
    document.getElementById('textOutput').textContent = data.text;
    document.getElementById('charCount').textContent = data.char_count;
    document.getElementById('wordCount').textContent = data.word_count;
    document.getElementById('lineCount').textContent = data.line_count;
    document.getElementById('resultsContainer').style.display = 'block';
}

function copyText() {
    navigator.clipboard.writeText(extractedText).then(() => {
        showSuccess('تم نسخ النص بنجاح!');
    }).catch(() => {
        showError('فشل في نسخ النص');
    });
}

function downloadText() {
    const blob = new Blob([extractedText], { type: 'text/plain;charset=utf-8' });
    const url = window.URL.createObjectURL(blob);
    
    const a = document.createElement('a');
    a.href = url;
    a.download = `extracted_text_${currentFilename.split('.')[0]}.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);
    
    showSuccess('تم تحميل الملف النصي بنجاح!');
}

function saveAsDocument() {
    // Redirect to add document page with pre-filled text
    const params = new URLSearchParams({
        title: `نص مستخرج من ${currentFilename}`,
        description: extractedText.substring(0, 500) + (extractedText.length > 500 ? '...' : ''),
        extracted_text: extractedText
    });
    
    window.location.href = `/documents/add?${params.toString()}`;
}

function resetScanner() {
    extractedText = '';
    currentFilename = '';
    document.getElementById('uploadArea').style.display = 'block';
    document.getElementById('processing').style.display = 'none';
    document.getElementById('resultsContainer').style.display = 'none';
    document.getElementById('fileInput').value = '';
    hideMessages();
}

function showError(message) {
    const errorDiv = document.getElementById('errorMessage');
    errorDiv.textContent = message;
    errorDiv.style.display = 'block';
    document.getElementById('successMessage').style.display = 'none';
}

function showSuccess(message) {
    const successDiv = document.getElementById('successMessage');
    successDiv.textContent = message;
    successDiv.style.display = 'block';
    document.getElementById('errorMessage').style.display = 'none';
}

function hideMessages() {
    document.getElementById('errorMessage').style.display = 'none';
    document.getElementById('successMessage').style.display = 'none';
}
</script>
{% endblock %}
