{% extends "base.html" %}

{% block title %}إضافة قسم جديد - إدارة الأقسام{% endblock %}

{% block extra_css %}
<style>
    .department-header {
        background: linear-gradient(135deg, #9b59b6 0%, #8e44ad 100%);
        color: white;
        border-radius: var(--border-radius);
        padding: 2rem;
        margin-bottom: 2rem;
        text-align: center;
    }
    
    .department-form {
        background: white;
        border-radius: var(--border-radius);
        padding: 2rem;
        box-shadow: var(--shadow-light);
        max-width: 600px;
        margin: 0 auto;
    }
    
    .form-section {
        margin-bottom: 2rem;
        padding-bottom: 1.5rem;
        border-bottom: 1px solid #e9ecef;
    }
    
    .form-section:last-child {
        border-bottom: none;
        margin-bottom: 0;
    }
    
    .section-title {
        font-size: 1.25rem;
        font-weight: 700;
        color: #2c3e50;
        margin-bottom: 1rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }
    
    .form-group {
        margin-bottom: 1.5rem;
    }
    
    .form-label {
        font-weight: 600;
        margin-bottom: 0.5rem;
        color: #2c3e50;
        display: block;
    }
    
    .form-control {
        width: 100%;
        padding: 0.75rem;
        border: 2px solid #e9ecef;
        border-radius: var(--border-radius);
        font-size: 1rem;
        transition: all 0.3s ease;
    }
    
    .form-control:focus {
        outline: none;
        border-color: #9b59b6;
        box-shadow: 0 0 0 3px rgba(155, 89, 182, 0.1);
    }
    
    .form-control.is-invalid {
        border-color: #e74c3c;
    }
    
    .invalid-feedback {
        color: #e74c3c;
        font-size: 0.875rem;
        margin-top: 0.25rem;
    }
    
    .form-control.textarea {
        min-height: 100px;
        resize: vertical;
    }
    
    .btn-submit {
        background: linear-gradient(135deg, #9b59b6 0%, #8e44ad 100%);
        color: white;
        border: none;
        padding: 1rem 2rem;
        border-radius: var(--border-radius);
        font-size: 1.1rem;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        width: 100%;
    }
    
    .btn-submit:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(155, 89, 182, 0.3);
    }
    
    .btn-cancel {
        background: #6c757d;
        color: white;
        border: none;
        padding: 1rem 2rem;
        border-radius: var(--border-radius);
        font-size: 1.1rem;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        width: 100%;
        margin-top: 1rem;
        text-decoration: none;
        display: inline-block;
        text-align: center;
    }
    
    .btn-cancel:hover {
        background: #5a6268;
        color: white;
    }
    
    .form-help {
        font-size: 0.875rem;
        color: #6c757d;
        margin-top: 0.25rem;
    }
    
    .manager-info {
        background: #f8f9fa;
        border-radius: var(--border-radius);
        padding: 1rem;
        margin-top: 0.5rem;
        border-left: 4px solid #9b59b6;
    }
    
    @media (max-width: 768px) {
        .department-form {
            padding: 1.5rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<!-- Header -->
<div class="department-header">
    <h1>
        <i class="fas fa-building me-3"></i>
        إضافة قسم جديد
    </h1>
    <p>إنشاء قسم أو وحدة جديدة في المؤسسة</p>
</div>

<!-- Form -->
<div class="department-form">
    <form method="POST">
        {{ form.hidden_tag() }}
        
        <!-- Basic Information -->
        <div class="form-section">
            <h3 class="section-title">
                <i class="fas fa-info-circle"></i>
                المعلومات الأساسية
            </h3>
            
            <div class="form-group">
                {{ form.name.label(class="form-label") }}
                {{ form.name(class="form-control" + (" is-invalid" if form.name.errors else ""), placeholder="أدخل اسم القسم") }}
                {% if form.name.errors %}
                    <div class="invalid-feedback">
                        {% for error in form.name.errors %}
                            {{ error }}
                        {% endfor %}
                    </div>
                {% endif %}
                <div class="form-help">اسم القسم يجب أن يكون واضحاً ومميزاً</div>
            </div>
            
            <div class="form-group">
                {{ form.description.label(class="form-label") }}
                {{ form.description(class="form-control textarea", placeholder="وصف مختصر عن القسم ومهامه") }}
                <div class="form-help">وصف اختياري يوضح مهام ونشاطات القسم</div>
            </div>
        </div>
        
        <!-- Management -->
        <div class="form-section">
            <h3 class="section-title">
                <i class="fas fa-user-tie"></i>
                الإدارة والمسؤولية
            </h3>
            
            <div class="form-group">
                {{ form.manager_id.label(class="form-label") }}
                {{ form.manager_id(class="form-control") }}
                <div class="form-help">اختر المستخدم الذي سيكون مديراً لهذا القسم</div>
                
                <div class="manager-info">
                    <h6><i class="fas fa-info-circle me-2"></i>معلومات مهمة:</h6>
                    <ul class="mb-0">
                        <li>مدير القسم يمكنه إدارة موظفي القسم</li>
                        <li>يمكن تغيير مدير القسم لاحقاً</li>
                        <li>يمكن ترك هذا الحقل فارغاً وتعيين مدير لاحقاً</li>
                    </ul>
                </div>
            </div>
        </div>
        
        <!-- Status -->
        <div class="form-section">
            <h3 class="section-title">
                <i class="fas fa-toggle-on"></i>
                حالة القسم
            </h3>
            
            <div class="form-group">
                {{ form.is_active.label(class="form-label") }}
                {{ form.is_active(class="form-control") }}
                <div class="form-help">
                    <strong>نشط:</strong> القسم يعمل ويمكن تعيين موظفين إليه<br>
                    <strong>غير نشط:</strong> القسم متوقف مؤقتاً
                </div>
            </div>
        </div>
        
        <!-- Submit Buttons -->
        <div class="form-section">
            {{ form.submit(class="btn-submit") }}
            <a href="{{ url_for('manage_departments') }}" class="btn-cancel">إلغاء</a>
        </div>
    </form>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Form validation
        const form = document.querySelector('form');
        form.addEventListener('submit', function(e) {
            const name = document.querySelector('input[name="name"]').value.trim();
            
            if (!name) {
                e.preventDefault();
                alert('يرجى إدخال اسم القسم');
                return;
            }
            
            if (name.length < 2) {
                e.preventDefault();
                alert('اسم القسم يجب أن يكون حرفين على الأقل');
                return;
            }
        });
        
        // Manager selection info
        const managerSelect = document.querySelector('select[name="manager_id"]');
        managerSelect.addEventListener('change', function() {
            const selectedOption = this.options[this.selectedIndex];
            if (selectedOption.value) {
                console.log('تم اختيار مدير القسم:', selectedOption.text);
            }
        });
        
        // Animation
        const sections = document.querySelectorAll('.form-section');
        sections.forEach((section, index) => {
            setTimeout(() => {
                section.style.opacity = '0';
                section.style.transform = 'translateY(20px)';
                section.style.transition = 'all 0.6s ease';
                
                setTimeout(() => {
                    section.style.opacity = '1';
                    section.style.transform = 'translateY(0)';
                }, 50);
            }, index * 100);
        });
    });
</script>
{% endblock %}
