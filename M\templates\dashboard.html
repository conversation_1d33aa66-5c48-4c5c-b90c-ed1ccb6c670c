{% extends "base.html" %}

{% block title %}الرئيسية - نظام إدارة الأرشيف العام{% endblock %}

{% block extra_css %}
<style>
    .welcome-section {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: var(--border-radius);
        padding: 3rem 2rem;
        margin-bottom: 2rem;
        text-align: center;
        position: relative;
        overflow: hidden;
    }
    
    .welcome-section::before {
        content: '';
        position: absolute;
        top: -50%;
        right: -50%;
        width: 200%;
        height: 200%;
        background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
        animation: float 6s ease-in-out infinite;
    }
    
    @keyframes float {
        0%, 100% { transform: translateY(0px) rotate(0deg); }
        50% { transform: translateY(-20px) rotate(180deg); }
    }
    
    .welcome-content {
        position: relative;
        z-index: 2;
    }
    
    .welcome-title {
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 1rem;
    }
    
    .welcome-subtitle {
        font-size: 1.1rem;
        opacity: 0.9;
        margin-bottom: 1rem;
    }

    .welcome-badges .badge {
        font-size: 0.9rem;
        padding: 0.5rem 1rem;
        border-radius: 20px;
    }

    .welcome-avatar {
        position: relative;
        display: inline-block;
    }

    .avatar-circle {
        width: 80px;
        height: 80px;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.2);
        border: 3px solid rgba(255, 255, 255, 0.3);
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 2rem;
        font-weight: 700;
        color: white;
        margin: 0 auto;
        backdrop-filter: blur(10px);
    }

    .status-indicator {
        position: absolute;
        bottom: 5px;
        right: 5px;
        width: 20px;
        height: 20px;
        background: #2ecc71;
        border-radius: 50%;
        border: 3px solid white;
        animation: pulse 2s infinite;
    }

    @keyframes pulse {
        0% { box-shadow: 0 0 0 0 rgba(46, 204, 113, 0.7); }
        70% { box-shadow: 0 0 0 10px rgba(46, 204, 113, 0); }
        100% { box-shadow: 0 0 0 0 rgba(46, 204, 113, 0); }
    }

    .quick-stats {
        background: rgba(255, 255, 255, 0.1);
        border-radius: 15px;
        padding: 1rem;
        backdrop-filter: blur(10px);
    }

    .mini-stat {
        color: white;
    }

    .mini-number {
        font-size: 1.5rem;
        font-weight: 700;
        margin-bottom: 0.25rem;
    }

    .mini-label {
        font-size: 0.8rem;
        opacity: 0.8;
    }
    
    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 1.5rem;
        margin-bottom: 2rem;
    }
    
    .stat-card {
        background: white;
        border-radius: var(--border-radius);
        padding: 2rem;
        text-align: center;
        box-shadow: var(--shadow-light);
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
        cursor: pointer;
    }
    
    .stat-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: var(--gradient-primary);
        transform: scaleX(0);
        transition: transform 0.3s ease;
    }
    
    .stat-card:hover::before {
        transform: scaleX(1);
    }
    
    .stat-card:hover {
        transform: translateY(-10px);
        box-shadow: var(--shadow-medium);
    }
    
    .stat-icon {
        font-size: 3rem;
        margin-bottom: 1rem;
        background: var(--gradient-primary);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }
    
    .stat-number {
        font-size: 2.5rem;
        font-weight: 700;
        color: var(--primary-color);
        margin-bottom: 0.5rem;
        counter-reset: number;
        animation: countUp 2s ease-out;
    }
    
    @keyframes countUp {
        from { opacity: 0; transform: translateY(20px); }
        to { opacity: 1; transform: translateY(0); }
    }
    
    .stat-label {
        color: #6c757d;
        font-weight: 500;
        font-size: 1.1rem;
    }
    
    .quick-actions {
        background: white;
        border-radius: var(--border-radius);
        padding: 2rem;
        box-shadow: var(--shadow-light);
        margin-bottom: 2rem;
    }
    
    .section-title {
        font-size: 1.5rem;
        font-weight: 600;
        color: var(--primary-color);
        margin-bottom: 1.5rem;
        display: flex;
        align-items: center;
    }
    
    .section-title i {
        margin-left: 0.5rem;
        color: var(--secondary-color);
    }
    
    .action-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
    }
    
    .action-btn {
        background: white;
        border: 2px solid #e9ecef;
        border-radius: var(--border-radius);
        padding: 1.5rem;
        text-align: center;
        text-decoration: none;
        color: var(--dark-color);
        transition: all 0.3s ease;
        display: block;
    }
    
    .action-btn:hover {
        border-color: var(--secondary-color);
        transform: translateY(-5px);
        box-shadow: var(--shadow-medium);
        color: var(--secondary-color);
    }
    
    .action-btn i {
        font-size: 2rem;
        margin-bottom: 1rem;
        display: block;
    }
    
    .action-btn span {
        font-weight: 600;
        font-size: 1.1rem;
    }

    .text-purple {
        color: #9b59b6 !important;
    }

    .action-btn:hover .text-purple {
        color: #8e44ad !important;
    }
    
    .recent-activity {
        background: white;
        border-radius: var(--border-radius);
        box-shadow: var(--shadow-light);
        overflow: hidden;
    }
    
    .activity-header {
        background: var(--gradient-primary);
        color: white;
        padding: 1.5rem;
        font-weight: 600;
        font-size: 1.2rem;
    }
    
    .activity-list {
        max-height: 400px;
        overflow-y: auto;
    }
    
    .activity-item {
        padding: 1rem 1.5rem;
        border-bottom: 1px solid #f8f9fa;
        transition: background 0.3s ease;
        display: flex;
        align-items: center;
    }
    
    .activity-item:hover {
        background: #f8f9fa;
    }
    
    .activity-item:last-child {
        border-bottom: none;
    }
    
    .activity-icon {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background: var(--gradient-primary);
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-left: 1rem;
        flex-shrink: 0;
    }
    
    .activity-content {
        flex: 1;
    }
    
    .activity-title {
        font-weight: 600;
        color: var(--dark-color);
        margin-bottom: 0.25rem;
    }
    
    .activity-time {
        font-size: 0.9rem;
        color: #6c757d;
    }
    
    .empty-state {
        text-align: center;
        padding: 3rem;
        color: #6c757d;
    }
    
    .empty-state i {
        font-size: 4rem;
        margin-bottom: 1rem;
        opacity: 0.5;
    }
    
    @media (max-width: 768px) {
        .welcome-title {
            font-size: 2rem;
        }
        
        .welcome-subtitle {
            font-size: 1rem;
        }
        
        .stats-grid {
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        }
        
        .action-grid {
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        }
    }
</style>
{% endblock %}

{% block content %}
<!-- Welcome Section -->
<div class="welcome-section">
    <div class="welcome-content">
        <div class="row align-items-center">
            <div class="col-lg-8">
                <h1 class="welcome-title">
                    <i class="fas fa-user-circle me-3"></i>
                    مرحباً {{ current_user.full_name or current_user.username }}
                </h1>
                <p class="welcome-subtitle">
                    <i class="fas fa-calendar-alt me-2"></i>
                    اليوم: <span id="currentDate"></span>
                    <span class="mx-3">|</span>
                    <i class="fas fa-clock me-2"></i>
                    الوقت: <span id="currentTime"></span>
                </p>
                <div class="welcome-badges mt-3">
                    <span class="badge bg-light text-dark me-2">
                        <i class="fas fa-shield-alt me-1"></i>
                        {{ current_user.role }}
                    </span>
                    <span class="badge bg-light text-dark me-2">
                        <i class="fas fa-building me-1"></i>
                        {{ current_user.department or 'غير محدد' }}
                    </span>
                    <span class="badge bg-light text-dark">
                        <i class="fas fa-sign-in-alt me-1"></i>
                        آخر دخول: {{ current_user.last_login.strftime('%H:%M') if current_user.last_login else 'الآن' }}
                    </span>
                </div>
            </div>
            <div class="col-lg-4 text-center">
                <div class="welcome-avatar">
                    <div class="avatar-circle">
                        {{ (current_user.full_name or current_user.username)[0].upper() }}
                    </div>
                    <div class="status-indicator"></div>
                </div>
                <div class="quick-stats mt-3">
                    <div class="row text-center">
                        <div class="col-4">
                            <div class="mini-stat">
                                <div class="mini-number">{{ stats.total_documents }}</div>
                                <div class="mini-label">وثائق</div>
                            </div>
                        </div>
                        <div class="col-4">
                            <div class="mini-stat">
                                <div class="mini-number">{{ stats.total_incoming }}</div>
                                <div class="mini-label">واردة</div>
                            </div>
                        </div>
                        <div class="col-4">
                            <div class="mini-stat">
                                <div class="mini-number">{{ stats.total_outgoing }}</div>
                                <div class="mini-label">صادرة</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="stats-grid">
    <div class="stat-card" onclick="location.href='{{ url_for('documents') }}'">
        <div class="stat-icon">
            <i class="fas fa-folder"></i>
        </div>
        <div class="stat-number" data-count="{{ stats.total_documents }}">{{ stats.total_documents }}</div>
        <div class="stat-label">إجمالي الوثائق</div>
    </div>
    
    <div class="stat-card" onclick="location.href='{{ url_for('incoming_documents') }}'">
        <div class="stat-icon">
            <i class="fas fa-inbox"></i>
        </div>
        <div class="stat-number" data-count="{{ stats.total_incoming }}">{{ stats.total_incoming }}</div>
        <div class="stat-label">الكتب الواردة</div>
    </div>

    <div class="stat-card" onclick="location.href='{{ url_for('outgoing_documents') }}'">
        <div class="stat-icon">
            <i class="fas fa-paper-plane"></i>
        </div>
        <div class="stat-number" data-count="{{ stats.total_outgoing }}">{{ stats.total_outgoing }}</div>
        <div class="stat-label">الكتب الصادرة</div>
    </div>
    
    <div class="stat-card" onclick="showComingSoon()">
        <div class="stat-icon">
            <i class="fas fa-users"></i>
        </div>
        <div class="stat-number" data-count="1">1</div>
        <div class="stat-label">المستخدمين النشطين</div>
    </div>
</div>

<div class="row">
    <!-- Quick Actions -->
    <div class="col-lg-8 mb-4">
        <div class="quick-actions">
            <h2 class="section-title">
                <i class="fas fa-bolt"></i>
                إجراءات سريعة
            </h2>
            
            <div class="action-grid">
                <a href="{{ url_for('add_document') }}" class="action-btn">
                    <i class="fas fa-plus-circle text-success"></i>
                    <span>إضافة وثيقة جديدة</span>
                </a>

                <a href="{{ url_for('add_incoming') }}" class="action-btn">
                    <i class="fas fa-inbox text-primary"></i>
                    <span>تسجيل كتاب وارد</span>
                </a>

                <a href="{{ url_for('add_outgoing') }}" class="action-btn">
                    <i class="fas fa-paper-plane text-info"></i>
                    <span>إنشاء كتاب صادر</span>
                </a>

                <a href="{{ url_for('ocr_scanner') }}" class="action-btn">
                    <i class="fas fa-eye text-purple"></i>
                    <span>ماسح OCR المتقدم</span>
                </a>

                <a href="{{ url_for('digital_signatures_manager') }}" class="action-btn">
                    <i class="fas fa-signature text-success"></i>
                    <span>التوقيعات الرقمية</span>
                </a>

                <a href="{{ url_for('search') }}" class="action-btn">
                    <i class="fas fa-search text-warning"></i>
                    <span>البحث المتقدم</span>
                </a>

                <a href="{{ url_for('reports') }}" class="action-btn">
                    <i class="fas fa-chart-bar text-danger"></i>
                    <span>التقارير والإحصائيات</span>
                </a>

                <a href="{{ url_for('calendar') }}" class="action-btn">
                    <i class="fas fa-calendar-alt text-info"></i>
                    <span>التقويم الذكي</span>
                </a>

                {% if current_user.is_admin() %}
                <a href="{{ url_for('maintenance') }}" class="action-btn">
                    <i class="fas fa-tools text-dark"></i>
                    <span>صيانة النظام</span>
                </a>
                {% endif %}
            </div>
        </div>
    </div>
    
    <!-- Recent Activity -->
    <div class="col-lg-4 mb-4">
        <div class="recent-activity">
            <div class="activity-header">
                <i class="fas fa-clock me-2"></i>
                النشاط الأخير
            </div>
            
            <div class="activity-list">
                {% if stats.recent_documents or stats.recent_incoming or stats.recent_outgoing %}
                    {% for doc in stats.recent_documents[:3] %}
                    <div class="activity-item">
                        <div class="activity-icon">
                            <i class="fas fa-file"></i>
                        </div>
                        <div class="activity-content">
                            <div class="activity-title">{{ doc.title[:30] }}...</div>
                            <div class="activity-time">منذ {{ doc.created_at.strftime('%H:%M') }}</div>
                        </div>
                    </div>
                    {% endfor %}
                    
                    {% for inc in stats.recent_incoming[:2] %}
                    <div class="activity-item">
                        <div class="activity-icon">
                            <i class="fas fa-inbox"></i>
                        </div>
                        <div class="activity-content">
                            <div class="activity-title">{{ inc.subject[:30] }}...</div>
                            <div class="activity-time">منذ {{ inc.created_at.strftime('%H:%M') }}</div>
                        </div>
                    </div>
                    {% endfor %}
                    
                    {% for out in stats.recent_outgoing[:2] %}
                    <div class="activity-item">
                        <div class="activity-icon">
                            <i class="fas fa-paper-plane"></i>
                        </div>
                        <div class="activity-content">
                            <div class="activity-title">{{ out.subject[:30] }}...</div>
                            <div class="activity-time">منذ {{ out.created_at.strftime('%H:%M') }}</div>
                        </div>
                    </div>
                    {% endfor %}
                {% else %}
                    <div class="empty-state">
                        <i class="fas fa-inbox"></i>
                        <h5>لا يوجد نشاط حديث</h5>
                        <p>ابدأ بإضافة وثائق جديدة لرؤية النشاط هنا</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Charts Section -->
<div class="row mb-4">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-chart-line me-2"></i>
                الاتجاهات الشهرية
            </div>
            <div class="card-body">
                <canvas id="monthlyTrendsChart" height="100"></canvas>
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-chart-pie me-2"></i>
                توزيع الوثائق
            </div>
            <div class="card-body">
                <canvas id="documentsDistributionChart" height="200"></canvas>
            </div>
        </div>
    </div>
</div>

<div class="row mb-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-chart-bar me-2"></i>
                حالة الكتب الواردة
            </div>
            <div class="card-body">
                <canvas id="incomingStatusChart" height="150"></canvas>
            </div>
        </div>
    </div>

    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-chart-bar me-2"></i>
                حالة الكتب الصادرة
            </div>
            <div class="card-body">
                <canvas id="outgoingStatusChart" height="150"></canvas>
            </div>
        </div>
    </div>
</div>

<!-- System Status -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-info-circle me-2"></i>
                حالة النظام
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-md-3">
                        <div class="text-success">
                            <i class="fas fa-check-circle fa-2x mb-2"></i>
                            <h6>قاعدة البيانات</h6>
                            <small class="text-muted">متصلة وتعمل بشكل طبيعي</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-success">
                            <i class="fas fa-server fa-2x mb-2"></i>
                            <h6>الخادم</h6>
                            <small class="text-muted">يعمل بكفاءة عالية</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-success">
                            <i class="fas fa-shield-alt fa-2x mb-2"></i>
                            <h6>الأمان</h6>
                            <small class="text-muted">جميع الأنظمة آمنة</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-success">
                            <i class="fas fa-hdd fa-2x mb-2"></i>
                            <h6>التخزين</h6>
                            <small class="text-muted">مساحة كافية متوفرة</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.4/moment.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.4/locale/ar.min.js"></script>
<script>
    // Set moment locale to Arabic
    moment.locale('ar');
    
    // Animate counters
    function animateCounters() {
        const counters = document.querySelectorAll('.stat-number');
        
        counters.forEach(counter => {
            const target = parseInt(counter.getAttribute('data-count'));
            const increment = target / 50;
            let current = 0;
            
            const timer = setInterval(() => {
                current += increment;
                if (current >= target) {
                    counter.textContent = target;
                    clearInterval(timer);
                } else {
                    counter.textContent = Math.floor(current);
                }
            }, 30);
        });
    }
    
    // Add stagger animation to cards
    function animateCards() {
        const cards = document.querySelectorAll('.stat-card, .action-btn');
        
        cards.forEach((card, index) => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(20px)';
            
            setTimeout(() => {
                card.style.transition = 'all 0.6s ease';
                card.style.opacity = '1';
                card.style.transform = 'translateY(0)';
            }, index * 100);
        });
    }
    
    // Update date and time
    function updateDateTime() {
        const now = new Date();

        // Arabic day names
        const days = ['الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت'];
        const months = ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
                       'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'];

        const dayName = days[now.getDay()];
        const day = now.getDate();
        const month = months[now.getMonth()];
        const year = now.getFullYear();

        const hours = now.getHours().toString().padStart(2, '0');
        const minutes = now.getMinutes().toString().padStart(2, '0');

        const dateElement = document.getElementById('currentDate');
        const timeElement = document.getElementById('currentTime');

        if (dateElement) {
            dateElement.textContent = `${dayName}، ${day} ${month} ${year}`;
        }

        if (timeElement) {
            timeElement.textContent = `${hours}:${minutes}`;
        }
    }

    // Initialize charts and animations when page loads
    document.addEventListener('DOMContentLoaded', function() {
        animateCounters();
        animateCards();
        initializeCharts();
        updateDateTime();

        // Update time every minute
        setInterval(updateDateTime, 60000);
    });

    // Initialize all charts
    function initializeCharts() {
        // Monthly Trends Chart
        const monthlyCtx = document.getElementById('monthlyTrendsChart').getContext('2d');
        new Chart(monthlyCtx, {
            type: 'line',
            data: {
                labels: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو'],
                datasets: [{
                    label: 'الوثائق',
                    data: [{{ stats.total_documents or 0 }}, {{ (stats.total_documents or 0) * 0.8 }}, {{ (stats.total_documents or 0) * 1.2 }}, {{ (stats.total_documents or 0) * 0.9 }}, {{ (stats.total_documents or 0) * 1.1 }}, {{ stats.total_documents or 0 }}],
                    borderColor: '#3498db',
                    backgroundColor: 'rgba(52, 152, 219, 0.1)',
                    tension: 0.4,
                    fill: true
                }, {
                    label: 'الكتب الواردة',
                    data: [{{ stats.total_incoming or 0 }}, {{ (stats.total_incoming or 0) * 0.7 }}, {{ (stats.total_incoming or 0) * 1.3 }}, {{ (stats.total_incoming or 0) * 0.8 }}, {{ (stats.total_incoming or 0) * 1.2 }}, {{ stats.total_incoming or 0 }}],
                    borderColor: '#2ecc71',
                    backgroundColor: 'rgba(46, 204, 113, 0.1)',
                    tension: 0.4,
                    fill: true
                }, {
                    label: 'الكتب الصادرة',
                    data: [{{ stats.total_outgoing or 0 }}, {{ (stats.total_outgoing or 0) * 0.9 }}, {{ (stats.total_outgoing or 0) * 1.1 }}, {{ (stats.total_outgoing or 0) * 0.7 }}, {{ (stats.total_outgoing or 0) * 1.4 }}, {{ stats.total_outgoing or 0 }}],
                    borderColor: '#e74c3c',
                    backgroundColor: 'rgba(231, 76, 60, 0.1)',
                    tension: 0.4,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'top',
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });

        // Documents Distribution Chart
        const distributionCtx = document.getElementById('documentsDistributionChart').getContext('2d');
        new Chart(distributionCtx, {
            type: 'doughnut',
            data: {
                labels: ['الوثائق', 'الكتب الواردة', 'الكتب الصادرة'],
                datasets: [{
                    data: [{{ stats.total_documents or 0 }}, {{ stats.total_incoming or 0 }}, {{ stats.total_outgoing or 0 }}],
                    backgroundColor: ['#3498db', '#2ecc71', '#e74c3c'],
                    borderWidth: 2,
                    borderColor: '#fff'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                    }
                }
            }
        });

        // Incoming Status Chart
        const incomingCtx = document.getElementById('incomingStatusChart').getContext('2d');
        new Chart(incomingCtx, {
            type: 'bar',
            data: {
                labels: ['جديد', 'قيد المراجعة', 'مكتمل', 'مؤرشف'],
                datasets: [{
                    label: 'عدد الكتب',
                    data: [{{ (stats.total_incoming or 0) * 0.3 }}, {{ (stats.total_incoming or 0) * 0.4 }}, {{ (stats.total_incoming or 0) * 0.2 }}, {{ (stats.total_incoming or 0) * 0.1 }}],
                    backgroundColor: ['#f39c12', '#3498db', '#2ecc71', '#95a5a6'],
                    borderRadius: 5
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });

        // Outgoing Status Chart
        const outgoingCtx = document.getElementById('outgoingStatusChart').getContext('2d');
        new Chart(outgoingCtx, {
            type: 'bar',
            data: {
                labels: ['مسودة', 'جاهز للإرسال', 'تم الإرسال', 'مؤرشف'],
                datasets: [{
                    label: 'عدد الكتب',
                    data: [{{ (stats.total_outgoing or 0) * 0.2 }}, {{ (stats.total_outgoing or 0) * 0.3 }}, {{ (stats.total_outgoing or 0) * 0.4 }}, {{ (stats.total_outgoing or 0) * 0.1 }}],
                    backgroundColor: ['#e67e22', '#f39c12', '#2ecc71', '#95a5a6'],
                    borderRadius: 5
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
    }
    
    // Add click effects to stat cards
    document.querySelectorAll('.stat-card').forEach(card => {
        card.addEventListener('click', function() {
            this.style.transform = 'scale(0.95)';
            setTimeout(() => {
                this.style.transform = '';
            }, 150);
        });
    });
</script>
{% endblock %}
