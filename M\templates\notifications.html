{% extends "base.html" %}

{% block title %}الإشعارات - نظام إدارة الأرشيف العام{% endblock %}

{% block extra_css %}
<style>
    .notifications-header {
        background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
        color: white;
        border-radius: var(--border-radius);
        padding: 2rem;
        margin-bottom: 2rem;
        text-align: center;
    }
    
    .notifications-title {
        font-size: 2rem;
        font-weight: 700;
        margin: 0;
    }
    
    .notifications-subtitle {
        opacity: 0.9;
        margin: 0.5rem 0 0 0;
    }
    
    .notifications-container {
        background: white;
        border-radius: var(--border-radius);
        box-shadow: var(--shadow-light);
        overflow: hidden;
    }
    
    .notification-item {
        padding: 1.5rem;
        border-bottom: 1px solid #e9ecef;
        transition: all 0.3s ease;
        position: relative;
    }
    
    .notification-item:last-child {
        border-bottom: none;
    }
    
    .notification-item:hover {
        background: #f8f9fa;
    }
    
    .notification-item.unread {
        background: #fff3cd;
        border-left: 4px solid #f39c12;
    }
    
    .notification-item.unread:hover {
        background: #ffeaa7;
    }
    
    .notification-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 0.5rem;
    }
    
    .notification-title {
        font-weight: 700;
        color: #2c3e50;
        margin: 0;
        flex: 1;
    }
    
    .notification-time {
        font-size: 0.875rem;
        color: #6c757d;
        white-space: nowrap;
        margin-right: 1rem;
    }
    
    .notification-message {
        color: #495057;
        margin-bottom: 1rem;
        line-height: 1.6;
    }
    
    .notification-type {
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.25rem 0.75rem;
        border-radius: 20px;
        font-size: 0.875rem;
        font-weight: 600;
    }
    
    .notification-type.info {
        background: #d1ecf1;
        color: #0c5460;
    }
    
    .notification-type.success {
        background: #d4edda;
        color: #155724;
    }
    
    .notification-type.warning {
        background: #fff3cd;
        color: #856404;
    }
    
    .notification-type.error {
        background: #f8d7da;
        color: #721c24;
    }
    
    .notification-actions {
        display: flex;
        gap: 0.5rem;
        margin-top: 1rem;
    }
    
    .btn-mark-read {
        background: #28a745;
        color: white;
        border: none;
        padding: 0.375rem 0.75rem;
        border-radius: 15px;
        font-size: 0.875rem;
        cursor: pointer;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 0.25rem;
    }
    
    .btn-mark-read:hover {
        background: #218838;
        color: white;
        transform: translateY(-1px);
    }
    
    .empty-notifications {
        text-align: center;
        padding: 4rem 2rem;
        color: #6c757d;
    }
    
    .empty-notifications i {
        font-size: 4rem;
        margin-bottom: 1rem;
        opacity: 0.5;
    }
    
    .stats-row {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
        margin-bottom: 2rem;
    }
    
    .stat-card {
        background: white;
        border-radius: var(--border-radius);
        padding: 1.5rem;
        text-align: center;
        box-shadow: var(--shadow-light);
        transition: all 0.3s ease;
    }
    
    .stat-card:hover {
        transform: translateY(-5px);
        box-shadow: var(--shadow-medium);
    }
    
    .stat-icon {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 1rem;
        font-size: 1.5rem;
        color: white;
    }
    
    .stat-icon.total {
        background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
    }
    
    .stat-icon.unread {
        background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
    }
    
    .stat-icon.read {
        background: linear-gradient(135deg, #2ecc71 0%, #27ae60 100%);
    }
    
    .stat-number {
        font-size: 2rem;
        font-weight: 700;
        color: #2c3e50;
        margin-bottom: 0.5rem;
    }
    
    .stat-label {
        color: #7f8c8d;
        font-weight: 600;
    }
    
    .mark-all-read {
        background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
        color: white;
        border: none;
        padding: 0.75rem 1.5rem;
        border-radius: var(--border-radius);
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        margin-bottom: 2rem;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
    }
    
    .mark-all-read:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(52, 152, 219, 0.3);
        color: white;
    }
    
    @media (max-width: 768px) {
        .notification-header {
            flex-direction: column;
            align-items: flex-start;
        }
        
        .notification-time {
            margin-right: 0;
            margin-top: 0.5rem;
        }
        
        .stats-row {
            grid-template-columns: repeat(2, 1fr);
        }
        
        .notification-actions {
            flex-direction: column;
        }
    }
</style>
{% endblock %}

{% block content %}
<!-- Header -->
<div class="notifications-header">
    <h1 class="notifications-title">
        <i class="fas fa-bell me-3"></i>
        الإشعارات
    </h1>
    <p class="notifications-subtitle">تابع جميع التحديثات والأنشطة المهمة</p>
</div>

<!-- Statistics -->
<div class="stats-row">
    <div class="stat-card">
        <div class="stat-icon total">
            <i class="fas fa-bell"></i>
        </div>
        <div class="stat-number">{{ notifications.total }}</div>
        <div class="stat-label">إجمالي الإشعارات</div>
    </div>
    
    <div class="stat-card">
        <div class="stat-icon unread">
            <i class="fas fa-bell-slash"></i>
        </div>
        <div class="stat-number">{{ notifications.items | selectattr('is_read', 'equalto', false) | list | length }}</div>
        <div class="stat-label">غير مقروءة</div>
    </div>
    
    <div class="stat-card">
        <div class="stat-icon read">
            <i class="fas fa-check-circle"></i>
        </div>
        <div class="stat-number">{{ notifications.items | selectattr('is_read', 'equalto', true) | list | length }}</div>
        <div class="stat-label">مقروءة</div>
    </div>
</div>

<!-- Mark All Read Button -->
{% if notifications.items | selectattr('is_read', 'equalto', false) | list | length > 0 %}
<button class="mark-all-read" onclick="markAllAsRead()">
    <i class="fas fa-check-double"></i>
    تمييز الكل كمقروء
</button>
{% endif %}

<!-- Notifications List -->
<div class="notifications-container">
    {% if notifications.items %}
        {% for notification in notifications.items %}
            <div class="notification-item {{ 'unread' if not notification.is_read else '' }}">
                <div class="notification-header">
                    <h5 class="notification-title">{{ notification.title }}</h5>
                    <span class="notification-time">{{ notification.created_at.strftime('%Y/%m/%d %H:%M') }}</span>
                </div>
                
                <div class="notification-message">{{ notification.message }}</div>
                
                <div class="d-flex justify-content-between align-items-center">
                    <span class="notification-type {{ notification.type }}">
                        {% if notification.type == 'info' %}
                            <i class="fas fa-info-circle"></i>
                            معلومات
                        {% elif notification.type == 'success' %}
                            <i class="fas fa-check-circle"></i>
                            نجح
                        {% elif notification.type == 'warning' %}
                            <i class="fas fa-exclamation-triangle"></i>
                            تحذير
                        {% elif notification.type == 'error' %}
                            <i class="fas fa-times-circle"></i>
                            خطأ
                        {% endif %}
                    </span>
                    
                    {% if not notification.is_read %}
                        <div class="notification-actions">
                            <a href="{{ url_for('mark_notification_read', notification_id=notification.id) }}" 
                               class="btn-mark-read">
                                <i class="fas fa-check"></i>
                                تمييز كمقروء
                            </a>
                        </div>
                    {% endif %}
                </div>
            </div>
        {% endfor %}
        
        <!-- Pagination -->
        {% if notifications.pages > 1 %}
            <div class="d-flex justify-content-center p-3">
                <nav>
                    <ul class="pagination">
                        {% if notifications.has_prev %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('notifications', page=notifications.prev_num) }}">السابق</a>
                            </li>
                        {% endif %}
                        
                        {% for page_num in notifications.iter_pages() %}
                            {% if page_num %}
                                {% if page_num != notifications.page %}
                                    <li class="page-item">
                                        <a class="page-link" href="{{ url_for('notifications', page=page_num) }}">{{ page_num }}</a>
                                    </li>
                                {% else %}
                                    <li class="page-item active">
                                        <span class="page-link">{{ page_num }}</span>
                                    </li>
                                {% endif %}
                            {% else %}
                                <li class="page-item disabled">
                                    <span class="page-link">…</span>
                                </li>
                            {% endif %}
                        {% endfor %}
                        
                        {% if notifications.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('notifications', page=notifications.next_num) }}">التالي</a>
                            </li>
                        {% endif %}
                    </ul>
                </nav>
            </div>
        {% endif %}
    {% else %}
        <div class="empty-notifications">
            <i class="fas fa-bell-slash"></i>
            <h4>لا توجد إشعارات</h4>
            <p>لم تتلق أي إشعارات بعد</p>
        </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script>
    function markAllAsRead() {
        if (confirm('هل أنت متأكد من تمييز جميع الإشعارات كمقروءة؟')) {
            // يمكن إضافة API endpoint لتمييز جميع الإشعارات كمقروءة
            const unreadNotifications = document.querySelectorAll('.notification-item.unread');
            unreadNotifications.forEach(notification => {
                const markReadBtn = notification.querySelector('.btn-mark-read');
                if (markReadBtn) {
                    window.location.href = markReadBtn.href;
                }
            });
        }
    }
    
    // تحسين تجربة المستخدم
    document.addEventListener('DOMContentLoaded', function() {
        // إضافة تأثيرات للإشعارات
        const notifications = document.querySelectorAll('.notification-item');
        notifications.forEach((notification, index) => {
            setTimeout(() => {
                notification.style.opacity = '0';
                notification.style.transform = 'translateX(20px)';
                notification.style.transition = 'all 0.6s ease';
                
                setTimeout(() => {
                    notification.style.opacity = '1';
                    notification.style.transform = 'translateX(0)';
                }, 50);
            }, index * 100);
        });
    });
</script>
{% endblock %}
