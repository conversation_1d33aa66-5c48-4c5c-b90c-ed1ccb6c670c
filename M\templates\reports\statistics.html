{% extends "base.html" %}

{% block title %}الإحصائيات المتقدمة - نظام إدارة الأرشيف العام{% endblock %}

{% block extra_css %}
<style>
    .statistics-header {
        background: linear-gradient(135deg, #8e44ad 0%, #9b59b6 100%);
        color: white;
        border-radius: var(--border-radius);
        padding: 2rem;
        margin-bottom: 2rem;
        position: relative;
        overflow: hidden;
    }
    
    .statistics-header::before {
        content: '';
        position: absolute;
        top: -50%;
        right: -50%;
        width: 200%;
        height: 200%;
        background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
        animation: float 6s ease-in-out infinite;
    }
    
    .statistics-content {
        position: relative;
        z-index: 2;
    }
    
    .overview-cards {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1.5rem;
        margin-bottom: 3rem;
    }
    
    .overview-card {
        background: white;
        border-radius: var(--border-radius);
        padding: 2rem;
        box-shadow: var(--shadow-light);
        text-align: center;
        transition: all 0.3s ease;
        border-left: 4px solid var(--primary-color);
    }
    
    .overview-card:hover {
        transform: translateY(-5px);
        box-shadow: var(--shadow-medium);
    }
    
    .overview-card.documents {
        border-left-color: #3498db;
    }
    
    .overview-card.incoming {
        border-left-color: #27ae60;
    }
    
    .overview-card.outgoing {
        border-left-color: #e67e22;
    }
    
    .overview-card.users {
        border-left-color: #9b59b6;
    }
    
    .card-icon {
        font-size: 3rem;
        margin-bottom: 1rem;
        opacity: 0.8;
    }
    
    .card-number {
        font-size: 2.5rem;
        font-weight: 700;
        color: var(--primary-color);
        margin-bottom: 0.5rem;
    }
    
    .card-label {
        font-size: 1.1rem;
        color: #6c757d;
        font-weight: 600;
    }
    
    .charts-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 2rem;
        margin-bottom: 3rem;
    }
    
    .chart-card {
        background: white;
        border-radius: var(--border-radius);
        padding: 2rem;
        box-shadow: var(--shadow-light);
    }
    
    .chart-title {
        font-size: 1.25rem;
        font-weight: 600;
        color: var(--primary-color);
        margin-bottom: 1.5rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }
    
    .chart-container {
        height: 300px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: #f8f9fa;
        border-radius: var(--border-radius);
        color: #6c757d;
    }
    
    .trends-table {
        background: white;
        border-radius: var(--border-radius);
        box-shadow: var(--shadow-light);
        overflow: hidden;
        margin-bottom: 3rem;
    }
    
    .table-header {
        background: #f8f9fa;
        padding: 1.5rem;
        border-bottom: 1px solid #dee2e6;
    }
    
    .table-title {
        font-size: 1.25rem;
        font-weight: 600;
        color: var(--primary-color);
        margin: 0;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }
    
    .table {
        margin: 0;
    }
    
    .table th {
        background: #f8f9fa;
        border-top: none;
        font-weight: 600;
        color: var(--primary-color);
    }
    
    .table td {
        vertical-align: middle;
    }
    
    .trend-up {
        color: #27ae60;
    }
    
    .trend-down {
        color: #e74c3c;
    }
    
    .trend-stable {
        color: #6c757d;
    }
    
    .top-users {
        background: white;
        border-radius: var(--border-radius);
        box-shadow: var(--shadow-light);
        padding: 2rem;
    }
    
    .user-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 1rem 0;
        border-bottom: 1px solid #f8f9fa;
    }
    
    .user-item:last-child {
        border-bottom: none;
    }
    
    .user-info {
        display: flex;
        align-items: center;
        gap: 1rem;
    }
    
    .user-avatar {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background: var(--primary-color);
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 600;
    }
    
    .user-details h6 {
        margin: 0;
        color: var(--primary-color);
    }
    
    .user-details small {
        color: #6c757d;
    }
    
    .user-stats {
        text-align: right;
    }
    
    .activity-score {
        font-size: 1.25rem;
        font-weight: 700;
        color: var(--primary-color);
    }
    
    .activity-label {
        font-size: 0.875rem;
        color: #6c757d;
    }
    
    @media (max-width: 768px) {
        .charts-grid {
            grid-template-columns: 1fr;
        }
        
        .overview-cards {
            grid-template-columns: 1fr;
        }
    }
</style>
{% endblock %}

{% block content %}
<!-- Statistics Header -->
<div class="statistics-header">
    <div class="statistics-content">
        <h1 class="mb-3">
            <i class="fas fa-chart-line me-3"></i>
            الإحصائيات المتقدمة
        </h1>
        <p class="mb-0 opacity-75">
            تحليل شامل لأداء النظام والاتجاهات والأنماط
        </p>
    </div>
</div>

<!-- Overview Cards -->
<div class="overview-cards">
    <div class="overview-card documents">
        <div class="card-icon">
            <i class="fas fa-file-alt" style="color: #3498db;"></i>
        </div>
        <div class="card-number">{{ stats.overview.total_documents }}</div>
        <div class="card-label">إجمالي الوثائق</div>
    </div>
    
    <div class="overview-card incoming">
        <div class="card-icon">
            <i class="fas fa-inbox" style="color: #27ae60;"></i>
        </div>
        <div class="card-number">{{ stats.overview.total_incoming }}</div>
        <div class="card-label">الكتب الواردة</div>
    </div>
    
    <div class="overview-card outgoing">
        <div class="card-icon">
            <i class="fas fa-paper-plane" style="color: #e67e22;"></i>
        </div>
        <div class="card-number">{{ stats.overview.total_outgoing }}</div>
        <div class="card-label">الكتب الصادرة</div>
    </div>
    
    <div class="overview-card users">
        <div class="card-icon">
            <i class="fas fa-users" style="color: #9b59b6;"></i>
        </div>
        <div class="card-number">{{ stats.overview.active_users }}</div>
        <div class="card-label">المستخدمون النشطون</div>
    </div>
</div>

<!-- Charts Grid -->
<div class="charts-grid">
    <div class="chart-card">
        <h3 class="chart-title">
            <i class="fas fa-chart-pie"></i>
            توزيع الوثائق حسب النوع
        </h3>
        <div class="chart-container">
            <div class="text-center">
                <i class="fas fa-chart-pie fa-3x mb-3"></i>
                <p>الرسم البياني سيتم تطويره لاحقاً</p>
                <small class="text-muted">
                    {% for type, count in stats.by_type.items() %}
                    {{ type }}: {{ count }}<br>
                    {% endfor %}
                </small>
            </div>
        </div>
    </div>
    
    <div class="chart-card">
        <h3 class="chart-title">
            <i class="fas fa-chart-bar"></i>
            الاتجاهات الشهرية
        </h3>
        <div class="chart-container">
            <div class="text-center">
                <i class="fas fa-chart-line fa-3x mb-3"></i>
                <p>الرسم البياني سيتم تطويره لاحقاً</p>
                <small class="text-muted">عرض الاتجاهات الشهرية للنشاط</small>
            </div>
        </div>
    </div>
</div>

<!-- Monthly Trends Table -->
<div class="trends-table">
    <div class="table-header">
        <h3 class="table-title">
            <i class="fas fa-calendar-alt"></i>
            الاتجاهات الشهرية
        </h3>
    </div>
    
    <div class="table-responsive">
        <table class="table table-striped">
            <thead>
                <tr>
                    <th>الشهر</th>
                    <th>الوثائق</th>
                    <th>الكتب الواردة</th>
                    <th>الكتب الصادرة</th>
                    <th>إجمالي النشاط</th>
                </tr>
            </thead>
            <tbody>
                {% for trend in stats.monthly_trends %}
                <tr>
                    <td>{{ trend.month_name }} {{ trend.year }}</td>
                    <td>{{ trend.documents }}</td>
                    <td>{{ trend.incoming }}</td>
                    <td>{{ trend.outgoing }}</td>
                    <td><strong>{{ trend.documents + trend.incoming + trend.outgoing }}</strong></td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
</div>

<!-- Top Users -->
<div class="top-users">
    <h3 class="chart-title">
        <i class="fas fa-trophy"></i>
        أكثر المستخدمين نشاطاً
    </h3>
    
    {% for user_stat in stats.top_users %}
    <div class="user-item">
        <div class="user-info">
            <div class="user-avatar">
                {{ user_stat.user.full_name[0] }}
            </div>
            <div class="user-details">
                <h6>{{ user_stat.user.full_name }}</h6>
                <small>{{ user_stat.user.department or 'غير محدد' }}</small>
            </div>
        </div>
        <div class="user-stats">
            <div class="activity-score">{{ user_stat.total_activity }}</div>
            <div class="activity-label">إجمالي النشاط</div>
        </div>
    </div>
    {% endfor %}
</div>

<div class="text-center mt-4">
    <a href="{{ url_for('reports') }}" class="btn btn-outline-primary">
        <i class="fas fa-arrow-right me-2"></i>
        العودة للتقارير
    </a>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Future: Add Chart.js integration here
    console.log('Statistics page loaded');
</script>
{% endblock %}
