{% extends "base.html" %}

{% block title %}خطأ في الخادم - نظام إدارة الأرشيف العام{% endblock %}

{% block extra_css %}
<style>
    .error-container {
        min-height: 70vh;
        display: flex;
        align-items: center;
        justify-content: center;
        text-align: center;
    }
    
    .error-content {
        max-width: 600px;
        padding: 2rem;
    }
    
    .error-icon {
        font-size: 8rem;
        color: #e74c3c;
        margin-bottom: 2rem;
        animation: shake 1s infinite;
    }
    
    @keyframes shake {
        0%, 100% { transform: translateX(0); }
        10%, 30%, 50%, 70%, 90% { transform: translateX(-10px); }
        20%, 40%, 60%, 80% { transform: translateX(10px); }
    }
    
    .error-title {
        font-size: 3rem;
        font-weight: 700;
        color: #2c3e50;
        margin-bottom: 1rem;
    }
    
    .error-message {
        font-size: 1.2rem;
        color: #7f8c8d;
        margin-bottom: 2rem;
        line-height: 1.6;
    }
    
    .error-actions {
        display: flex;
        gap: 1rem;
        justify-content: center;
        flex-wrap: wrap;
    }
    
    .btn-error {
        padding: 0.75rem 2rem;
        border-radius: 25px;
        font-weight: 600;
        text-decoration: none;
        transition: all 0.3s ease;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
    }
    
    .btn-primary-error {
        background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
        color: white;
        border: none;
    }
    
    .btn-primary-error:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(231, 76, 60, 0.3);
        color: white;
    }
    
    .btn-secondary-error {
        background: #ecf0f1;
        color: #2c3e50;
        border: 2px solid #bdc3c7;
    }
    
    .btn-secondary-error:hover {
        background: #d5dbdb;
        color: #2c3e50;
        transform: translateY(-2px);
    }
    
    .error-details {
        background: #fff5f5;
        border: 1px solid #fed7d7;
        border-radius: 10px;
        padding: 1.5rem;
        margin-top: 2rem;
        text-align: right;
    }
    
    .error-details h6 {
        color: #e53e3e;
        margin-bottom: 1rem;
    }
    
    .error-details p {
        color: #742a2a;
        margin: 0;
        font-size: 0.9rem;
    }
    
    .support-info {
        background: #f0f8ff;
        border: 1px solid #bee3f8;
        border-radius: 10px;
        padding: 1.5rem;
        margin-top: 2rem;
        text-align: right;
    }
    
    .support-info h6 {
        color: #2b6cb0;
        margin-bottom: 1rem;
    }
    
    .support-info p {
        color: #2c5282;
        margin: 0;
        font-size: 0.9rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="error-container">
    <div class="error-content">
        <div class="error-icon">
            <i class="fas fa-exclamation-triangle"></i>
        </div>
        
        <h1 class="error-title">500</h1>
        
        <p class="error-message">
            عذراً، حدث خطأ داخلي في الخادم.<br>
            نحن نعمل على حل هذه المشكلة. يرجى المحاولة مرة أخرى لاحقاً.
        </p>
        
        <div class="error-actions">
            <a href="{{ url_for('dashboard') }}" class="btn-error btn-primary-error">
                <i class="fas fa-home"></i>
                العودة للرئيسية
            </a>
            
            <button onclick="location.reload()" class="btn-error btn-secondary-error">
                <i class="fas fa-redo"></i>
                إعادة المحاولة
            </button>
        </div>
        
        <div class="error-details">
            <h6><i class="fas fa-info-circle me-2"></i>تفاصيل الخطأ:</h6>
            <p>
                حدث خطأ غير متوقع أثناء معالجة طلبك. تم تسجيل هذا الخطأ تلقائياً 
                وسيتم مراجعته من قبل فريق الدعم التقني.
            </p>
        </div>
        
        <div class="support-info">
            <h6><i class="fas fa-headset me-2"></i>هل تحتاج مساعدة؟</h6>
            <p>
                إذا استمرت هذه المشكلة، يرجى التواصل مع مدير النظام وتقديم 
                الوقت الذي حدث فيه الخطأ والإجراء الذي كنت تحاول تنفيذه.
            </p>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // إضافة تأثيرات تفاعلية
    document.addEventListener('DOMContentLoaded', function() {
        // تأثير الظهور التدريجي
        const content = document.querySelector('.error-content');
        content.style.opacity = '0';
        content.style.transform = 'translateY(30px)';
        content.style.transition = 'all 0.6s ease';
        
        setTimeout(() => {
            content.style.opacity = '1';
            content.style.transform = 'translateY(0)';
        }, 100);
        
        // تسجيل الخطأ للمراقبة
        console.error('خطأ 500: خطأ داخلي في الخادم');
        
        // إرسال تقرير خطأ (اختياري)
        if (navigator.sendBeacon) {
            const errorData = {
                error_type: '500',
                timestamp: new Date().toISOString(),
                url: window.location.href,
                user_agent: navigator.userAgent
            };
            
            // يمكن إرسال هذا لنظام مراقبة الأخطاء
            // navigator.sendBeacon('/api/error-report', JSON.stringify(errorData));
        }
    });
</script>
{% endblock %}
