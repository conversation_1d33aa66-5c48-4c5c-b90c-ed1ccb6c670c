{% extends "base.html" %}

{% block title %}صيانة النظام الشاملة - نظام إدارة الأرشيف العام{% endblock %}

{% block extra_css %}
<style>
    body {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        font-family: 'Cairo', sans-serif;
    }

    .maintenance-container {
        max-width: 1400px;
        margin: 2rem auto;
        padding: 0 1rem;
    }

    .maintenance-header {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        border-radius: 20px;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        text-align: center;
    }

    .maintenance-header h1 {
        color: #2c3e50;
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 0.5rem;
    }

    .maintenance-header p {
        color: #6c757d;
        font-size: 1.1rem;
        margin: 0;
    }

    .health-banner {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        border-radius: 20px;
        padding: 1.5rem;
        margin-bottom: 2rem;
        box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        text-align: center;
    }

    .health-status {
        display: inline-flex;
        align-items: center;
        gap: 1rem;
        padding: 1rem 2rem;
        border-radius: 50px;
        font-weight: 600;
        font-size: 1.1rem;
    }

    .health-healthy {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        color: white;
    }

    .health-warning {
        background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
        color: #212529;
    }

    .health-critical {
        background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
        color: white;
    }

    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1.5rem;
        margin-bottom: 2rem;
    }

    .stat-card {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        border-radius: 15px;
        padding: 1.5rem;
        box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        transition: transform 0.3s ease;
        text-align: center;
    }

    .stat-card:hover {
        transform: translateY(-5px);
    }

    .stat-card .icon {
        font-size: 2.5rem;
        margin-bottom: 1rem;
        display: block;
    }

    .stat-card .number {
        font-size: 2rem;
        font-weight: 700;
        color: #2c3e50;
        margin-bottom: 0.5rem;
    }

    .stat-card .label {
        color: #6c757d;
        font-size: 0.9rem;
        margin: 0;
    }

    .maintenance-content {
        display: grid;
        grid-template-columns: 2fr 1fr;
        gap: 2rem;
        margin-bottom: 2rem;
    }

    .action-section {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        border-radius: 20px;
        padding: 2rem;
        box-shadow: 0 20px 40px rgba(0,0,0,0.1);
    }

    .section-header {
        display: flex;
        align-items: center;
        gap: 1rem;
        margin-bottom: 1.5rem;
        padding-bottom: 1rem;
        border-bottom: 2px solid #f8f9fa;
    }

    .section-icon {
        width: 50px;
        height: 50px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1.3rem;
    }

    .section-title {
        color: #2c3e50;
        font-size: 1.3rem;
        font-weight: 600;
        margin: 0;
    }

    .icon-maintenance { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
    .icon-health { background: linear-gradient(135deg, #28a745 0%, #20c997 100%); }

    .actions-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 1rem;
    }

    .action-card {
        background: #f8f9fa;
        border-radius: 12px;
        padding: 1.5rem;
        transition: all 0.3s ease;
        border: 2px solid transparent;
    }

    .action-card:hover {
        background: #e9ecef;
        border-color: #667eea;
        transform: translateY(-2px);
    }

    .action-card h4 {
        color: #2c3e50;
        margin-bottom: 0.75rem;
        font-size: 1rem;
        font-weight: 600;
    }

    .action-card p {
        color: #6c757d;
        margin-bottom: 1rem;
        font-size: 0.85rem;
        line-height: 1.4;
    }

    .action-btn {
        width: 100%;
        padding: 0.75rem;
        border: none;
        border-radius: 8px;
        font-weight: 600;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        gap: 0.5rem;
        transition: all 0.3s ease;
        cursor: pointer;
        font-size: 0.9rem;
    }

    .btn-primary { background: #667eea; color: white; }
    .btn-success { background: #28a745; color: white; }
    .btn-warning { background: #ffc107; color: #212529; }
    .btn-danger { background: #dc3545; color: white; }
    .btn-info { background: #17a2b8; color: white; }

    .action-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        color: inherit;
        text-decoration: none;
    }

    .sidebar {
        display: grid;
        gap: 1.5rem;
    }

    .sidebar-section {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        border-radius: 20px;
        padding: 1.5rem;
        box-shadow: 0 20px 40px rgba(0,0,0,0.1);
    }

    .sidebar-section h3 {
        color: #2c3e50;
        font-size: 1.1rem;
        font-weight: 600;
        margin-bottom: 1rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .info-list {
        display: grid;
        gap: 0.75rem;
    }

    .info-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0.75rem;
        background: #f8f9fa;
        border-radius: 8px;
        border-left: 4px solid #667eea;
    }

    .info-label {
        font-weight: 600;
        color: #2c3e50;
        font-size: 0.85rem;
    }

    .info-value {
        color: #6c757d;
        font-size: 0.85rem;
        text-align: left;
    }

    .health-checks {
        display: grid;
        gap: 0.5rem;
    }

    .health-check {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        padding: 0.75rem;
        background: #f8f9fa;
        border-radius: 8px;
        font-size: 0.85rem;
    }

    .check-status {
        width: 12px;
        height: 12px;
        border-radius: 50%;
        flex-shrink: 0;
    }

    .status-healthy { background: #28a745; }
    .status-warning { background: #ffc107; }
    .status-critical { background: #dc3545; }

    .check-name {
        flex: 1;
        font-weight: 500;
        color: #2c3e50;
    }

    @media (max-width: 768px) {
        .maintenance-container {
            margin: 1rem;
            padding: 0;
        }

        .maintenance-content {
            grid-template-columns: 1fr;
        }

        .stats-grid {
            grid-template-columns: repeat(2, 1fr);
        }

        .actions-grid {
            grid-template-columns: 1fr;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="maintenance-container">
    <!-- Header -->
    <div class="maintenance-header">
        <h1>
            <i class="fas fa-tools me-3"></i>
            صيانة النظام الشاملة
        </h1>
        <p>إدارة وصيانة شاملة لنظام إدارة الأرشيف العام</p>
    </div>

    <!-- Health Status Banner -->
    <div class="health-banner">
        <div class="health-status health-{{ health_check.overall_status or 'healthy' }}">
            {% if health_check.overall_status == 'healthy' %}
                <i class="fas fa-check-circle"></i>
                النظام يعمل بحالة ممتازة
            {% elif health_check.overall_status == 'warning' %}
                <i class="fas fa-exclamation-triangle"></i>
                النظام يحتاج لصيانة
            {% elif health_check.overall_status == 'critical' %}
                <i class="fas fa-times-circle"></i>
                النظام يحتاج لتدخل فوري
            {% else %}
                <i class="fas fa-question-circle"></i>
                حالة النظام غير محددة
            {% endif %}
        </div>
    </div>

    <!-- System Statistics -->
    <div class="stats-grid">
        <div class="stat-card">
            <i class="fas fa-file-alt icon" style="color: #667eea;"></i>
            <div class="number">{{ stats.total_documents or 0 }}</div>
            <div class="label">إجمالي الوثائق</div>
        </div>
        <div class="stat-card">
            <i class="fas fa-users icon" style="color: #28a745;"></i>
            <div class="number">{{ stats.total_users or 0 }}</div>
            <div class="label">المستخدمون</div>
        </div>
        <div class="stat-card">
            <i class="fas fa-history icon" style="color: #17a2b8;"></i>
            <div class="number">{{ stats.total_logs or 0 }}</div>
            <div class="label">سجلات الأنشطة</div>
        </div>
        <div class="stat-card">
            <i class="fas fa-archive icon" style="color: #6f42c1;"></i>
            <div class="number">{{ stats.archived_documents or 0 }}</div>
            <div class="label">وثائق مؤرشفة</div>
        </div>
        <div class="stat-card">
            <i class="fas fa-hdd icon" style="color: #ffc107;"></i>
            <div class="number">{{ stats.database_size or 'غير محدد' }}</div>
            <div class="label">حجم قاعدة البيانات</div>
        </div>
        <div class="stat-card">
            <i class="fas fa-folder icon" style="color: #fd7e14;"></i>
            <div class="number">{{ stats.upload_folder_size or 'غير محدد' }}</div>
            <div class="label">حجم الملفات</div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="maintenance-content">
        <!-- Main Actions -->
        <div class="main-actions">
            <!-- Automatic Maintenance -->
            <div class="action-section">
                <div class="section-header">
                    <div class="section-icon icon-maintenance">
                        <i class="fas fa-robot"></i>
                    </div>
                    <h2 class="section-title">الصيانة التلقائية</h2>
                </div>

                <div class="actions-grid">
                    <div class="action-card">
                        <h4>الأرشفة التلقائية</h4>
                        <p>أرشفة الوثائق القديمة تلقائياً حسب السياسات المحددة</p>
                        <form method="POST" action="{{ url_for('auto_archive') }}" style="margin: 0;">
                            <button type="submit" class="action-btn btn-primary">
                                <i class="fas fa-archive"></i>
                                تشغيل الأرشفة التلقائية
                            </button>
                        </form>
                    </div>

                    <div class="action-card">
                        <h4>تنظيف السجلات</h4>
                        <p>حذف سجلات الأنشطة القديمة لتحسين الأداء</p>
                        <form method="POST" action="{{ url_for('cleanup_logs') }}" style="margin: 0;">
                            <button type="submit" class="action-btn btn-warning">
                                <i class="fas fa-broom"></i>
                                تنظيف السجلات
                            </button>
                        </form>
                    </div>

                    <div class="action-card">
                        <h4>النسخ الاحتياطي</h4>
                        <p>إنشاء نسخة احتياطية شاملة من النظام</p>
                        <form method="POST" action="{{ url_for('create_backup') }}" style="margin: 0;">
                            <button type="submit" class="action-btn btn-success">
                                <i class="fas fa-download"></i>
                                إنشاء نسخة احتياطية
                            </button>
                        </form>
                    </div>

                    <div class="action-card">
                        <h4>تحسين قاعدة البيانات</h4>
                        <p>تحسين أداء قاعدة البيانات وإعادة فهرسة الجداول</p>
                        <form method="POST" action="{{ url_for('optimize_database') }}" style="margin: 0;">
                            <button type="submit" class="action-btn btn-info">
                                <i class="fas fa-database"></i>
                                تحسين قاعدة البيانات
                            </button>
                        </form>
                    </div>

                    <div class="action-card">
                        <h4>تنظيف الملفات اليتيمة</h4>
                        <p>حذف الملفات التي لا ترتبط بوثائق في قاعدة البيانات</p>
                        <form method="POST" action="{{ url_for('cleanup_orphaned_files') }}" style="margin: 0;">
                            <button type="submit" class="action-btn btn-warning">
                                <i class="fas fa-trash"></i>
                                تنظيف الملفات اليتيمة
                            </button>
                        </form>
                    </div>

                    <div class="action-card">
                        <h4>إعادة تشغيل النظام</h4>
                        <p>إعادة تشغيل خدمات النظام لتحسين الأداء</p>
                        <button type="button" class="action-btn btn-danger" onclick="confirmRestart()">
                            <i class="fas fa-power-off"></i>
                            إعادة تشغيل النظام
                        </button>
                    </div>
                </div>
            </div>

            <!-- Health Monitoring -->
            <div class="action-section">
                <div class="section-header">
                    <div class="section-icon icon-health">
                        <i class="fas fa-heartbeat"></i>
                    </div>
                    <h2 class="section-title">مراقبة صحة النظام</h2>
                </div>

                <div class="health-checks">
                    {% for check_name, check_result in health_check.checks.items() %}
                    <div class="health-check">
                        <div class="check-status status-{{ check_result.status }}"></div>
                        <div class="check-name">
                            {% if check_name == 'database_connection' %}
                                اتصال قاعدة البيانات
                            {% elif check_name == 'file_system' %}
                                نظام الملفات
                            {% elif check_name == 'storage_space' %}
                                مساحة التخزين
                            {% elif check_name == 'backup_status' %}
                                حالة النسخ الاحتياطية
                            {% elif check_name == 'log_rotation' %}
                                دوران السجلات
                            {% elif check_name == 'orphaned_files' %}
                                الملفات اليتيمة
                            {% elif check_name == 'data_integrity' %}
                                سلامة البيانات
                            {% elif check_name == 'performance' %}
                                الأداء العام
                            {% else %}
                                {{ check_name }}
                            {% endif %}
                        </div>
                        <div class="check-message">{{ check_result.message }}</div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="sidebar">
            <!-- System Information -->
            <div class="sidebar-section">
                <h3>
                    <i class="fas fa-info-circle"></i>
                    معلومات النظام
                </h3>
                <div class="info-list">
                    <div class="info-item">
                        <span class="info-label">وقت التشغيل:</span>
                        <span class="info-value">{{ maintenance_history.system_uptime or 'غير محدد' }}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">آخر أرشفة:</span>
                        <span class="info-value">{{ maintenance_history.last_archive_date or 'لم تتم' }}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">آخر تنظيف:</span>
                        <span class="info-value">{{ maintenance_history.last_cleanup_date or 'لم يتم' }}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">آخر نسخة احتياطية:</span>
                        <span class="info-value">{{ maintenance_history.last_backup_date or 'لم تتم' }}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">استخدام الذاكرة:</span>
                        <span class="info-value">{{ stats.memory_usage or 'غير متاح' }}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">استخدام القرص:</span>
                        <span class="info-value">{{ stats.disk_usage or 'غير متاح' }}</span>
                    </div>
                </div>
            </div>

            <!-- Performance Metrics -->
            <div class="sidebar-section">
                <h3>
                    <i class="fas fa-tachometer-alt"></i>
                    مقاييس الأداء
                </h3>
                <div class="info-list">
                    <div class="info-item">
                        <span class="info-label">متوسط الاستجابة:</span>
                        <span class="info-value">{{ performance_analysis.response_times.avg_page_load or 1.2 }}s</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">استعلامات قاعدة البيانات:</span>
                        <span class="info-value">{{ performance_analysis.response_times.avg_database_query or 0.05 }}s</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">رفع الملفات:</span>
                        <span class="info-value">{{ performance_analysis.response_times.avg_file_upload or 2.3 }}s</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">استخدام المعالج:</span>
                        <span class="info-value">{{ performance_analysis.resource_usage.cpu_usage_percent or 25 }}%</span>
                    </div>
                </div>
            </div>

            <!-- Recommendations -->
            {% if health_check.recommendations %}
            <div class="sidebar-section">
                <h3>
                    <i class="fas fa-lightbulb"></i>
                    التوصيات
                </h3>
                <div class="recommendations">
                    {% for recommendation in health_check.recommendations[:5] %}
                    <div class="recommendation">
                        <div class="recommendation-text">{{ recommendation }}</div>
                    </div>
                    {% endfor %}
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 MAINTENANCE: Comprehensive maintenance page loaded');

    // Add confirmation for dangerous operations
    const dangerousForms = document.querySelectorAll('form[action*="cleanup"], form[action*="optimize"]');
    dangerousForms.forEach(form => {
        form.addEventListener('submit', function(e) {
            const action = this.action.split('/').pop();
            let message = 'هل أنت متأكد من تنفيذ هذا الإجراء؟';

            if (action.includes('cleanup')) {
                message = 'هل أنت متأكد من حذف السجلات القديمة؟ هذا الإجراء لا يمكن التراجع عنه.';
            } else if (action.includes('optimize')) {
                message = 'هل أنت متأكد من تحسين قاعدة البيانات؟ قد يستغرق هذا بعض الوقت.';
            }

            if (!confirm(message)) {
                e.preventDefault();
                return false;
            }

            // Disable submit button
            const submitBtn = this.querySelector('button[type="submit"]');
            if (submitBtn) {
                submitBtn.disabled = true;
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري التنفيذ...';
            }
        });
    });

    console.log('✅ MAINTENANCE: Form confirmations initialized');
});

function confirmRestart() {
    if (confirm('هل أنت متأكد من إعادة تشغيل النظام؟ سيتم قطع الاتصال مؤقتاً.')) {
        alert('سيتم إعادة تشغيل النظام خلال 30 ثانية. يرجى الانتظار...');
        // In a real system, this would trigger a restart
        setTimeout(() => {
            window.location.reload();
        }, 30000);
    }
}

// Auto-refresh health status every 5 minutes
setInterval(function() {
    console.log('🔄 MAINTENANCE: Auto-refreshing health status...');
    // In a real system, this would make an AJAX call to update health status
}, 300000);
</script>
{% endblock %}
