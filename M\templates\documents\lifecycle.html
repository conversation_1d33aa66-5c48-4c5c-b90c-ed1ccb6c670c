{% extends "base.html" %}

{% block title %}إدارة دورة حياة الوثائق - نظام إدارة الأرشيف العام{% endblock %}

{% block extra_css %}
<style>
    body {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        font-family: 'Cairo', sans-serif;
    }

    .lifecycle-container {
        max-width: 1400px;
        margin: 2rem auto;
        padding: 0 1rem;
    }

    .lifecycle-header {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        border-radius: 20px;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        text-align: center;
    }

    .lifecycle-header h1 {
        color: #2c3e50;
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 0.5rem;
    }

    .lifecycle-header p {
        color: #6c757d;
        font-size: 1.1rem;
        margin: 0;
    }

    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 1.5rem;
        margin-bottom: 2rem;
    }

    .stat-card {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        border-radius: 15px;
        padding: 1.5rem;
        box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        transition: transform 0.3s ease;
    }

    .stat-card:hover {
        transform: translateY(-5px);
    }

    .stat-card .icon {
        font-size: 2.5rem;
        margin-bottom: 1rem;
        display: block;
    }

    .stat-card .number {
        font-size: 2rem;
        font-weight: 700;
        color: #2c3e50;
        margin-bottom: 0.5rem;
    }

    .stat-card .label {
        color: #6c757d;
        font-size: 0.9rem;
        margin: 0;
    }

    .lifecycle-stages {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        border-radius: 20px;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: 0 20px 40px rgba(0,0,0,0.1);
    }

    .stages-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 2rem;
    }

    .stages-header h2 {
        color: #2c3e50;
        font-size: 1.8rem;
        font-weight: 600;
        margin: 0;
    }

    .stage-flow {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 2rem;
        overflow-x: auto;
        padding: 1rem 0;
    }

    .stage-item {
        flex: 1;
        min-width: 150px;
        text-align: center;
        position: relative;
    }

    .stage-item:not(:last-child)::after {
        content: '→';
        position: absolute;
        right: -20px;
        top: 50%;
        transform: translateY(-50%);
        font-size: 1.5rem;
        color: #667eea;
        font-weight: bold;
    }

    .stage-circle {
        width: 80px;
        height: 80px;
        border-radius: 50%;
        margin: 0 auto 1rem;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
        color: white;
        font-weight: bold;
        position: relative;
    }

    .stage-creation { background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%); }
    .stage-active { background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%); }
    .stage-review { background: linear-gradient(135deg, #ffd89b 0%, #19547b 100%); }
    .stage-archive { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
    .stage-disposal { background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); }

    .stage-name {
        font-weight: 600;
        color: #2c3e50;
        margin-bottom: 0.5rem;
    }

    .stage-count {
        color: #6c757d;
        font-size: 0.9rem;
    }

    .retention-policies {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        border-radius: 20px;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: 0 20px 40px rgba(0,0,0,0.1);
    }

    .policy-card {
        border: 1px solid #e9ecef;
        border-radius: 10px;
        padding: 1.5rem;
        margin-bottom: 1rem;
        transition: all 0.3s ease;
    }

    .policy-card:hover {
        border-color: #667eea;
        box-shadow: 0 5px 15px rgba(102, 126, 234, 0.1);
    }

    .policy-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1rem;
    }

    .policy-name {
        font-weight: 600;
        color: #2c3e50;
        font-size: 1.1rem;
    }

    .policy-status {
        padding: 0.25rem 0.75rem;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: 600;
    }

    .status-active {
        background: #d4edda;
        color: #155724;
    }

    .policy-details {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
        margin-bottom: 1rem;
    }

    .policy-detail {
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .policy-detail i {
        color: #667eea;
        width: 20px;
    }

    .archived-documents {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        border-radius: 20px;
        padding: 2rem;
        box-shadow: 0 20px 40px rgba(0,0,0,0.1);
    }

    .document-table {
        width: 100%;
        border-collapse: collapse;
        margin-top: 1rem;
    }

    .document-table th,
    .document-table td {
        padding: 1rem;
        text-align: right;
        border-bottom: 1px solid #e9ecef;
    }

    .document-table th {
        background: #f8f9fa;
        font-weight: 600;
        color: #2c3e50;
    }

    .document-table tr:hover {
        background: #f8f9fa;
    }

    .action-btn {
        padding: 0.5rem 1rem;
        border: none;
        border-radius: 5px;
        font-size: 0.8rem;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-block;
        margin: 0 0.25rem;
    }

    .btn-view {
        background: #007bff;
        color: white;
    }

    .btn-restore {
        background: #28a745;
        color: white;
    }

    .btn-view:hover {
        background: #0056b3;
        color: white;
        text-decoration: none;
    }

    .btn-restore:hover {
        background: #1e7e34;
        color: white;
        text-decoration: none;
    }

    @media (max-width: 768px) {
        .lifecycle-container {
            margin: 1rem;
            padding: 0;
        }

        .stats-grid {
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        }

        .stage-flow {
            flex-direction: column;
            gap: 1rem;
        }

        .stage-item:not(:last-child)::after {
            content: '↓';
            right: auto;
            top: auto;
            bottom: -15px;
        }

        .policy-details {
            grid-template-columns: 1fr;
        }

        .document-table {
            font-size: 0.8rem;
        }

        .document-table th,
        .document-table td {
            padding: 0.5rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="lifecycle-container">
    <!-- Header -->
    <div class="lifecycle-header">
        <h1>
            <i class="fas fa-recycle me-3"></i>
            إدارة دورة حياة الوثائق
        </h1>
        <p>نظام شامل لإدارة دورة حياة الوثائق من الإنشاء إلى الأرشفة</p>
    </div>

    <!-- Statistics -->
    <div class="stats-grid">
        <div class="stat-card">
            <i class="fas fa-file-alt icon" style="color: #667eea;"></i>
            <div class="number">{{ lifecycle_stats.total_documents or 0 }}</div>
            <div class="label">إجمالي الوثائق</div>
        </div>
        <div class="stat-card">
            <i class="fas fa-play-circle icon" style="color: #28a745;"></i>
            <div class="number">{{ lifecycle_stats.active_documents or 0 }}</div>
            <div class="label">وثائق نشطة</div>
        </div>
        <div class="stat-card">
            <i class="fas fa-archive icon" style="color: #6f42c1;"></i>
            <div class="number">{{ lifecycle_stats.archived_documents or 0 }}</div>
            <div class="label">وثائق مؤرشفة</div>
        </div>
        <div class="stat-card">
            <i class="fas fa-eye icon" style="color: #fd7e14;"></i>
            <div class="number">{{ lifecycle_stats.under_review or 0 }}</div>
            <div class="label">قيد المراجعة</div>
        </div>
        <div class="stat-card">
            <i class="fas fa-clock icon" style="color: #dc3545;"></i>
            <div class="number">{{ lifecycle_stats.expired_documents or 0 }}</div>
            <div class="label">وثائق منتهية الصلاحية</div>
        </div>
    </div>

    <!-- Lifecycle Stages -->
    <div class="lifecycle-stages">
        <div class="stages-header">
            <h2>
                <i class="fas fa-project-diagram me-2"></i>
                مراحل دورة حياة الوثائق
            </h2>
        </div>

        <div class="stage-flow">
            {% for stage_key, stage_data in lifecycle_stages.items() %}
            <div class="stage-item">
                <div class="stage-circle stage-{{ stage_key }}">
                    {{ stage_data.count or 0 }}
                </div>
                <div class="stage-name">{{ stage_data.name }}</div>
                <div class="stage-count">{{ stage_data.count or 0 }} وثيقة</div>
            </div>
            {% endfor %}
        </div>
    </div>

    <!-- Retention Policies -->
    <div class="retention-policies">
        <h2>
            <i class="fas fa-shield-alt me-2"></i>
            سياسات الاحتفاظ
        </h2>

        {% for policy in retention_policies %}
        <div class="policy-card">
            <div class="policy-header">
                <div class="policy-name">{{ policy.name }}</div>
                <span class="policy-status status-{{ policy.status }}">{{ policy.status }}</span>
            </div>
            <div class="policy-details">
                <div class="policy-detail">
                    <i class="fas fa-clock"></i>
                    <span>فترة الاحتفاظ: {{ policy.retention_period_text }}</span>
                </div>
                <div class="policy-detail">
                    <i class="fas fa-file-alt"></i>
                    <span>{{ policy.document_types|length }} نوع وثيقة</span>
                </div>
                <div class="policy-detail">
                    <i class="fas fa-robot"></i>
                    <span>أرشفة تلقائية: {{ 'نعم' if policy.auto_archive else 'لا' }}</span>
                </div>
                <div class="policy-detail">
                    <i class="fas fa-eye"></i>
                    <span>مراجعة مطلوبة: {{ 'نعم' if policy.review_required else 'لا' }}</span>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>

    <!-- Archived Documents -->
    <div class="archived-documents">
        <h2>
            <i class="fas fa-archive me-2"></i>
            الوثائق المؤرشفة
        </h2>

        {% if archived_documents %}
        <table class="document-table">
            <thead>
                <tr>
                    <th>العنوان</th>
                    <th>النوع</th>
                    <th>تاريخ الأرشفة</th>
                    <th>سبب الأرشفة</th>
                    <th>الإجراءات</th>
                </tr>
            </thead>
            <tbody>
                {% for archive_info in archived_documents[:10] %}
                <tr>
                    <td>{{ archive_info.document.title }}</td>
                    <td>{{ archive_info.document.document_type }}</td>
                    <td>{{ archive_info.archive_date.strftime('%Y-%m-%d') if archive_info.archive_date else 'غير محدد' }}</td>
                    <td>{{ archive_info.archive_reason }}</td>
                    <td>
                        <a href="{{ url_for('view_document', id=archive_info.document.id) }}" class="action-btn btn-view">
                            <i class="fas fa-eye"></i> عرض
                        </a>
                        {% if archive_info.can_restore %}
                        <a href="{{ url_for('manage_document_lifecycle', id=archive_info.document.id) }}" class="action-btn btn-restore">
                            <i class="fas fa-undo"></i> استعادة
                        </a>
                        {% endif %}
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
        {% else %}
        <div class="text-center py-4">
            <i class="fas fa-archive fa-3x text-muted mb-3"></i>
            <p class="text-muted">لا توجد وثائق مؤرشفة</p>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 LIFECYCLE: Document lifecycle management page loaded');
    
    // Add interactive features
    const statCards = document.querySelectorAll('.stat-card');
    statCards.forEach(card => {
        card.addEventListener('click', function() {
            // Add click animation
            this.style.transform = 'scale(0.95)';
            setTimeout(() => {
                this.style.transform = 'translateY(-5px)';
            }, 150);
        });
    });
    
    // Stage flow interactions
    const stageItems = document.querySelectorAll('.stage-item');
    stageItems.forEach(item => {
        item.addEventListener('mouseenter', function() {
            const circle = this.querySelector('.stage-circle');
            circle.style.transform = 'scale(1.1)';
        });
        
        item.addEventListener('mouseleave', function() {
            const circle = this.querySelector('.stage-circle');
            circle.style.transform = 'scale(1)';
        });
    });
    
    console.log('✅ LIFECYCLE: Interactive features initialized');
});
</script>
{% endblock %}
