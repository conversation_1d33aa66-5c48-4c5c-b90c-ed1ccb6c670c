{% extends "base.html" %}

{% block title %}إضافة مستخدم جديد - إدارة المستخدمين{% endblock %}

{% block extra_css %}
<style>
    .user-header {
        background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
        color: white;
        border-radius: var(--border-radius);
        padding: 2rem;
        margin-bottom: 2rem;
        text-align: center;
    }
    
    .user-form {
        background: white;
        border-radius: var(--border-radius);
        padding: 2rem;
        box-shadow: var(--shadow-light);
        max-width: 600px;
        margin: 0 auto;
    }
    
    .form-section {
        margin-bottom: 2rem;
        padding-bottom: 1.5rem;
        border-bottom: 1px solid #e9ecef;
    }
    
    .form-section:last-child {
        border-bottom: none;
        margin-bottom: 0;
    }
    
    .section-title {
        font-size: 1.25rem;
        font-weight: 700;
        color: #2c3e50;
        margin-bottom: 1rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }
    
    .form-group {
        margin-bottom: 1.5rem;
    }
    
    .form-label {
        font-weight: 600;
        margin-bottom: 0.5rem;
        color: #2c3e50;
        display: block;
    }
    
    .form-control {
        width: 100%;
        padding: 0.75rem;
        border: 2px solid #e9ecef;
        border-radius: var(--border-radius);
        font-size: 1rem;
        transition: all 0.3s ease;
    }
    
    .form-control:focus {
        outline: none;
        border-color: #3498db;
        box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
    }
    
    .form-control.is-invalid {
        border-color: #e74c3c;
    }
    
    .invalid-feedback {
        color: #e74c3c;
        font-size: 0.875rem;
        margin-top: 0.25rem;
    }
    
    .btn-submit {
        background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
        color: white;
        border: none;
        padding: 1rem 2rem;
        border-radius: var(--border-radius);
        font-size: 1.1rem;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        width: 100%;
    }
    
    .btn-submit:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(52, 152, 219, 0.3);
    }
    
    .btn-cancel {
        background: #6c757d;
        color: white;
        border: none;
        padding: 1rem 2rem;
        border-radius: var(--border-radius);
        font-size: 1.1rem;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        width: 100%;
        margin-top: 1rem;
        text-decoration: none;
        display: inline-block;
        text-align: center;
    }
    
    .btn-cancel:hover {
        background: #5a6268;
        color: white;
    }
    
    .password-strength {
        margin-top: 0.5rem;
        font-size: 0.875rem;
    }
    
    .strength-weak {
        color: #e74c3c;
    }
    
    .strength-medium {
        color: #f39c12;
    }
    
    .strength-strong {
        color: #2ecc71;
    }
    
    .form-help {
        font-size: 0.875rem;
        color: #6c757d;
        margin-top: 0.25rem;
    }
    
    @media (max-width: 768px) {
        .user-form {
            padding: 1.5rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<!-- Header -->
<div class="user-header">
    <h1>
        <i class="fas fa-user-plus me-3"></i>
        إضافة مستخدم جديد
    </h1>
    <p>إنشاء حساب مستخدم جديد في النظام</p>
</div>

<!-- Form -->
<div class="user-form">
    <form method="POST">
        {{ form.hidden_tag() }}
        
        <!-- Basic Information -->
        <div class="form-section">
            <h3 class="section-title">
                <i class="fas fa-user"></i>
                المعلومات الأساسية
            </h3>
            
            <div class="form-group">
                {{ form.username.label(class="form-label") }}
                {{ form.username(class="form-control" + (" is-invalid" if form.username.errors else ""), placeholder="أدخل اسم المستخدم") }}
                {% if form.username.errors %}
                    <div class="invalid-feedback">
                        {% for error in form.username.errors %}
                            {{ error }}
                        {% endfor %}
                    </div>
                {% endif %}
                <div class="form-help">اسم المستخدم يجب أن يكون فريداً ولا يحتوي على مسافات</div>
            </div>
            
            <div class="form-group">
                {{ form.email.label(class="form-label") }}
                {{ form.email(class="form-control" + (" is-invalid" if form.email.errors else ""), placeholder="أدخل البريد الإلكتروني") }}
                {% if form.email.errors %}
                    <div class="invalid-feedback">
                        {% for error in form.email.errors %}
                            {{ error }}
                        {% endfor %}
                    </div>
                {% endif %}
                <div class="form-help">سيتم استخدام البريد الإلكتروني لإرسال الإشعارات</div>
            </div>
            
            <div class="form-group">
                {{ form.full_name.label(class="form-label") }}
                {{ form.full_name(class="form-control", placeholder="أدخل الاسم الكامل") }}
                <div class="form-help">الاسم الكامل كما يظهر في النظام</div>
            </div>
        </div>
        
        <!-- Security -->
        <div class="form-section">
            <h3 class="section-title">
                <i class="fas fa-lock"></i>
                الأمان والصلاحيات
            </h3>
            
            <div class="form-group">
                {{ form.password.label(class="form-label") }}
                {{ form.password(class="form-control" + (" is-invalid" if form.password.errors else ""), placeholder="أدخل كلمة المرور", id="password") }}
                {% if form.password.errors %}
                    <div class="invalid-feedback">
                        {% for error in form.password.errors %}
                            {{ error }}
                        {% endfor %}
                    </div>
                {% endif %}
                <div id="passwordStrength" class="password-strength"></div>
                <div class="form-help">كلمة المرور يجب أن تكون 6 أحرف على الأقل</div>
            </div>
            
            <div class="form-group">
                {{ form.role.label(class="form-label") }}
                {{ form.role(class="form-control") }}
                <div class="form-help">
                    <strong>موظف:</strong> يمكنه إدارة الوثائق والكتب<br>
                    <strong>مدير:</strong> صلاحيات كاملة في النظام
                </div>
            </div>
            
            <div class="form-group">
                {{ form.is_active.label(class="form-label") }}
                {{ form.is_active(class="form-control") }}
                <div class="form-help">المستخدمين غير النشطين لا يمكنهم تسجيل الدخول</div>
            </div>
        </div>
        
        <!-- Department -->
        <div class="form-section">
            <h3 class="section-title">
                <i class="fas fa-building"></i>
                معلومات العمل
            </h3>
            
            <div class="form-group">
                {{ form.department.label(class="form-label") }}
                {{ form.department(class="form-control", placeholder="أدخل اسم القسم") }}
                <div class="form-help">القسم أو الوحدة التي يعمل بها المستخدم</div>
            </div>
        </div>
        
        <!-- Submit Buttons -->
        <div class="form-section">
            {{ form.submit(class="btn-submit") }}
            <a href="{{ url_for('manage_users') }}" class="btn-cancel">إلغاء</a>
        </div>
    </form>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Password strength checker
        const passwordInput = document.getElementById('password');
        const strengthDiv = document.getElementById('passwordStrength');
        
        passwordInput.addEventListener('input', function() {
            const password = this.value;
            let strength = 0;
            let message = '';
            
            if (password.length >= 6) strength++;
            if (password.match(/[a-z]/)) strength++;
            if (password.match(/[A-Z]/)) strength++;
            if (password.match(/[0-9]/)) strength++;
            if (password.match(/[^a-zA-Z0-9]/)) strength++;
            
            switch(strength) {
                case 0:
                case 1:
                    message = '<span class="strength-weak">ضعيفة جداً</span>';
                    break;
                case 2:
                    message = '<span class="strength-weak">ضعيفة</span>';
                    break;
                case 3:
                    message = '<span class="strength-medium">متوسطة</span>';
                    break;
                case 4:
                    message = '<span class="strength-strong">قوية</span>';
                    break;
                case 5:
                    message = '<span class="strength-strong">قوية جداً</span>';
                    break;
            }
            
            strengthDiv.innerHTML = password.length > 0 ? 'قوة كلمة المرور: ' + message : '';
        });
        
        // Form validation
        const form = document.querySelector('form');
        form.addEventListener('submit', function(e) {
            const username = document.querySelector('input[name="username"]').value.trim();
            const email = document.querySelector('input[name="email"]').value.trim();
            const password = document.querySelector('input[name="password"]').value;
            
            if (!username) {
                e.preventDefault();
                alert('يرجى إدخال اسم المستخدم');
                return;
            }
            
            if (!email) {
                e.preventDefault();
                alert('يرجى إدخال البريد الإلكتروني');
                return;
            }
            
            if (!password || password.length < 6) {
                e.preventDefault();
                alert('كلمة المرور يجب أن تكون 6 أحرف على الأقل');
                return;
            }
        });
        
        // Animation
        const sections = document.querySelectorAll('.form-section');
        sections.forEach((section, index) => {
            setTimeout(() => {
                section.style.opacity = '0';
                section.style.transform = 'translateY(20px)';
                section.style.transition = 'all 0.6s ease';
                
                setTimeout(() => {
                    section.style.opacity = '1';
                    section.style.transform = 'translateY(0)';
                }, 50);
            }, index * 100);
        });
    });
</script>
{% endblock %}
