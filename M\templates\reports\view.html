{% extends "base.html" %}

{% block title %}عرض التقرير - نظام إدارة الأرشيف العام{% endblock %}

{% block extra_css %}
<style>
    .report-header {
        background: linear-gradient(135deg, #8e44ad 0%, #9b59b6 100%);
        color: white;
        border-radius: var(--border-radius);
        padding: 2rem;
        margin-bottom: 2rem;
        position: relative;
        overflow: hidden;
    }
    
    .report-header::before {
        content: '';
        position: absolute;
        top: -50%;
        right: -50%;
        width: 200%;
        height: 200%;
        background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
        animation: float 6s ease-in-out infinite;
    }
    
    .report-content {
        position: relative;
        z-index: 2;
    }
    
    .report-info {
        background: white;
        border-radius: var(--border-radius);
        padding: 2rem;
        box-shadow: var(--shadow-light);
        margin-bottom: 2rem;
    }
    
    .info-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 1rem;
    }
    
    .info-item {
        display: flex;
        justify-content: space-between;
        padding: 0.75rem 0;
        border-bottom: 1px solid #f8f9fa;
    }
    
    .info-item:last-child {
        border-bottom: none;
    }
    
    .info-label {
        font-weight: 600;
        color: #6c757d;
    }
    
    .info-value {
        color: var(--primary-color);
        font-weight: 500;
    }
    
    .summary-cards {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1.5rem;
        margin-bottom: 2rem;
    }
    
    .summary-card {
        background: white;
        border-radius: var(--border-radius);
        padding: 1.5rem;
        box-shadow: var(--shadow-light);
        text-align: center;
        border-left: 4px solid var(--primary-color);
    }
    
    .summary-number {
        font-size: 2rem;
        font-weight: 700;
        color: var(--primary-color);
        margin-bottom: 0.5rem;
    }
    
    .summary-label {
        color: #6c757d;
        font-weight: 600;
    }
    
    .data-table {
        background: white;
        border-radius: var(--border-radius);
        box-shadow: var(--shadow-light);
        overflow: hidden;
    }
    
    .table-header {
        background: #f8f9fa;
        padding: 1.5rem;
        border-bottom: 1px solid #dee2e6;
    }
    
    .table-title {
        font-size: 1.25rem;
        font-weight: 600;
        color: var(--primary-color);
        margin: 0;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }
    
    .table-responsive {
        max-height: 600px;
        overflow-y: auto;
    }
    
    .table {
        margin: 0;
    }
    
    .table th {
        background: #f8f9fa;
        border-top: none;
        font-weight: 600;
        color: var(--primary-color);
        position: sticky;
        top: 0;
        z-index: 10;
    }
    
    .table td {
        vertical-align: middle;
    }
    
    .badge {
        padding: 0.5rem 1rem;
        border-radius: 20px;
        font-weight: 600;
        font-size: 0.8rem;
    }
    
    .badge-success {
        background: rgba(39, 174, 96, 0.1);
        color: #27ae60;
    }
    
    .badge-warning {
        background: rgba(241, 196, 15, 0.1);
        color: #f1c40f;
    }
    
    .badge-danger {
        background: rgba(231, 76, 60, 0.1);
        color: #e74c3c;
    }
    
    .badge-info {
        background: rgba(52, 152, 219, 0.1);
        color: #3498db;
    }
    
    .badge-secondary {
        background: rgba(149, 165, 166, 0.1);
        color: #95a5a6;
    }
    
    .export-actions {
        background: white;
        border-radius: var(--border-radius);
        padding: 1.5rem;
        box-shadow: var(--shadow-light);
        margin-bottom: 2rem;
        text-align: center;
    }
    
    .btn-export {
        background: linear-gradient(135deg, var(--primary-color) 0%, #2980b9 100%);
        color: white;
        border: none;
        padding: 0.75rem 1.5rem;
        border-radius: var(--border-radius);
        font-weight: 600;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        margin: 0.25rem;
        transition: all 0.3s ease;
    }
    
    .btn-export:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-medium);
        color: white;
    }
    
    .btn-export.csv {
        background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
    }
    
    .btn-export.excel {
        background: linear-gradient(135deg, #16a085 0%, #1abc9c 100%);
    }
    
    .btn-export.pdf {
        background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
    }
    
    .no-data {
        text-align: center;
        padding: 3rem;
        color: #6c757d;
    }
    
    .no-data i {
        font-size: 4rem;
        margin-bottom: 1rem;
        opacity: 0.5;
    }
    
    @media (max-width: 768px) {
        .summary-cards {
            grid-template-columns: 1fr;
        }
        
        .info-grid {
            grid-template-columns: 1fr;
        }
        
        .table-responsive {
            font-size: 0.875rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<!-- Report Header -->
<div class="report-header">
    <div class="report-content">
        <h1 class="mb-3">
            <i class="fas fa-file-alt me-3"></i>
            تقرير {{ form.report_type.data }}
        </h1>
        <p class="mb-0 opacity-75">
            تم توليد التقرير في {{ report_data.generated_at.strftime('%Y/%m/%d %H:%M') }}
            بواسطة {{ report_data.generated_by }}
        </p>
    </div>
</div>

<!-- Export Actions -->
<div class="export-actions">
    <h5 class="mb-3">تصدير التقرير</h5>
    <form method="POST" action="{{ url_for('generate_report') }}" style="display: inline;">
        {{ form.hidden_tag() }}
        <input type="hidden" name="export_format" value="csv">
        <button type="submit" class="btn-export csv">
            <i class="fas fa-file-csv"></i>
            تصدير CSV
        </button>
    </form>
    
    <form method="POST" action="{{ url_for('generate_report') }}" style="display: inline;">
        {{ form.hidden_tag() }}
        <input type="hidden" name="export_format" value="excel">
        <button type="submit" class="btn-export excel">
            <i class="fas fa-file-excel"></i>
            تصدير Excel
        </button>
    </form>
    
    <form method="POST" action="{{ url_for('generate_report') }}" style="display: inline;">
        {{ form.hidden_tag() }}
        <input type="hidden" name="export_format" value="pdf">
        <button type="submit" class="btn-export pdf">
            <i class="fas fa-file-pdf"></i>
            تصدير PDF
        </button>
    </form>
</div>

<!-- Report Information -->
<div class="report-info">
    <h4 class="mb-3">
        <i class="fas fa-info-circle me-2"></i>
        معلومات التقرير
    </h4>
    
    <div class="info-grid">
        <div class="info-item">
            <span class="info-label">نوع التقرير:</span>
            <span class="info-value">{{ form.report_type.data }}</span>
        </div>
        
        {% if report_data.date_from %}
        <div class="info-item">
            <span class="info-label">من تاريخ:</span>
            <span class="info-value">{{ report_data.date_from.strftime('%Y/%m/%d') }}</span>
        </div>
        {% endif %}
        
        {% if report_data.date_to %}
        <div class="info-item">
            <span class="info-label">إلى تاريخ:</span>
            <span class="info-value">{{ report_data.date_to.strftime('%Y/%m/%d') }}</span>
        </div>
        {% endif %}
        
        {% if report_data.filters.status %}
        <div class="info-item">
            <span class="info-label">فلتر الحالة:</span>
            <span class="info-value">{{ report_data.filters.status }}</span>
        </div>
        {% endif %}
        
        {% if report_data.filters.priority %}
        <div class="info-item">
            <span class="info-label">فلتر الأولوية:</span>
            <span class="info-value">{{ report_data.filters.priority }}</span>
        </div>
        {% endif %}
    </div>
</div>

<!-- Summary Cards -->
{% if report_data.summary %}
<div class="summary-cards">
    <div class="summary-card">
        <div class="summary-number">{{ report_data.summary.total_count }}</div>
        <div class="summary-label">إجمالي العناصر</div>
    </div>
    
    {% if report_data.summary.by_status %}
    {% for status, count in report_data.summary.by_status %}
    <div class="summary-card">
        <div class="summary-number">{{ count }}</div>
        <div class="summary-label">{{ status }}</div>
    </div>
    {% endfor %}
    {% endif %}
</div>
{% endif %}

<!-- Data Table -->
<div class="data-table">
    <div class="table-header">
        <h4 class="table-title">
            <i class="fas fa-table"></i>
            بيانات التقرير
        </h4>
    </div>
    
    {% if report_data.items %}
    <div class="table-responsive">
        <table class="table table-striped">
            <thead>
                <tr>
                    {% if form.report_type.data == 'documents' %}
                    <th>الرقم</th>
                    <th>العنوان</th>
                    <th>النوع</th>
                    <th>الحالة</th>
                    <th>تاريخ الإنشاء</th>
                    <th>المنشئ</th>
                    {% elif form.report_type.data == 'incoming' %}
                    <th>رقم الوارد</th>
                    <th>الموضوع</th>
                    <th>الجهة المرسلة</th>
                    <th>الأولوية</th>
                    <th>الحالة</th>
                    <th>تاريخ الاستلام</th>
                    {% elif form.report_type.data == 'outgoing' %}
                    <th>رقم الصادر</th>
                    <th>الموضوع</th>
                    <th>الجهة المرسل إليها</th>
                    <th>الأولوية</th>
                    <th>الحالة</th>
                    <th>تاريخ الإرسال</th>
                    {% elif form.report_type.data == 'users' %}
                    <th>الرقم</th>
                    <th>الاسم الكامل</th>
                    <th>اسم المستخدم</th>
                    <th>الدور</th>
                    <th>القسم</th>
                    <th>الحالة</th>
                    {% endif %}
                </tr>
            </thead>
            <tbody>
                {% for item in report_data.items %}
                <tr>
                    {% if form.report_type.data == 'documents' %}
                    <td>{{ item.id }}</td>
                    <td>{{ item.title }}</td>
                    <td>{{ item.document_type }}</td>
                    <td>
                        <span class="badge badge-{{ 'success' if item.status == 'نشط' else 'secondary' }}">
                            {{ item.status }}
                        </span>
                    </td>
                    <td>{{ item.created_at.strftime('%Y/%m/%d') }}</td>
                    <td>{{ item.creator.full_name if item.creator else 'غير محدد' }}</td>
                    {% elif form.report_type.data == 'incoming' %}
                    <td>{{ item.incoming_number }}</td>
                    <td>{{ item.subject[:50] }}{% if item.subject|length > 50 %}...{% endif %}</td>
                    <td>{{ item.sender_name }}</td>
                    <td>
                        <span class="badge badge-{{ 'danger' if item.priority == 'عاجل' else 'warning' if item.priority == 'مهم' else 'info' }}">
                            {{ item.priority }}
                        </span>
                    </td>
                    <td>
                        <span class="badge badge-{{ 'success' if item.status == 'مكتمل' else 'warning' if item.status == 'قيد المراجعة' else 'info' }}">
                            {{ item.status }}
                        </span>
                    </td>
                    <td>{{ item.received_date.strftime('%Y/%m/%d') }}</td>
                    {% elif form.report_type.data == 'outgoing' %}
                    <td>{{ item.outgoing_number }}</td>
                    <td>{{ item.subject[:50] }}{% if item.subject|length > 50 %}...{% endif %}</td>
                    <td>{{ item.recipient_name }}</td>
                    <td>
                        <span class="badge badge-{{ 'danger' if item.priority == 'عاجل' else 'warning' if item.priority == 'مهم' else 'info' }}">
                            {{ item.priority }}
                        </span>
                    </td>
                    <td>
                        <span class="badge badge-{{ 'success' if item.status == 'تم الإرسال' else 'warning' if item.status == 'جاهز للإرسال' else 'secondary' }}">
                            {{ item.status }}
                        </span>
                    </td>
                    <td>{{ item.sent_date.strftime('%Y/%m/%d') }}</td>
                    {% elif form.report_type.data == 'users' %}
                    <td>{{ item.id }}</td>
                    <td>{{ item.full_name }}</td>
                    <td>{{ item.username }}</td>
                    <td>
                        <span class="badge badge-{{ 'danger' if item.role == 'admin' else 'info' }}">
                            {{ 'مدير' if item.role == 'admin' else 'موظف' }}
                        </span>
                    </td>
                    <td>{{ item.department or 'غير محدد' }}</td>
                    <td>
                        <span class="badge badge-{{ 'success' if item.is_active else 'secondary' }}">
                            {{ 'نشط' if item.is_active else 'معطل' }}
                        </span>
                    </td>
                    {% endif %}
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
    {% else %}
    <div class="no-data">
        <i class="fas fa-inbox"></i>
        <h5>لا توجد بيانات</h5>
        <p>لم يتم العثور على بيانات تطابق معايير التقرير المحددة</p>
    </div>
    {% endif %}
</div>

<div class="text-center mt-4">
    <a href="{{ url_for('generate_report') }}" class="btn btn-outline-primary">
        <i class="fas fa-plus me-2"></i>
        إنشاء تقرير جديد
    </a>
    <a href="{{ url_for('reports') }}" class="btn btn-outline-secondary">
        <i class="fas fa-arrow-right me-2"></i>
        العودة للتقارير
    </a>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Print functionality
    function printReport() {
        window.print();
    }
    
    // Add print button
    document.addEventListener('DOMContentLoaded', function() {
        const exportActions = document.querySelector('.export-actions');
        const printBtn = document.createElement('button');
        printBtn.className = 'btn-export';
        printBtn.innerHTML = '<i class="fas fa-print"></i> طباعة';
        printBtn.onclick = printReport;
        exportActions.appendChild(printBtn);
    });
</script>
{% endblock %}
