{% extends "base.html" %}

{% block title %}البحث المتقدم - نظام إدارة الأرشيف العام{% endblock %}

{% block extra_css %}
<style>
    .search-container {
        background: white;
        border-radius: var(--border-radius);
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: var(--shadow-light);
    }
    
    .search-header {
        background: var(--gradient-primary);
        color: white;
        padding: 1.5rem;
        margin: -2rem -2rem 2rem -2rem;
        border-radius: var(--border-radius) var(--border-radius) 0 0;
    }
    
    .search-form {
        display: grid;
        grid-template-columns: 2fr 1fr 1fr;
        gap: 1rem;
        margin-bottom: 2rem;
    }
    
    .search-dates {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 1rem;
    }
    
    .search-button {
        background: var(--gradient-primary);
        border: none;
        color: white;
        padding: 0.75rem 2rem;
        border-radius: var(--border-radius);
        font-weight: 600;
        transition: all 0.3s ease;
        height: fit-content;
        align-self: end;
    }
    
    .search-button:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-medium);
    }
    
    .results-section {
        background: white;
        border-radius: var(--border-radius);
        box-shadow: var(--shadow-light);
        overflow: hidden;
    }
    
    .results-header {
        background: linear-gradient(135deg, #8e44ad 0%, #9b59b6 100%);
        color: white;
        padding: 1.5rem;
        font-weight: 600;
    }
    
    .results-tabs {
        display: flex;
        background: #f8f9fa;
        border-bottom: 1px solid #dee2e6;
    }
    
    .tab-button {
        background: none;
        border: none;
        padding: 1rem 2rem;
        cursor: pointer;
        transition: all 0.3s ease;
        font-weight: 600;
        color: #6c757d;
        border-bottom: 3px solid transparent;
    }
    
    .tab-button.active {
        color: var(--primary-color);
        border-bottom-color: var(--secondary-color);
        background: white;
    }
    
    .tab-button:hover {
        background: rgba(52, 152, 219, 0.1);
        color: var(--secondary-color);
    }
    
    .tab-content {
        display: none;
        padding: 2rem;
    }
    
    .tab-content.active {
        display: block;
    }
    
    .result-item {
        border: 1px solid #e9ecef;
        border-radius: var(--border-radius);
        padding: 1.5rem;
        margin-bottom: 1rem;
        transition: all 0.3s ease;
        cursor: pointer;
    }
    
    .result-item:hover {
        border-color: var(--secondary-color);
        transform: translateY(-2px);
        box-shadow: var(--shadow-light);
    }
    
    .result-title {
        font-size: 1.1rem;
        font-weight: 600;
        color: var(--primary-color);
        margin-bottom: 0.5rem;
        text-decoration: none;
    }
    
    .result-title:hover {
        color: var(--secondary-color);
    }
    
    .result-meta {
        display: flex;
        flex-wrap: wrap;
        gap: 1rem;
        margin-bottom: 1rem;
        font-size: 0.9rem;
        color: #6c757d;
    }
    
    .result-meta span {
        display: flex;
        align-items: center;
        gap: 0.25rem;
    }
    
    .result-description {
        color: #495057;
        line-height: 1.6;
    }
    
    .result-type {
        background: var(--gradient-primary);
        color: white;
        padding: 0.25rem 0.75rem;
        border-radius: 15px;
        font-size: 0.8rem;
        font-weight: 500;
    }
    
    .result-type.incoming {
        background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
    }
    
    .result-type.outgoing {
        background: linear-gradient(135deg, #e67e22 0%, #f39c12 100%);
    }
    
    .no-results {
        text-align: center;
        padding: 4rem 2rem;
        color: #6c757d;
    }
    
    .no-results i {
        font-size: 4rem;
        margin-bottom: 1rem;
        opacity: 0.5;
    }
    
    .search-stats {
        background: rgba(52, 152, 219, 0.1);
        border-radius: var(--border-radius);
        padding: 1rem;
        margin-bottom: 2rem;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    
    .stats-item {
        text-align: center;
    }
    
    .stats-number {
        font-size: 1.5rem;
        font-weight: 700;
        color: var(--primary-color);
    }
    
    .stats-label {
        font-size: 0.9rem;
        color: #6c757d;
    }
    
    @media (max-width: 768px) {
        .search-form {
            grid-template-columns: 1fr;
        }
        
        .search-dates {
            grid-template-columns: 1fr;
        }
        
        .results-tabs {
            flex-direction: column;
        }
        
        .search-stats {
            flex-direction: column;
            gap: 1rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">
        <i class="fas fa-search me-2"></i>
        البحث المتقدم
    </h1>
</div>

<!-- Search Form -->
<div class="search-container">
    <div class="search-header">
        <h4 class="mb-0">
            <i class="fas fa-filter me-2"></i>
            معايير البحث
        </h4>
    </div>
    
    <form method="GET" class="search-form">
        <div>
            <label class="form-label">كلمة البحث</label>
            <input type="text" name="q" class="form-control" 
                   placeholder="ابحث في العناوين والأوصاف والكلمات المفتاحية..." 
                   value="{{ query }}">
        </div>
        
        <div>
            <label class="form-label">نوع الوثيقة</label>
            <select name="type" class="form-select">
                <option value="">جميع الأنواع</option>
                <option value="كتاب رسمي" {{ 'selected' if doc_type == 'كتاب رسمي' }}>كتاب رسمي</option>
                <option value="مذكرة داخلية" {{ 'selected' if doc_type == 'مذكرة داخلية' }}>مذكرة داخلية</option>
                <option value="تقرير" {{ 'selected' if doc_type == 'تقرير' }}>تقرير</option>
                <option value="عقد" {{ 'selected' if doc_type == 'عقد' }}>عقد</option>
                <option value="فاتورة" {{ 'selected' if doc_type == 'فاتورة' }}>فاتورة</option>
                <option value="أخرى" {{ 'selected' if doc_type == 'أخرى' }}>أخرى</option>
            </select>
        </div>
        
        <div class="search-dates">
            <div>
                <label class="form-label">من تاريخ</label>
                <input type="date" name="date_from" class="form-control" value="{{ date_from }}">
            </div>
            <div>
                <label class="form-label">إلى تاريخ</label>
                <input type="date" name="date_to" class="form-control" value="{{ date_to }}">
            </div>
        </div>
        
        <button type="submit" class="search-button">
            <i class="fas fa-search me-2"></i>
            بحث
        </button>
    </form>
</div>

<!-- Search Results -->
{% if query %}
<div class="results-section">
    <div class="results-header">
        <i class="fas fa-list me-2"></i>
        نتائج البحث عن: "{{ query }}"
    </div>
    
    {% if results.total > 0 %}
    <!-- Search Statistics -->
    <div class="search-stats">
        <div class="stats-item">
            <div class="stats-number">{{ results.total }}</div>
            <div class="stats-label">إجمالي النتائج</div>
        </div>
        <div class="stats-item">
            <div class="stats-number">{{ results.documents|length }}</div>
            <div class="stats-label">الوثائق</div>
        </div>
        <div class="stats-item">
            <div class="stats-number">{{ results.incoming|length }}</div>
            <div class="stats-label">الكتب الواردة</div>
        </div>
        <div class="stats-item">
            <div class="stats-number">{{ results.outgoing|length }}</div>
            <div class="stats-label">الكتب الصادرة</div>
        </div>
    </div>
    
    <!-- Results Tabs -->
    <div class="results-tabs">
        <button class="tab-button active" onclick="showTab('all')">
            جميع النتائج ({{ results.total }})
        </button>
        <button class="tab-button" onclick="showTab('documents')">
            الوثائق ({{ results.documents|length }})
        </button>
        <button class="tab-button" onclick="showTab('incoming')">
            الكتب الواردة ({{ results.incoming|length }})
        </button>
        <button class="tab-button" onclick="showTab('outgoing')">
            الكتب الصادرة ({{ results.outgoing|length }})
        </button>
    </div>
    
    <!-- All Results Tab -->
    <div id="all-tab" class="tab-content active">
        {% for document in results.documents %}
        <div class="result-item" onclick="location.href='{{ url_for('view_document', id=document.id) }}'">
            <div class="d-flex justify-content-between align-items-start mb-2">
                <a href="{{ url_for('view_document', id=document.id) }}" class="result-title">
                    {{ document.title }}
                </a>
                <span class="result-type">وثيقة</span>
            </div>
            <div class="result-meta">
                <span><i class="fas fa-tag"></i> {{ document.document_type }}</span>
                <span><i class="fas fa-calendar"></i> {{ document.created_at.strftime('%Y/%m/%d') }}</span>
                <span><i class="fas fa-user"></i> {{ document.creator.full_name if document.creator else 'غير محدد' }}</span>
            </div>
            {% if document.description %}
            <div class="result-description">
                {{ document.description[:200] }}{% if document.description|length > 200 %}...{% endif %}
            </div>
            {% endif %}
        </div>
        {% endfor %}
        
        {% for document in results.incoming %}
        <div class="result-item" onclick="location.href='{{ url_for('view_incoming', id=document.id) }}'">
            <div class="d-flex justify-content-between align-items-start mb-2">
                <a href="{{ url_for('view_incoming', id=document.id) }}" class="result-title">
                    {{ document.subject }}
                </a>
                <span class="result-type incoming">كتاب وارد</span>
            </div>
            <div class="result-meta">
                <span><i class="fas fa-building"></i> {{ document.sender_name }}</span>
                <span><i class="fas fa-calendar"></i> {{ document.received_date.strftime('%Y/%m/%d') }}</span>
                <span><i class="fas fa-flag"></i> {{ document.priority }}</span>
                <span><i class="fas fa-hashtag"></i> {{ document.incoming_number }}</span>
            </div>
            {% if document.notes %}
            <div class="result-description">
                {{ document.notes[:200] }}{% if document.notes|length > 200 %}...{% endif %}
            </div>
            {% endif %}
        </div>
        {% endfor %}
        
        {% for document in results.outgoing %}
        <div class="result-item" onclick="location.href='{{ url_for('view_outgoing', id=document.id) }}'">
            <div class="d-flex justify-content-between align-items-start mb-2">
                <a href="{{ url_for('view_outgoing', id=document.id) }}" class="result-title">
                    {{ document.subject }}
                </a>
                <span class="result-type outgoing">كتاب صادر</span>
            </div>
            <div class="result-meta">
                <span><i class="fas fa-building"></i> {{ document.recipient_name }}</span>
                <span><i class="fas fa-calendar"></i> {{ document.sent_date.strftime('%Y/%m/%d') }}</span>
                <span><i class="fas fa-flag"></i> {{ document.priority }}</span>
                <span><i class="fas fa-hashtag"></i> {{ document.outgoing_number }}</span>
            </div>
            {% if document.notes %}
            <div class="result-description">
                {{ document.notes[:200] }}{% if document.notes|length > 200 %}...{% endif %}
            </div>
            {% endif %}
        </div>
        {% endfor %}
    </div>
    
    <!-- Documents Tab -->
    <div id="documents-tab" class="tab-content">
        {% if results.documents %}
            {% for document in results.documents %}
            <div class="result-item" onclick="location.href='{{ url_for('view_document', id=document.id) }}'">
                <div class="d-flex justify-content-between align-items-start mb-2">
                    <a href="{{ url_for('view_document', id=document.id) }}" class="result-title">
                        {{ document.title }}
                    </a>
                    <span class="result-type">{{ document.document_type }}</span>
                </div>
                <div class="result-meta">
                    <span><i class="fas fa-calendar"></i> {{ document.created_at.strftime('%Y/%m/%d') }}</span>
                    <span><i class="fas fa-user"></i> {{ document.creator.full_name if document.creator else 'غير محدد' }}</span>
                    {% if document.tags %}
                        <span><i class="fas fa-tags"></i> {{ document.tags }}</span>
                    {% endif %}
                </div>
                {% if document.description %}
                <div class="result-description">
                    {{ document.description }}
                </div>
                {% endif %}
            </div>
            {% endfor %}
        {% else %}
            <div class="no-results">
                <i class="fas fa-folder-open"></i>
                <h5>لا توجد وثائق</h5>
                <p>لم يتم العثور على وثائق تطابق معايير البحث</p>
            </div>
        {% endif %}
    </div>
    
    <!-- Incoming Tab -->
    <div id="incoming-tab" class="tab-content">
        {% if results.incoming %}
            {% for document in results.incoming %}
            <div class="result-item" onclick="location.href='{{ url_for('view_incoming', id=document.id) }}'">
                <div class="d-flex justify-content-between align-items-start mb-2">
                    <a href="{{ url_for('view_incoming', id=document.id) }}" class="result-title">
                        {{ document.subject }}
                    </a>
                    <span class="result-type incoming">{{ document.priority }}</span>
                </div>
                <div class="result-meta">
                    <span><i class="fas fa-hashtag"></i> {{ document.incoming_number }}</span>
                    <span><i class="fas fa-building"></i> {{ document.sender_name }}</span>
                    <span><i class="fas fa-calendar"></i> {{ document.received_date.strftime('%Y/%m/%d') }}</span>
                    <span><i class="fas fa-info-circle"></i> {{ document.status }}</span>
                </div>
                {% if document.notes %}
                <div class="result-description">
                    {{ document.notes }}
                </div>
                {% endif %}
            </div>
            {% endfor %}
        {% else %}
            <div class="no-results">
                <i class="fas fa-inbox"></i>
                <h5>لا توجد كتب واردة</h5>
                <p>لم يتم العثور على كتب واردة تطابق معايير البحث</p>
            </div>
        {% endif %}
    </div>
    
    <!-- Outgoing Tab -->
    <div id="outgoing-tab" class="tab-content">
        {% if results.outgoing %}
            {% for document in results.outgoing %}
            <div class="result-item" onclick="location.href='{{ url_for('view_outgoing', id=document.id) }}'">
                <div class="d-flex justify-content-between align-items-start mb-2">
                    <a href="{{ url_for('view_outgoing', id=document.id) }}" class="result-title">
                        {{ document.subject }}
                    </a>
                    <span class="result-type outgoing">{{ document.priority }}</span>
                </div>
                <div class="result-meta">
                    <span><i class="fas fa-hashtag"></i> {{ document.outgoing_number }}</span>
                    <span><i class="fas fa-building"></i> {{ document.recipient_name }}</span>
                    <span><i class="fas fa-calendar"></i> {{ document.sent_date.strftime('%Y/%m/%d') }}</span>
                    <span><i class="fas fa-info-circle"></i> {{ document.status }}</span>
                </div>
                {% if document.notes %}
                <div class="result-description">
                    {{ document.notes }}
                </div>
                {% endif %}
            </div>
            {% endfor %}
        {% else %}
            <div class="no-results">
                <i class="fas fa-paper-plane"></i>
                <h5>لا توجد كتب صادرة</h5>
                <p>لم يتم العثور على كتب صادرة تطابق معايير البحث</p>
            </div>
        {% endif %}
    </div>
    
    {% else %}
    <div class="no-results">
        <i class="fas fa-search"></i>
        <h5>لا توجد نتائج</h5>
        <p>لم يتم العثور على أي نتائج تطابق معايير البحث المحددة</p>
        <p class="text-muted">جرب استخدام كلمات مفتاحية مختلفة أو توسيع نطاق البحث</p>
    </div>
    {% endif %}
</div>
{% else %}
<div class="results-section">
    <div class="no-results">
        <i class="fas fa-search"></i>
        <h5>ابدأ البحث</h5>
        <p>أدخل كلمة البحث أو حدد معايير البحث للعثور على الوثائق والكتب</p>
    </div>
</div>
{% endif %}
{% endblock %}

{% block extra_js %}
<script>
    function showTab(tabName) {
        // Hide all tabs
        document.querySelectorAll('.tab-content').forEach(tab => {
            tab.classList.remove('active');
        });
        
        // Remove active class from all buttons
        document.querySelectorAll('.tab-button').forEach(button => {
            button.classList.remove('active');
        });
        
        // Show selected tab
        document.getElementById(tabName + '-tab').classList.add('active');
        
        // Add active class to clicked button
        event.target.classList.add('active');
    }
    
    // Highlight search terms
    const searchTerm = '{{ query }}';
    if (searchTerm) {
        document.querySelectorAll('.result-title, .result-description').forEach(element => {
            const text = element.innerHTML;
            const highlightedText = text.replace(
                new RegExp(searchTerm, 'gi'), 
                '<mark>$&</mark>'
            );
            element.innerHTML = highlightedText;
        });
    }
    
    // Auto-focus search input
    document.querySelector('input[name="q"]').focus();
</script>
{% endblock %}
