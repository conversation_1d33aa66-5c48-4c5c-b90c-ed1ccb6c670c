# 🚀 **دليل التشغيل الآمن**
## نظام إدارة الأرشيف العام - Secure Deployment Guide

---

## 📋 **متطلبات النظام**

### **🖥️ متطلبات الخادم:**
- **نظام التشغيل:** Ubuntu 20.04+ / CentOS 8+ / Windows Server 2019+
- **Python:** 3.8+ (يُفضل 3.10+)
- **الذاكرة:** 4GB RAM (يُفضل 8GB+)
- **التخزين:** 50GB مساحة فارغة (يُفضل SSD)
- **الشبكة:** اتصال إنترنت مستقر

### **🔒 متطلبات الأمان:**
- **SSL Certificate:** شهادة SSL صالحة
- **Firewall:** جدار حماية مُفعل
- **Antivirus:** برنامج حماية محدث
- **Backup System:** نظام نسخ احتياطي

---

## 🛠️ **خطوات التثبيت الآمن**

### **1. إعداد البيئة:**
```bash
# إنشاء مستخدم مخصص للنظام
sudo useradd -m -s /bin/bash archiveuser
sudo usermod -aG sudo archiveuser

# إنشاء مجلد التطبيق
sudo mkdir -p /opt/archive_system
sudo chown archiveuser:archiveuser /opt/archive_system
cd /opt/archive_system

# إنشاء البيئة الافتراضية
python3 -m venv venv
source venv/bin/activate
```

### **2. تثبيت المكتبات الأمنية:**
```bash
# تثبيت المتطلبات الأمنية
pip install -r requirements_security.txt

# تثبيت مكتبات إضافية للإنتاج
pip install gunicorn supervisor
```

### **3. إعداد قاعدة البيانات الآمنة:**
```bash
# إنشاء مجلد قاعدة البيانات مع صلاحيات محدودة
mkdir -p data
chmod 700 data

# إعداد النسخ الاحتياطية
mkdir -p backups
chmod 700 backups
```

### **4. إعداد متغيرات البيئة:**
```bash
# إنشاء ملف البيئة الآمن
cat > .env << EOF
SECRET_KEY=$(python -c "import secrets; print(secrets.token_hex(32))")
ENCRYPTION_KEY=$(python -c "from cryptography.fernet import Fernet; print(Fernet.generate_key().decode())")
DATABASE_URL=sqlite:///data/archive_system.db
FLASK_ENV=production
UPLOAD_FOLDER=/opt/archive_system/uploads
LOG_LEVEL=INFO
EOF

# تأمين ملف البيئة
chmod 600 .env
```

---

## 🔧 **إعدادات الإنتاج**

### **1. إعداد Gunicorn:**
```python
# gunicorn_config.py
bind = "127.0.0.1:8000"
workers = 4
worker_class = "sync"
worker_connections = 1000
max_requests = 1000
max_requests_jitter = 100
timeout = 30
keepalive = 2
preload_app = True
user = "archiveuser"
group = "archiveuser"
tmp_upload_dir = None
secure_scheme_headers = {
    'X-FORWARDED-PROTOCOL': 'ssl',
    'X-FORWARDED-PROTO': 'https',
    'X-FORWARDED-SSL': 'on'
}
```

### **2. إعداد Nginx:**
```nginx
# /etc/nginx/sites-available/archive_system
server {
    listen 80;
    server_name your-domain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com;

    # SSL Configuration
    ssl_certificate /path/to/ssl/certificate.crt;
    ssl_certificate_key /path/to/ssl/private.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;

    # Security Headers
    add_header Strict-Transport-Security "max-age=63072000; includeSubDomains; preload";
    add_header X-Content-Type-Options nosniff;
    add_header X-Frame-Options DENY;
    add_header X-XSS-Protection "1; mode=block";
    add_header Referrer-Policy "strict-origin-when-cross-origin";

    # File Upload Limits
    client_max_body_size 50M;
    client_body_timeout 60s;
    client_header_timeout 60s;

    location / {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_redirect off;
        proxy_buffering off;
    }

    location /static {
        alias /opt/archive_system/static;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    location /uploads {
        alias /opt/archive_system/uploads;
        internal;
    }
}
```

### **3. إعداد Supervisor:**
```ini
# /etc/supervisor/conf.d/archive_system.conf
[program:archive_system]
command=/opt/archive_system/venv/bin/gunicorn -c gunicorn_config.py app:app
directory=/opt/archive_system
user=archiveuser
autostart=true
autorestart=true
redirect_stderr=true
stdout_logfile=/var/log/archive_system/app.log
stdout_logfile_maxbytes=50MB
stdout_logfile_backups=5
environment=PATH="/opt/archive_system/venv/bin"
```

---

## 🔒 **إعدادات الأمان المتقدمة**

### **1. جدار الحماية:**
```bash
# UFW Firewall Configuration
sudo ufw default deny incoming
sudo ufw default allow outgoing
sudo ufw allow ssh
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
sudo ufw enable
```

### **2. Fail2Ban:**
```ini
# /etc/fail2ban/jail.local
[DEFAULT]
bantime = 3600
findtime = 600
maxretry = 5

[nginx-http-auth]
enabled = true
filter = nginx-http-auth
logpath = /var/log/nginx/error.log

[nginx-limit-req]
enabled = true
filter = nginx-limit-req
logpath = /var/log/nginx/error.log
maxretry = 10
```

### **3. مراقبة السجلات:**
```bash
# إعداد logrotate
cat > /etc/logrotate.d/archive_system << EOF
/var/log/archive_system/*.log {
    daily
    missingok
    rotate 52
    compress
    delaycompress
    notifempty
    create 644 archiveuser archiveuser
    postrotate
        supervisorctl restart archive_system
    endscript
}
EOF
```

---

## 📊 **مراقبة النظام**

### **1. مراقبة الأداء:**
```bash
# إعداد مراقبة الموارد
cat > monitor.sh << EOF
#!/bin/bash
# System monitoring script
echo "$(date): System Status Check" >> /var/log/archive_system/monitor.log
echo "CPU: $(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | awk -F'%' '{print $1}')" >> /var/log/archive_system/monitor.log
echo "Memory: $(free | grep Mem | awk '{printf("%.2f%%", $3/$2 * 100.0)}')" >> /var/log/archive_system/monitor.log
echo "Disk: $(df -h / | awk 'NR==2{printf "%s", $5}')" >> /var/log/archive_system/monitor.log
echo "---" >> /var/log/archive_system/monitor.log
EOF

chmod +x monitor.sh

# إضافة إلى crontab
echo "*/5 * * * * /opt/archive_system/monitor.sh" | crontab -
```

### **2. النسخ الاحتياطية التلقائية:**
```bash
# إعداد النسخ الاحتياطية
cat > backup.sh << EOF
#!/bin/bash
BACKUP_DIR="/opt/archive_system/backups"
DATE=$(date +%Y%m%d_%H%M%S)

# Database backup
cp data/archive_system.db $BACKUP_DIR/db_backup_$DATE.db

# Files backup
tar -czf $BACKUP_DIR/files_backup_$DATE.tar.gz uploads/

# Keep only last 30 backups
find $BACKUP_DIR -name "*.db" -mtime +30 -delete
find $BACKUP_DIR -name "*.tar.gz" -mtime +30 -delete

echo "$(date): Backup completed - $DATE" >> /var/log/archive_system/backup.log
EOF

chmod +x backup.sh

# تشغيل يومي في الساعة 2 صباحاً
echo "0 2 * * * /opt/archive_system/backup.sh" | crontab -
```

---

## 🚨 **إجراءات الطوارئ**

### **1. استعادة النظام:**
```bash
# إيقاف الخدمات
sudo supervisorctl stop archive_system
sudo systemctl stop nginx

# استعادة قاعدة البيانات
cp backups/db_backup_YYYYMMDD_HHMMSS.db data/archive_system.db

# استعادة الملفات
tar -xzf backups/files_backup_YYYYMMDD_HHMMSS.tar.gz

# إعادة تشغيل الخدمات
sudo systemctl start nginx
sudo supervisorctl start archive_system
```

### **2. فحص الأمان:**
```bash
# فحص سجلات الأمان
tail -f /var/log/archive_system/security.log

# فحص محاولات الاختراق
grep "SECURITY EVENT" /var/log/archive_system/app.log

# فحص حالة النظام
systemctl status nginx
supervisorctl status archive_system
```

---

## ✅ **قائمة التحقق النهائية**

### **🔒 الأمان:**
- [ ] SSL Certificate مثبت وصالح
- [ ] جدار الحماية مُفعل ومُكون
- [ ] Fail2Ban مثبت ومُفعل
- [ ] كلمات مرور قوية لجميع الحسابات
- [ ] صلاحيات الملفات محدودة (600/700)
- [ ] متغيرات البيئة آمنة
- [ ] النسخ الاحتياطية تعمل تلقائياً

### **⚡ الأداء:**
- [ ] Gunicorn مُكون بشكل صحيح
- [ ] Nginx مُحسن للأداء
- [ ] قاعدة البيانات مُحسنة
- [ ] مراقبة الموارد مُفعلة
- [ ] تنظيف السجلات تلقائي

### **🔧 الصيانة:**
- [ ] مراقبة السجلات مُفعلة
- [ ] تنبيهات النظام مُكونة
- [ ] إجراءات الطوارئ موثقة
- [ ] فريق الدعم مُدرب
- [ ] خطة الاستعادة جاهزة

---

**🎉 النظام جاهز للتشغيل الآمن في بيئة الإنتاج!**
