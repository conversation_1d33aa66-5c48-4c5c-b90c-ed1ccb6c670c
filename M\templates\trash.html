{% extends "base.html" %}

{% block title %}سلة المهملات - نظام إدارة الأرشيف العام{% endblock %}

{% block extra_css %}
<style>
    .trash-header {
        background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
        color: white;
        border-radius: var(--border-radius);
        padding: 2rem;
        margin-bottom: 2rem;
        text-align: center;
    }
    
    .trash-title {
        font-size: 2rem;
        font-weight: 700;
        margin: 0;
    }
    
    .trash-subtitle {
        opacity: 0.9;
        margin: 0.5rem 0 0 0;
    }
    
    .stats-row {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
        margin-bottom: 2rem;
    }
    
    .stat-card {
        background: white;
        border-radius: var(--border-radius);
        padding: 1.5rem;
        text-align: center;
        box-shadow: var(--shadow-light);
        transition: all 0.3s ease;
    }
    
    .stat-card:hover {
        transform: translateY(-5px);
        box-shadow: var(--shadow-medium);
    }
    
    .stat-icon {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 1rem;
        font-size: 1.5rem;
        color: white;
    }
    
    .stat-icon.total {
        background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
    }
    
    .stat-icon.documents {
        background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
    }
    
    .stat-icon.incoming {
        background: linear-gradient(135deg, #2ecc71 0%, #27ae60 100%);
    }
    
    .stat-icon.outgoing {
        background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
    }
    
    .stat-number {
        font-size: 2rem;
        font-weight: 700;
        color: #2c3e50;
        margin-bottom: 0.5rem;
    }
    
    .stat-label {
        color: #7f8c8d;
        font-weight: 600;
    }
    
    .filters-card {
        background: white;
        border-radius: var(--border-radius);
        padding: 1.5rem;
        margin-bottom: 2rem;
        box-shadow: var(--shadow-light);
    }
    
    .filters-row {
        display: flex;
        align-items: center;
        gap: 1rem;
        flex-wrap: wrap;
    }
    
    .filter-select {
        padding: 0.75rem;
        border: 2px solid #e9ecef;
        border-radius: var(--border-radius);
        background: white;
        min-width: 200px;
    }
    
    .btn-danger {
        background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
        border: none;
        color: white;
        padding: 0.75rem 1.5rem;
        border-radius: var(--border-radius);
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
    }
    
    .btn-danger:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(231, 76, 60, 0.3);
    }
    
    .trash-table {
        background: white;
        border-radius: var(--border-radius);
        overflow: hidden;
        box-shadow: var(--shadow-light);
    }
    
    .table th {
        background: #f8f9fa;
        font-weight: 600;
        color: #2c3e50;
        border: none;
        padding: 1rem;
    }
    
    .table td {
        padding: 1rem;
        border-top: 1px solid #e9ecef;
        vertical-align: middle;
    }
    
    .table tbody tr:hover {
        background: #f8f9fa;
    }
    
    .entity-badge {
        padding: 0.25rem 0.75rem;
        border-radius: 20px;
        font-size: 0.875rem;
        font-weight: 600;
    }
    
    .entity-document {
        background: #d1ecf1;
        color: #0c5460;
    }
    
    .entity-incoming {
        background: #d4edda;
        color: #155724;
    }
    
    .entity-outgoing {
        background: #fff3cd;
        color: #856404;
    }
    
    .action-buttons {
        display: flex;
        gap: 0.5rem;
    }
    
    .btn-sm {
        padding: 0.375rem 0.75rem;
        font-size: 0.875rem;
        border-radius: 15px;
        border: none;
        cursor: pointer;
        transition: all 0.3s ease;
    }
    
    .btn-success {
        background: #27ae60;
        color: white;
    }
    
    .btn-success:hover {
        background: #2ecc71;
        transform: translateY(-1px);
    }
    
    .btn-danger-sm {
        background: #e74c3c;
        color: white;
    }
    
    .btn-danger-sm:hover {
        background: #c0392b;
        transform: translateY(-1px);
    }
    
    .empty-trash {
        text-align: center;
        padding: 3rem;
        color: #6c757d;
    }
    
    .empty-trash i {
        font-size: 4rem;
        margin-bottom: 1rem;
        opacity: 0.5;
    }
    
    .time-info {
        font-size: 0.875rem;
        color: #6c757d;
    }
    
    @media (max-width: 768px) {
        .filters-row {
            flex-direction: column;
            align-items: stretch;
        }
        
        .filter-select {
            min-width: auto;
        }
        
        .action-buttons {
            flex-direction: column;
        }
    }
</style>
{% endblock %}

{% block content %}
<!-- Header -->
<div class="trash-header">
    <h1 class="trash-title">
        <i class="fas fa-trash-alt me-3"></i>
        سلة المهملات
    </h1>
    <p class="trash-subtitle">إدارة العناصر المحذوفة واسترجاعها</p>
</div>

<!-- Statistics -->
<div class="stats-row">
    <div class="stat-card">
        <div class="stat-icon total">
            <i class="fas fa-trash"></i>
        </div>
        <div class="stat-number">{{ trash_stats.total_items }}</div>
        <div class="stat-label">إجمالي العناصر</div>
    </div>
    
    <div class="stat-card">
        <div class="stat-icon documents">
            <i class="fas fa-file"></i>
        </div>
        <div class="stat-number">{{ trash_stats.documents }}</div>
        <div class="stat-label">الوثائق</div>
    </div>
    
    <div class="stat-card">
        <div class="stat-icon incoming">
            <i class="fas fa-inbox"></i>
        </div>
        <div class="stat-number">{{ trash_stats.incoming }}</div>
        <div class="stat-label">الكتب الواردة</div>
    </div>
    
    <div class="stat-card">
        <div class="stat-icon outgoing">
            <i class="fas fa-paper-plane"></i>
        </div>
        <div class="stat-number">{{ trash_stats.outgoing }}</div>
        <div class="stat-label">الكتب الصادرة</div>
    </div>
</div>

<!-- Filters and Actions -->
<div class="filters-card">
    <form method="GET" action="{{ url_for('trash') }}">
        <div class="filters-row">
            <select name="entity_type" class="filter-select">
                <option value="">جميع الأنواع</option>
                {% for entity_type in entity_types %}
                    <option value="{{ entity_type }}" 
                            {{ 'selected' if current_filter == entity_type else '' }}>
                        {% if entity_type == 'document' %}الوثائق
                        {% elif entity_type == 'incoming' %}الكتب الواردة
                        {% elif entity_type == 'outgoing' %}الكتب الصادرة
                        {% endif %}
                    </option>
                {% endfor %}
            </select>
            
            <button type="submit" class="btn btn-primary">
                <i class="fas fa-filter me-2"></i>
                تطبيق الفلتر
            </button>
            
            <a href="{{ url_for('trash') }}" class="btn btn-secondary">
                <i class="fas fa-times me-2"></i>
                مسح الفلتر
            </a>
            
            {% if trash_stats.total_items > 0 %}
                <button type="button" class="btn-danger" 
                        onclick="confirmEmptyTrash()">
                    <i class="fas fa-trash-alt me-2"></i>
                    إفراغ سلة المهملات
                </button>
            {% endif %}
        </div>
    </form>
</div>

<!-- Trash Table -->
<div class="trash-table">
    {% if deleted_items.items %}
        <div class="table-responsive">
            <table class="table">
                <thead>
                    <tr>
                        <th>النوع</th>
                        <th>العنوان/الموضوع</th>
                        <th>تاريخ الحذف</th>
                        <th>المحذوف بواسطة</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for item in deleted_items.items %}
                        {% set data = item.entity_data | from_json %}
                        <tr>
                            <td>
                                <span class="entity-badge entity-{{ item.entity_type }}">
                                    {% if item.entity_type == 'document' %}
                                        <i class="fas fa-file me-1"></i>وثيقة
                                    {% elif item.entity_type == 'incoming' %}
                                        <i class="fas fa-inbox me-1"></i>كتاب وارد
                                    {% elif item.entity_type == 'outgoing' %}
                                        <i class="fas fa-paper-plane me-1"></i>كتاب صادر
                                    {% endif %}
                                </span>
                            </td>
                            <td>
                                <div>{{ data.title or data.subject or 'غير محدد' }}</div>
                                {% if data.description %}
                                    <small class="text-muted">{{ data.description[:50] }}...</small>
                                {% endif %}
                            </td>
                            <td>
                                <div class="time-info">
                                    <div>{{ item.deleted_at.strftime('%Y/%m/%d') }}</div>
                                    <div>{{ item.deleted_at.strftime('%H:%M:%S') }}</div>
                                </div>
                            </td>
                            <td>{{ item.deleter.full_name or item.deleter.username }}</td>
                            <td>
                                <div class="action-buttons">
                                    {% if item.can_restore %}
                                        <button class="btn-sm btn-success" 
                                                onclick="confirmRestore({{ item.id }}, '{{ data.title or data.subject or 'العنصر' }}')">
                                            <i class="fas fa-undo me-1"></i>
                                            استرجاع
                                        </button>
                                    {% endif %}
                                    <button class="btn-sm btn-danger-sm" 
                                            onclick="confirmDelete({{ item.id }}, '{{ data.title or data.subject or 'العنصر' }}')">
                                        <i class="fas fa-times me-1"></i>
                                        حذف نهائي
                                    </button>
                                </div>
                            </td>
                        </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        
        <!-- Pagination -->
        {% if deleted_items.pages > 1 %}
            <div class="pagination-wrapper">
                <nav>
                    <ul class="pagination justify-content-center">
                        {% if deleted_items.has_prev %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('trash', page=deleted_items.prev_num, entity_type=current_filter) }}">السابق</a>
                            </li>
                        {% endif %}
                        
                        {% for page_num in deleted_items.iter_pages() %}
                            {% if page_num %}
                                {% if page_num != deleted_items.page %}
                                    <li class="page-item">
                                        <a class="page-link" href="{{ url_for('trash', page=page_num, entity_type=current_filter) }}">{{ page_num }}</a>
                                    </li>
                                {% else %}
                                    <li class="page-item active">
                                        <span class="page-link">{{ page_num }}</span>
                                    </li>
                                {% endif %}
                            {% else %}
                                <li class="page-item disabled">
                                    <span class="page-link">…</span>
                                </li>
                            {% endif %}
                        {% endfor %}
                        
                        {% if deleted_items.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('trash', page=deleted_items.next_num, entity_type=current_filter) }}">التالي</a>
                            </li>
                        {% endif %}
                    </ul>
                </nav>
            </div>
        {% endif %}
    {% else %}
        <div class="empty-trash">
            <i class="fas fa-trash-alt"></i>
            <h4>سلة المهملات فارغة</h4>
            <p>لا توجد عناصر محذوفة حالياً</p>
        </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script>
    function confirmRestore(itemId, itemName) {
        if (confirm(`هل أنت متأكد من استرجاع "${itemName}"؟`)) {
            window.location.href = `/trash/restore/${itemId}`;
        }
    }
    
    function confirmDelete(itemId, itemName) {
        if (confirm(`هل أنت متأكد من الحذف النهائي لـ "${itemName}"؟\n\nتحذير: لا يمكن التراجع عن هذا الإجراء!`)) {
            window.location.href = `/trash/delete-permanently/${itemId}`;
        }
    }
    
    function confirmEmptyTrash() {
        if (confirm('هل أنت متأكد من إفراغ سلة المهملات بالكامل؟\n\nتحذير: سيتم حذف جميع العناصر نهائياً ولا يمكن التراجع عن هذا الإجراء!')) {
            window.location.href = '/trash/empty';
        }
    }
    
    // تحسين تجربة المستخدم
    document.addEventListener('DOMContentLoaded', function() {
        // إضافة تأثيرات للبطاقات
        const cards = document.querySelectorAll('.stat-card, .trash-table');
        cards.forEach((card, index) => {
            setTimeout(() => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(20px)';
                card.style.transition = 'all 0.6s ease';
                
                setTimeout(() => {
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, 50);
            }, index * 100);
        });
    });
</script>
{% endblock %}
