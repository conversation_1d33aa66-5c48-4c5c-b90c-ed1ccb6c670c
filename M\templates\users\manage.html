{% extends "base.html" %}

{% block title %}إدارة المستخدمين - نظام إدارة الأرشيف العام{% endblock %}

{% block extra_css %}
<style>
    .users-header {
        background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
        color: white;
        border-radius: var(--border-radius);
        padding: 2rem;
        margin-bottom: 2rem;
        text-align: center;
    }
    
    .users-title {
        font-size: 2rem;
        font-weight: 700;
        margin: 0;
    }
    
    .users-subtitle {
        opacity: 0.9;
        margin: 0.5rem 0 0 0;
    }
    
    .stats-row {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
        margin-bottom: 2rem;
    }
    
    .stat-card {
        background: white;
        border-radius: var(--border-radius);
        padding: 1.5rem;
        text-align: center;
        box-shadow: var(--shadow-light);
        transition: all 0.3s ease;
    }
    
    .stat-card:hover {
        transform: translateY(-5px);
        box-shadow: var(--shadow-medium);
    }
    
    .stat-icon {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 1rem;
        font-size: 1.5rem;
        color: white;
    }
    
    .stat-icon.total {
        background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
    }
    
    .stat-icon.active {
        background: linear-gradient(135deg, #2ecc71 0%, #27ae60 100%);
    }
    
    .stat-icon.admin {
        background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
    }
    
    .stat-icon.employee {
        background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
    }
    
    .stat-number {
        font-size: 2rem;
        font-weight: 700;
        color: #2c3e50;
        margin-bottom: 0.5rem;
    }
    
    .stat-label {
        color: #7f8c8d;
        font-weight: 600;
    }
    
    .users-controls {
        background: white;
        border-radius: var(--border-radius);
        padding: 1.5rem;
        margin-bottom: 2rem;
        box-shadow: var(--shadow-light);
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-wrap: wrap;
        gap: 1rem;
    }
    
    .btn-add-user {
        background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
        color: white;
        border: none;
        padding: 0.75rem 1.5rem;
        border-radius: var(--border-radius);
        font-weight: 600;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        transition: all 0.3s ease;
    }
    
    .btn-add-user:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(52, 152, 219, 0.3);
        color: white;
    }
    
    .users-table {
        background: white;
        border-radius: var(--border-radius);
        box-shadow: var(--shadow-light);
        overflow: hidden;
    }
    
    .table {
        margin: 0;
    }
    
    .table th {
        background: #f8f9fa;
        border: none;
        font-weight: 700;
        color: #2c3e50;
        padding: 1rem;
    }
    
    .table td {
        border: none;
        padding: 1rem;
        vertical-align: middle;
    }
    
    .table tbody tr {
        border-bottom: 1px solid #e9ecef;
        transition: all 0.3s ease;
    }
    
    .table tbody tr:hover {
        background: #f8f9fa;
    }
    
    .user-avatar {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: 700;
        margin-right: 1rem;
    }
    
    .user-info {
        display: flex;
        align-items: center;
    }
    
    .user-details h6 {
        margin: 0;
        font-weight: 700;
        color: #2c3e50;
    }
    
    .user-details small {
        color: #7f8c8d;
    }
    
    .status-badge {
        padding: 0.25rem 0.75rem;
        border-radius: 20px;
        font-size: 0.875rem;
        font-weight: 600;
    }
    
    .status-badge.active {
        background: #d4edda;
        color: #155724;
    }
    
    .status-badge.inactive {
        background: #f8d7da;
        color: #721c24;
    }
    
    .role-badge {
        padding: 0.25rem 0.75rem;
        border-radius: 20px;
        font-size: 0.875rem;
        font-weight: 600;
    }
    
    .role-badge.admin {
        background: #f8d7da;
        color: #721c24;
    }
    
    .role-badge.employee {
        background: #d1ecf1;
        color: #0c5460;
    }
    
    .btn-action {
        padding: 0.375rem 0.75rem;
        border: none;
        border-radius: 5px;
        font-size: 0.875rem;
        cursor: pointer;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 0.25rem;
        margin: 0 0.25rem;
    }
    
    .btn-edit {
        background: #f39c12;
        color: white;
    }
    
    .btn-edit:hover {
        background: #e67e22;
        color: white;
    }
    
    .btn-toggle {
        background: #17a2b8;
        color: white;
    }
    
    .btn-toggle:hover {
        background: #138496;
        color: white;
    }
    
    @media (max-width: 768px) {
        .users-controls {
            flex-direction: column;
            align-items: stretch;
        }
        
        .stats-row {
            grid-template-columns: repeat(2, 1fr);
        }
        
        .table-responsive {
            font-size: 0.875rem;
        }
        
        .user-info {
            flex-direction: column;
            align-items: flex-start;
        }
        
        .user-avatar {
            margin-right: 0;
            margin-bottom: 0.5rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<!-- Header -->
<div class="users-header">
    <h1 class="users-title">
        <i class="fas fa-users me-3"></i>
        إدارة المستخدمين
    </h1>
    <p class="users-subtitle">إدارة حسابات المستخدمين وصلاحياتهم</p>
</div>

<!-- Statistics -->
<div class="stats-row">
    <div class="stat-card">
        <div class="stat-icon total">
            <i class="fas fa-users"></i>
        </div>
        <div class="stat-number">{{ user_stats.total_users }}</div>
        <div class="stat-label">إجمالي المستخدمين</div>
    </div>
    
    <div class="stat-card">
        <div class="stat-icon active">
            <i class="fas fa-user-check"></i>
        </div>
        <div class="stat-number">{{ user_stats.active_users }}</div>
        <div class="stat-label">المستخدمين النشطين</div>
    </div>
    
    <div class="stat-card">
        <div class="stat-icon admin">
            <i class="fas fa-user-shield"></i>
        </div>
        <div class="stat-number">{{ user_stats.admin_users }}</div>
        <div class="stat-label">المديرين</div>
    </div>
    
    <div class="stat-card">
        <div class="stat-icon employee">
            <i class="fas fa-user-tie"></i>
        </div>
        <div class="stat-number">{{ user_stats.employee_users }}</div>
        <div class="stat-label">الموظفين</div>
    </div>
</div>

<!-- Controls -->
<div class="users-controls">
    <div>
        <h5 class="mb-0">قائمة المستخدمين</h5>
        <small class="text-muted">إدارة جميع حسابات المستخدمين في النظام</small>
    </div>
    
    <a href="{{ url_for('add_user') }}" class="btn-add-user">
        <i class="fas fa-plus"></i>
        إضافة مستخدم جديد
    </a>
</div>

<!-- Users Table -->
<div class="users-table">
    <div class="table-responsive">
        <table class="table">
            <thead>
                <tr>
                    <th>المستخدم</th>
                    <th>البريد الإلكتروني</th>
                    <th>القسم</th>
                    <th>الدور</th>
                    <th>الحالة</th>
                    <th>تاريخ الإنشاء</th>
                    <th>آخر دخول</th>
                    <th>الإجراءات</th>
                </tr>
            </thead>
            <tbody>
                {% for user in users.items %}
                <tr>
                    <td>
                        <div class="user-info">
                            <div class="user-avatar">
                                {{ user.full_name[0] if user.full_name else user.username[0] }}
                            </div>
                            <div class="user-details">
                                <h6>{{ user.full_name or user.username }}</h6>
                                <small>@{{ user.username }}</small>
                            </div>
                        </div>
                    </td>
                    <td>{{ user.email }}</td>
                    <td>{{ user.department or 'غير محدد' }}</td>
                    <td>
                        <span class="role-badge {{ user.role }}">
                            {% if user.role == 'admin' %}
                                <i class="fas fa-shield-alt me-1"></i>مدير
                            {% else %}
                                <i class="fas fa-user me-1"></i>موظف
                            {% endif %}
                        </span>
                    </td>
                    <td>
                        <span class="status-badge {{ 'active' if user.is_active else 'inactive' }}">
                            {% if user.is_active %}
                                <i class="fas fa-check-circle me-1"></i>نشط
                            {% else %}
                                <i class="fas fa-times-circle me-1"></i>غير نشط
                            {% endif %}
                        </span>
                    </td>
                    <td>{{ user.created_at.strftime('%Y/%m/%d') if user.created_at else 'غير محدد' }}</td>
                    <td>{{ user.last_login.strftime('%Y/%m/%d %H:%M') if user.last_login else 'لم يسجل دخول' }}</td>
                    <td>
                        <a href="{{ url_for('edit_user', user_id=user.id) }}" class="btn-action btn-edit">
                            <i class="fas fa-edit"></i>
                        </a>
                        {% if user.id != current_user.id %}
                        <a href="{{ url_for('toggle_user_status', user_id=user.id) }}" 
                           class="btn-action btn-toggle"
                           onclick="return confirm('هل تريد تغيير حالة هذا المستخدم؟')">
                            <i class="fas fa-{{ 'pause' if user.is_active else 'play' }}"></i>
                        </a>
                        {% endif %}
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
    
    <!-- Pagination -->
    {% if users.pages > 1 %}
        <div class="d-flex justify-content-center p-3">
            <nav>
                <ul class="pagination">
                    {% if users.has_prev %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('manage_users', page=users.prev_num) }}">السابق</a>
                        </li>
                    {% endif %}
                    
                    {% for page_num in users.iter_pages() %}
                        {% if page_num %}
                            {% if page_num != users.page %}
                                <li class="page-item">
                                    <a class="page-link" href="{{ url_for('manage_users', page=page_num) }}">{{ page_num }}</a>
                                </li>
                            {% else %}
                                <li class="page-item active">
                                    <span class="page-link">{{ page_num }}</span>
                                </li>
                            {% endif %}
                        {% else %}
                            <li class="page-item disabled">
                                <span class="page-link">…</span>
                            </li>
                        {% endif %}
                    {% endfor %}
                    
                    {% if users.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('manage_users', page=users.next_num) }}">التالي</a>
                        </li>
                    {% endif %}
                </ul>
            </nav>
        </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script>
    // تحسين تجربة المستخدم
    document.addEventListener('DOMContentLoaded', function() {
        // إضافة تأثيرات للصفوف
        const rows = document.querySelectorAll('tbody tr');
        rows.forEach((row, index) => {
            setTimeout(() => {
                row.style.opacity = '0';
                row.style.transform = 'translateX(20px)';
                row.style.transition = 'all 0.6s ease';
                
                setTimeout(() => {
                    row.style.opacity = '1';
                    row.style.transform = 'translateX(0)';
                }, 50);
            }, index * 50);
        });
    });
</script>
{% endblock %}
