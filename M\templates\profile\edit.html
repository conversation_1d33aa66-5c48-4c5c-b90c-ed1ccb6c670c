{% extends "base.html" %}

{% block title %}تعديل الملف الشخصي - {{ user.full_name }} - نظام إدارة الأرشيف العام{% endblock %}

{% block extra_css %}
<style>
    body {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        font-family: 'Cairo', sans-serif;
    }

    .edit-profile-container {
        max-width: 800px;
        margin: 2rem auto;
        padding: 0 1rem;
    }

    .edit-header {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        border-radius: 20px;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        text-align: center;
    }

    .edit-header h1 {
        color: #2c3e50;
        font-size: 2rem;
        font-weight: 700;
        margin-bottom: 0.5rem;
    }

    .edit-header p {
        color: #6c757d;
        font-size: 1rem;
        margin: 0;
    }

    .edit-form {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        border-radius: 20px;
        padding: 2rem;
        box-shadow: 0 20px 40px rgba(0,0,0,0.1);
    }

    .form-section {
        margin-bottom: 2rem;
        padding-bottom: 2rem;
        border-bottom: 2px solid #f8f9fa;
    }

    .form-section:last-child {
        border-bottom: none;
        margin-bottom: 0;
    }

    .section-header {
        display: flex;
        align-items: center;
        gap: 1rem;
        margin-bottom: 1.5rem;
    }

    .section-icon {
        width: 40px;
        height: 40px;
        border-radius: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1.1rem;
    }

    .section-title {
        color: #2c3e50;
        font-size: 1.2rem;
        font-weight: 600;
        margin: 0;
    }

    .icon-basic { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
    .icon-security { background: linear-gradient(135deg, #dc3545 0%, #fd7e14 100%); }

    .form-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 1.5rem;
    }

    .form-group {
        margin-bottom: 1.5rem;
    }

    .form-label {
        font-weight: 600;
        color: #2c3e50;
        margin-bottom: 0.5rem;
        display: block;
        font-size: 0.9rem;
    }

    .form-control {
        width: 100%;
        padding: 0.75rem;
        border: 2px solid #e9ecef;
        border-radius: 10px;
        font-size: 0.9rem;
        transition: all 0.3s ease;
        background: white;
    }

    .form-control:focus {
        outline: none;
        border-color: #667eea;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        transform: translateY(-2px);
    }

    .form-control:invalid {
        border-color: #dc3545;
    }

    .form-control:valid {
        border-color: #28a745;
    }

    .password-group {
        position: relative;
    }

    .password-toggle {
        position: absolute;
        left: 10px;
        top: 50%;
        transform: translateY(-50%);
        background: none;
        border: none;
        color: #6c757d;
        cursor: pointer;
        font-size: 1rem;
        padding: 0.5rem;
    }

    .password-toggle:hover {
        color: #667eea;
    }

    .password-strength {
        margin-top: 0.5rem;
        font-size: 0.8rem;
    }

    .strength-weak { color: #dc3545; }
    .strength-medium { color: #ffc107; }
    .strength-strong { color: #28a745; }

    .form-help {
        font-size: 0.8rem;
        color: #6c757d;
        margin-top: 0.25rem;
    }

    .form-actions {
        display: flex;
        justify-content: space-between;
        align-items: center;
        gap: 1rem;
        margin-top: 2rem;
        flex-wrap: wrap;
    }

    .action-btn {
        padding: 0.75rem 2rem;
        border: none;
        border-radius: 10px;
        font-weight: 600;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        transition: all 0.3s ease;
        cursor: pointer;
        font-size: 0.9rem;
    }

    .btn-primary {
        background: #667eea;
        color: white;
    }

    .btn-secondary {
        background: #6c757d;
        color: white;
    }

    .btn-danger {
        background: #dc3545;
        color: white;
    }

    .action-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        color: white;
        text-decoration: none;
    }

    .action-btn:disabled {
        opacity: 0.6;
        cursor: not-allowed;
        transform: none;
    }

    .alert {
        padding: 1rem;
        border-radius: 10px;
        margin-bottom: 1rem;
        border: none;
        font-weight: 500;
    }

    .alert-info {
        background: #d1ecf1;
        color: #0c5460;
        border-left: 4px solid #17a2b8;
    }

    .alert-warning {
        background: #fff3cd;
        color: #856404;
        border-left: 4px solid #ffc107;
    }

    @media (max-width: 768px) {
        .edit-profile-container {
            margin: 1rem;
            padding: 0;
        }

        .form-grid {
            grid-template-columns: 1fr;
        }

        .form-actions {
            flex-direction: column;
        }

        .action-btn {
            width: 100%;
            justify-content: center;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="edit-profile-container">
    <!-- Header -->
    <div class="edit-header">
        <h1>
            <i class="fas fa-user-edit me-2"></i>
            تعديل الملف الشخصي
        </h1>
        <p>تحديث معلوماتك الشخصية وإعدادات الحساب</p>
    </div>

    <!-- Edit Form -->
    <div class="edit-form">
        <form method="POST" id="editProfileForm">
            <!-- Basic Information Section -->
            <div class="form-section">
                <div class="section-header">
                    <div class="section-icon icon-basic">
                        <i class="fas fa-user"></i>
                    </div>
                    <h3 class="section-title">المعلومات الأساسية</h3>
                </div>

                <div class="form-grid">
                    <div class="form-group">
                        <label class="form-label" for="full_name">الاسم الكامل *</label>
                        <input type="text" 
                               class="form-control" 
                               id="full_name" 
                               name="full_name" 
                               value="{{ user.full_name or '' }}" 
                               required>
                        <div class="form-help">أدخل اسمك الكامل كما تريد أن يظهر في النظام</div>
                    </div>

                    <div class="form-group">
                        <label class="form-label" for="email">البريد الإلكتروني *</label>
                        <input type="email" 
                               class="form-control" 
                               id="email" 
                               name="email" 
                               value="{{ user.email or '' }}" 
                               required>
                        <div class="form-help">سيتم استخدامه لإرسال الإشعارات</div>
                    </div>
                </div>

                <div class="form-group">
                    <label class="form-label" for="department">القسم</label>
                    <input type="text" 
                           class="form-control" 
                           id="department" 
                           name="department" 
                           value="{{ user.department or '' }}" 
                           placeholder="أدخل اسم القسم">
                    <div class="form-help">القسم أو الإدارة التي تعمل بها</div>
                </div>

                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>ملاحظة:</strong> اسم المستخدم ({{ user.username }}) والدور ({{ user.role }}) لا يمكن تغييرهما. اتصل بمدير النظام إذا كنت بحاجة لتغييرهما.
                </div>
            </div>

            <!-- Security Section -->
            <div class="form-section">
                <div class="section-header">
                    <div class="section-icon icon-security">
                        <i class="fas fa-lock"></i>
                    </div>
                    <h3 class="section-title">الأمان وكلمة المرور</h3>
                </div>

                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>تنبيه:</strong> اترك حقول كلمة المرور فارغة إذا كنت لا تريد تغييرها.
                </div>

                <div class="form-grid">
                    <div class="form-group">
                        <label class="form-label" for="current_password">كلمة المرور الحالية</label>
                        <div class="password-group">
                            <input type="password" 
                                   class="form-control" 
                                   id="current_password" 
                                   name="current_password" 
                                   placeholder="أدخل كلمة المرور الحالية">
                            <button type="button" class="password-toggle" onclick="togglePassword('current_password')">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                        <div class="form-help">مطلوبة فقط عند تغيير كلمة المرور</div>
                    </div>

                    <div class="form-group">
                        <label class="form-label" for="new_password">كلمة المرور الجديدة</label>
                        <div class="password-group">
                            <input type="password" 
                                   class="form-control" 
                                   id="new_password" 
                                   name="new_password" 
                                   placeholder="أدخل كلمة المرور الجديدة"
                                   onkeyup="checkPasswordStrength()">
                            <button type="button" class="password-toggle" onclick="togglePassword('new_password')">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                        <div class="password-strength" id="passwordStrength"></div>
                        <div class="form-help">يجب أن تكون 6 أحرف على الأقل</div>
                    </div>
                </div>

                <div class="form-group">
                    <label class="form-label" for="confirm_password">تأكيد كلمة المرور الجديدة</label>
                    <div class="password-group">
                        <input type="password" 
                               class="form-control" 
                               id="confirm_password" 
                               name="confirm_password" 
                               placeholder="أعد إدخال كلمة المرور الجديدة"
                               onkeyup="checkPasswordMatch()">
                        <button type="button" class="password-toggle" onclick="togglePassword('confirm_password')">
                            <i class="fas fa-eye"></i>
                        </button>
                    </div>
                    <div id="passwordMatch"></div>
                </div>
            </div>

            <!-- Form Actions -->
            <div class="form-actions">
                <div>
                    <button type="submit" class="action-btn btn-primary" id="saveBtn">
                        <i class="fas fa-save"></i>
                        حفظ التغييرات
                    </button>
                </div>
                <div>
                    <a href="{{ url_for('profile') }}" class="action-btn btn-secondary">
                        <i class="fas fa-times"></i>
                        إلغاء
                    </a>
                </div>
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 PROFILE: Edit profile page loaded');
    
    // Form validation
    const form = document.getElementById('editProfileForm');
    const saveBtn = document.getElementById('saveBtn');
    
    form.addEventListener('submit', function(e) {
        const newPassword = document.getElementById('new_password').value;
        const confirmPassword = document.getElementById('confirm_password').value;
        const currentPassword = document.getElementById('current_password').value;
        
        // If trying to change password
        if (newPassword || confirmPassword) {
            if (!currentPassword) {
                e.preventDefault();
                alert('يجب إدخال كلمة المرور الحالية لتغيير كلمة المرور');
                return false;
            }
            
            if (newPassword !== confirmPassword) {
                e.preventDefault();
                alert('كلمة المرور الجديدة وتأكيدها غير متطابقين');
                return false;
            }
            
            if (newPassword.length < 6) {
                e.preventDefault();
                alert('كلمة المرور يجب أن تكون 6 أحرف على الأقل');
                return false;
            }
        }
        
        // Disable submit button to prevent double submission
        saveBtn.disabled = true;
        saveBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الحفظ...';
    });
    
    console.log('✅ PROFILE: Form validation initialized');
});

function togglePassword(fieldId) {
    const field = document.getElementById(fieldId);
    const button = field.nextElementSibling;
    const icon = button.querySelector('i');
    
    if (field.type === 'password') {
        field.type = 'text';
        icon.className = 'fas fa-eye-slash';
    } else {
        field.type = 'password';
        icon.className = 'fas fa-eye';
    }
}

function checkPasswordStrength() {
    const password = document.getElementById('new_password').value;
    const strengthDiv = document.getElementById('passwordStrength');
    
    if (!password) {
        strengthDiv.innerHTML = '';
        return;
    }
    
    let strength = 0;
    let feedback = [];
    
    if (password.length >= 6) strength++;
    else feedback.push('6 أحرف على الأقل');
    
    if (/[A-Z]/.test(password)) strength++;
    else feedback.push('حرف كبير');
    
    if (/[0-9]/.test(password)) strength++;
    else feedback.push('رقم');
    
    if (/[^A-Za-z0-9]/.test(password)) strength++;
    else feedback.push('رمز خاص');
    
    let strengthText = '';
    let strengthClass = '';
    
    if (strength < 2) {
        strengthText = 'ضعيفة';
        strengthClass = 'strength-weak';
    } else if (strength < 3) {
        strengthText = 'متوسطة';
        strengthClass = 'strength-medium';
    } else {
        strengthText = 'قوية';
        strengthClass = 'strength-strong';
    }
    
    strengthDiv.innerHTML = `<span class="${strengthClass}">قوة كلمة المرور: ${strengthText}</span>`;
    
    if (feedback.length > 0) {
        strengthDiv.innerHTML += `<br><small>يُنصح بإضافة: ${feedback.join(', ')}</small>`;
    }
}

function checkPasswordMatch() {
    const newPassword = document.getElementById('new_password').value;
    const confirmPassword = document.getElementById('confirm_password').value;
    const matchDiv = document.getElementById('passwordMatch');
    
    if (!confirmPassword) {
        matchDiv.innerHTML = '';
        return;
    }
    
    if (newPassword === confirmPassword) {
        matchDiv.innerHTML = '<small style="color: #28a745;"><i class="fas fa-check"></i> كلمات المرور متطابقة</small>';
    } else {
        matchDiv.innerHTML = '<small style="color: #dc3545;"><i class="fas fa-times"></i> كلمات المرور غير متطابقة</small>';
    }
}
</script>
{% endblock %}
