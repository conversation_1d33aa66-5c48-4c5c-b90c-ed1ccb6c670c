{% extends "base.html" %}

{% block title %}الكتب الصادرة - نظام إدارة الأرشيف العام{% endblock %}

{% block extra_css %}
<style>
    .search-section {
        background: white;
        border-radius: var(--border-radius);
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: var(--shadow-light);
    }
    
    .documents-table {
        background: white;
        border-radius: var(--border-radius);
        overflow: hidden;
        box-shadow: var(--shadow-light);
    }
    
    .table-header {
        background: linear-gradient(135deg, #e67e22 0%, #f39c12 100%);
        color: white;
        padding: 1.5rem;
        font-weight: 600;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    
    .btn-add {
        background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
        border: none;
        color: white;
        padding: 0.75rem 1.5rem;
        border-radius: var(--border-radius);
        font-weight: 600;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        transition: all 0.3s ease;
    }
    
    .btn-add:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-medium);
        color: white;
    }
    
    .filter-row {
        display: grid;
        grid-template-columns: 2fr 1fr 1fr auto;
        gap: 1rem;
        align-items: end;
    }
    
    .document-row {
        transition: all 0.3s ease;
    }
    
    .document-row:hover {
        background: rgba(230, 126, 34, 0.05);
        transform: translateX(5px);
    }
    
    .document-title {
        font-weight: 600;
        color: var(--primary-color);
        text-decoration: none;
    }
    
    .document-title:hover {
        color: #e67e22;
    }
    
    .priority-badge {
        padding: 0.25rem 0.75rem;
        border-radius: 15px;
        font-size: 0.8rem;
        font-weight: 500;
    }
    
    .priority-عادي {
        background: rgba(149, 165, 166, 0.1);
        color: #95a5a6;
    }
    
    .priority-مهم {
        background: rgba(241, 196, 15, 0.1);
        color: #f1c40f;
    }
    
    .priority-عاجل {
        background: rgba(231, 76, 60, 0.1);
        color: #e74c3c;
    }
    
    .status-badge {
        padding: 0.25rem 0.75rem;
        border-radius: 15px;
        font-size: 0.8rem;
        font-weight: 500;
    }
    
    .status-مسودة {
        background: rgba(149, 165, 166, 0.1);
        color: #95a5a6;
    }
    
    .status-جاهز_للإرسال {
        background: rgba(241, 196, 15, 0.1);
        color: #f1c40f;
    }
    
    .status-تم_الإرسال {
        background: rgba(39, 174, 96, 0.1);
        color: #27ae60;
    }
    
    .status-مؤرشف {
        background: rgba(52, 152, 219, 0.1);
        color: #3498db;
    }
    
    .action-buttons {
        display: flex;
        gap: 0.5rem;
    }
    
    .btn-action {
        padding: 0.5rem;
        border: none;
        border-radius: 6px;
        cursor: pointer;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        width: 35px;
        height: 35px;
    }
    
    .btn-view {
        background: rgba(230, 126, 34, 0.1);
        color: #e67e22;
    }
    
    .btn-edit {
        background: rgba(241, 196, 15, 0.1);
        color: #f1c40f;
    }
    
    .btn-delete {
        background: rgba(231, 76, 60, 0.1);
        color: #e74c3c;
    }
    
    .btn-action:hover {
        transform: scale(1.1);
    }
    
    .empty-state {
        text-align: center;
        padding: 4rem 2rem;
        color: #6c757d;
    }
    
    .empty-state i {
        font-size: 4rem;
        margin-bottom: 1rem;
        opacity: 0.5;
    }
</style>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">
        <i class="fas fa-paper-plane me-2"></i>
        الكتب الصادرة
    </h1>
    <a href="{{ url_for('add_outgoing') }}" class="btn-add">
        <i class="fas fa-plus me-2"></i>
        إضافة كتاب صادر جديد
    </a>
</div>

<!-- Search and Filter Section -->
<div class="search-section">
    <form method="GET" class="filter-row">
        <div>
            <label class="form-label">البحث في الكتب الصادرة</label>
            <input type="text" name="search" class="form-control" 
                   placeholder="ابحث في الموضوع أو الجهة المرسل إليها..." 
                   value="{{ search }}">
        </div>
        
        <div>
            <label class="form-label">الحالة</label>
            <select name="status" class="form-select">
                <option value="">جميع الحالات</option>
                <option value="مسودة" {{ 'selected' if status == 'مسودة' }}>مسودة</option>
                <option value="جاهز للإرسال" {{ 'selected' if status == 'جاهز للإرسال' }}>جاهز للإرسال</option>
                <option value="تم الإرسال" {{ 'selected' if status == 'تم الإرسال' }}>تم الإرسال</option>
                <option value="مؤرشف" {{ 'selected' if status == 'مؤرشف' }}>مؤرشف</option>
            </select>
        </div>
        
        <div>
            <label class="form-label">الأولوية</label>
            <select name="priority" class="form-select">
                <option value="">جميع الأولويات</option>
                <option value="عادي" {{ 'selected' if priority == 'عادي' }}>عادي</option>
                <option value="مهم" {{ 'selected' if priority == 'مهم' }}>مهم</option>
                <option value="عاجل" {{ 'selected' if priority == 'عاجل' }}>عاجل</option>
            </select>
        </div>
        
        <div>
            <button type="submit" class="btn btn-primary">
                <i class="fas fa-search me-2"></i>
                بحث
            </button>
        </div>
    </form>
</div>

<!-- Documents Table -->
<div class="documents-table">
    <div class="table-header">
        <span>
            <i class="fas fa-list me-2"></i>
            قائمة الكتب الصادرة ({{ documents.total }} كتاب)
        </span>
        <span class="badge bg-light text-dark">
            صفحة {{ documents.page }} من {{ documents.pages }}
        </span>
    </div>
    
    {% if documents.items %}
    <div class="table-responsive">
        <table class="table table-hover mb-0">
            <thead>
                <tr>
                    <th>رقم الصادر</th>
                    <th>الموضوع</th>
                    <th>الجهة المرسل إليها</th>
                    <th>تاريخ الإرسال</th>
                    <th>الأولوية</th>
                    <th>الحالة</th>
                    <th>المُعِد</th>
                    <th>الإجراءات</th>
                </tr>
            </thead>
            <tbody>
                {% for document in documents.items %}
                <tr class="document-row">
                    <td>
                        <strong class="text-primary">{{ document.outgoing_number }}</strong>
                    </td>
                    <td>
                        <a href="{{ url_for('view_outgoing', id=document.id) }}" 
                           class="document-title">{{ document.subject }}</a>
                        {% if document.notes %}
                            <small class="d-block text-muted">
                                {{ document.notes[:50] }}{% if document.notes|length > 50 %}...{% endif %}
                            </small>
                        {% endif %}
                    </td>
                    <td>{{ document.recipient_name }}</td>
                    <td>{{ document.sent_date.strftime('%Y/%m/%d') }}</td>
                    <td>
                        <span class="priority-badge priority-{{ document.priority }}">
                            {{ document.priority }}
                        </span>
                    </td>
                    <td>
                        <span class="status-badge status-{{ document.status.replace(' ', '_') }}">
                            {{ document.status }}
                        </span>
                    </td>
                    <td>{{ document.preparer.full_name if document.preparer else 'غير محدد' }}</td>
                    <td>
                        <div class="action-buttons">
                            <a href="{{ url_for('view_outgoing', id=document.id) }}" 
                               class="btn-action btn-view" title="عرض">
                                <i class="fas fa-eye"></i>
                            </a>
                            {% if current_user.is_admin() or document.prepared_by == current_user.id %}
                            <a href="{{ url_for('edit_outgoing', id=document.id) }}" 
                               class="btn-action btn-edit" title="تعديل">
                                <i class="fas fa-edit"></i>
                            </a>
                            <button type="button" class="btn-action btn-delete" 
                                    onclick="confirmDelete({{ document.id }}, '{{ document.outgoing_number }}')" 
                                    title="حذف">
                                <i class="fas fa-trash"></i>
                            </button>
                            {% endif %}
                        </div>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
    
    <!-- Pagination -->
    {% if documents.pages > 1 %}
    <div class="pagination-wrapper">
        <nav aria-label="تنقل بين الصفحات">
            <ul class="pagination justify-content-center mb-0">
                {% if documents.has_prev %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('outgoing_documents', page=documents.prev_num, search=search, status=status, priority=priority) }}">
                            السابق
                        </a>
                    </li>
                {% endif %}
                
                {% for page_num in documents.iter_pages() %}
                    {% if page_num %}
                        {% if page_num != documents.page %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('outgoing_documents', page=page_num, search=search, status=status, priority=priority) }}">
                                    {{ page_num }}
                                </a>
                            </li>
                        {% else %}
                            <li class="page-item active">
                                <span class="page-link">{{ page_num }}</span>
                            </li>
                        {% endif %}
                    {% else %}
                        <li class="page-item disabled">
                            <span class="page-link">...</span>
                        </li>
                    {% endif %}
                {% endfor %}
                
                {% if documents.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('outgoing_documents', page=documents.next_num, search=search, status=status, priority=priority) }}">
                            التالي
                        </a>
                    </li>
                {% endif %}
            </ul>
        </nav>
    </div>
    {% endif %}
    
    {% else %}
    <div class="empty-state">
        <i class="fas fa-paper-plane"></i>
        <h5>لا توجد كتب صادرة</h5>
        <p>لم يتم العثور على أي كتب صادرة تطابق معايير البحث</p>
        <a href="{{ url_for('add_outgoing') }}" class="btn btn-primary">
            <i class="fas fa-plus me-2"></i>
            إضافة أول كتاب صادر
        </a>
    </div>
    {% endif %}
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تأكيد الحذف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من حذف الكتاب الصادر رقم "<span id="documentNumber"></span>"؟</p>
                <p class="text-danger"><strong>تحذير:</strong> هذا الإجراء لا يمكن التراجع عنه.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-trash me-2"></i>
                        حذف
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    function confirmDelete(documentId, documentNumber) {
        document.getElementById('documentNumber').textContent = documentNumber;
        document.getElementById('deleteForm').action = `/outgoing/${documentId}/delete`;
        
        const modal = new bootstrap.Modal(document.getElementById('deleteModal'));
        modal.show();
    }
    
    // Auto-submit form on filter change
    document.querySelectorAll('select[name="status"], select[name="priority"]').forEach(select => {
        select.addEventListener('change', function() {
            this.form.submit();
        });
    });
    
    // Highlight search terms
    const searchTerm = '{{ search }}';
    if (searchTerm) {
        document.querySelectorAll('.document-title').forEach(title => {
            const text = title.textContent;
            const highlightedText = text.replace(
                new RegExp(searchTerm, 'gi'), 
                '<mark>$&</mark>'
            );
            title.innerHTML = highlightedText;
        });
    }
</script>
{% endblock %}
