{% extends "base.html" %}

{% block title %}الميزات المحسنة لإدارة الوثائق - نظام إدارة الأرشيف العام{% endblock %}

{% block extra_css %}
<style>
    body {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        font-family: 'Cairo', sans-serif;
    }

    .enhanced-container {
        max-width: 1400px;
        margin: 2rem auto;
        padding: 0 1rem;
    }

    .enhanced-header {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        border-radius: 20px;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        text-align: center;
    }

    .enhanced-header h1 {
        color: #2c3e50;
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 0.5rem;
    }

    .enhanced-header p {
        color: #6c757d;
        font-size: 1.1rem;
        margin: 0;
    }

    .features-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
        gap: 2rem;
        margin-bottom: 2rem;
    }

    .feature-card {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        border-radius: 20px;
        padding: 2rem;
        box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        transition: transform 0.3s ease;
    }

    .feature-card:hover {
        transform: translateY(-5px);
    }

    .feature-header {
        display: flex;
        align-items: center;
        margin-bottom: 1.5rem;
    }

    .feature-icon {
        width: 60px;
        height: 60px;
        border-radius: 15px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
        color: white;
        margin-left: 1rem;
    }

    .feature-title {
        flex: 1;
    }

    .feature-title h3 {
        color: #2c3e50;
        font-size: 1.3rem;
        font-weight: 600;
        margin: 0 0 0.25rem 0;
    }

    .feature-title p {
        color: #6c757d;
        font-size: 0.9rem;
        margin: 0;
    }

    .feature-stats {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
        margin-bottom: 1.5rem;
    }

    .stat-item {
        text-align: center;
        padding: 1rem;
        background: #f8f9fa;
        border-radius: 10px;
    }

    .stat-number {
        font-size: 1.5rem;
        font-weight: 700;
        color: #2c3e50;
        margin-bottom: 0.25rem;
    }

    .stat-label {
        font-size: 0.8rem;
        color: #6c757d;
        margin: 0;
    }

    .feature-progress {
        margin-bottom: 1rem;
    }

    .progress-label {
        display: flex;
        justify-content: space-between;
        margin-bottom: 0.5rem;
        font-size: 0.9rem;
        color: #2c3e50;
    }

    .progress-bar-container {
        height: 8px;
        background: #e9ecef;
        border-radius: 4px;
        overflow: hidden;
    }

    .progress-bar {
        height: 100%;
        background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
        border-radius: 4px;
        transition: width 0.3s ease;
    }

    .feature-actions {
        display: flex;
        gap: 0.5rem;
        flex-wrap: wrap;
    }

    .action-btn {
        padding: 0.5rem 1rem;
        border: none;
        border-radius: 8px;
        font-size: 0.8rem;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
    }

    .btn-primary {
        background: #667eea;
        color: white;
    }

    .btn-success {
        background: #28a745;
        color: white;
    }

    .btn-info {
        background: #17a2b8;
        color: white;
    }

    .btn-warning {
        background: #ffc107;
        color: #212529;
    }

    .action-btn:hover {
        transform: translateY(-2px);
        text-decoration: none;
        color: inherit;
    }

    .numbering-system {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }

    .storage-management {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    }

    .location-tracking {
        background: linear-gradient(135deg, #17a2b8 0%, #6f42c1 100%);
    }

    .metadata-enhancement {
        background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
    }

    .storage-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
        gap: 1rem;
        margin-top: 1rem;
    }

    .cabinet-card {
        background: #f8f9fa;
        border-radius: 10px;
        padding: 1rem;
        text-align: center;
        transition: all 0.3s ease;
    }

    .cabinet-card:hover {
        background: #e9ecef;
        transform: translateY(-2px);
    }

    .cabinet-status {
        width: 12px;
        height: 12px;
        border-radius: 50%;
        display: inline-block;
        margin-left: 0.5rem;
    }

    .status-available { background: #28a745; }
    .status-nearly-full { background: #ffc107; }
    .status-full { background: #dc3545; }

    .cabinet-name {
        font-weight: 600;
        color: #2c3e50;
        margin-bottom: 0.5rem;
    }

    .cabinet-capacity {
        font-size: 0.8rem;
        color: #6c757d;
    }

    .search-enhanced {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        border-radius: 20px;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: 0 20px 40px rgba(0,0,0,0.1);
    }

    .search-form {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
        margin-bottom: 1rem;
    }

    .form-group {
        display: flex;
        flex-direction: column;
    }

    .form-label {
        font-weight: 600;
        color: #2c3e50;
        margin-bottom: 0.5rem;
        font-size: 0.9rem;
    }

    .form-control, .form-select {
        padding: 0.5rem;
        border: 1px solid #ddd;
        border-radius: 8px;
        font-size: 0.9rem;
    }

    .form-control:focus, .form-select:focus {
        outline: none;
        border-color: #667eea;
        box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.1);
    }

    .search-results {
        margin-top: 1rem;
        max-height: 400px;
        overflow-y: auto;
    }

    .result-item {
        background: #f8f9fa;
        border-radius: 8px;
        padding: 1rem;
        margin-bottom: 0.5rem;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .result-info {
        flex: 1;
    }

    .result-title {
        font-weight: 600;
        color: #2c3e50;
        margin-bottom: 0.25rem;
    }

    .result-meta {
        font-size: 0.8rem;
        color: #6c757d;
    }

    .result-badges {
        display: flex;
        gap: 0.5rem;
        flex-wrap: wrap;
    }

    .badge {
        padding: 0.25rem 0.5rem;
        border-radius: 12px;
        font-size: 0.7rem;
        font-weight: 600;
    }

    .badge-success { background: #d4edda; color: #155724; }
    .badge-warning { background: #fff3cd; color: #856404; }
    .badge-danger { background: #f8d7da; color: #721c24; }
    .badge-info { background: #d1ecf1; color: #0c5460; }

    @media (max-width: 768px) {
        .enhanced-container {
            margin: 1rem;
            padding: 0;
        }

        .features-grid {
            grid-template-columns: 1fr;
        }

        .feature-stats {
            grid-template-columns: 1fr;
        }

        .search-form {
            grid-template-columns: 1fr;
        }

        .storage-grid {
            grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="enhanced-container">
    <!-- Header -->
    <div class="enhanced-header">
        <h1>
            <i class="fas fa-cogs me-3"></i>
            الميزات المحسنة لإدارة الوثائق
        </h1>
        <p>نظام شامل للميزات المتقدمة في إدارة الوثائق والتخزين والتتبع</p>
    </div>

    <!-- Enhanced Search -->
    <div class="search-enhanced">
        <h2>
            <i class="fas fa-search me-2"></i>
            البحث المحسن
        </h2>
        
        <form class="search-form" id="enhancedSearchForm">
            <div class="form-group">
                <label class="form-label">البحث النصي</label>
                <input type="text" class="form-control" name="q" placeholder="ابحث في العنوان والوصف...">
            </div>
            <div class="form-group">
                <label class="form-label">نوع الوثيقة</label>
                <select class="form-select" name="type">
                    <option value="">جميع الأنواع</option>
                    <option value="وثائق إدارية">وثائق إدارية</option>
                    <option value="وثائق مالية">وثائق مالية</option>
                    <option value="وثائق قانونية">وثائق قانونية</option>
                </select>
            </div>
            <div class="form-group">
                <label class="form-label">لها رقم</label>
                <select class="form-select" name="has_number">
                    <option value="">الكل</option>
                    <option value="true">نعم</option>
                    <option value="false">لا</option>
                </select>
            </div>
            <div class="form-group">
                <label class="form-label">لها موقع تخزين</label>
                <select class="form-select" name="has_storage">
                    <option value="">الكل</option>
                    <option value="true">نعم</option>
                    <option value="false">لا</option>
                </select>
            </div>
            <div class="form-group">
                <label class="form-label">من تاريخ</label>
                <input type="date" class="form-control" name="date_from">
            </div>
            <div class="form-group">
                <label class="form-label">إلى تاريخ</label>
                <input type="date" class="form-control" name="date_to">
            </div>
        </form>
        
        <div class="text-center">
            <button type="button" class="action-btn btn-primary" onclick="performEnhancedSearch()">
                <i class="fas fa-search"></i>
                بحث محسن
            </button>
        </div>
        
        <div class="search-results" id="searchResults" style="display: none;">
            <!-- Search results will be populated here -->
        </div>
    </div>

    <!-- Features Grid -->
    <div class="features-grid">
        <!-- Document Numbering System -->
        <div class="feature-card">
            <div class="feature-header">
                <div class="feature-icon numbering-system">
                    <i class="fas fa-hashtag"></i>
                </div>
                <div class="feature-title">
                    <h3>نظام ترقيم الوثائق</h3>
                    <p>نظام ترقيم تلقائي ومنظم للوثائق</p>
                </div>
            </div>
            
            <div class="feature-stats">
                <div class="stat-item">
                    <div class="stat-number">{{ numbering_system.total_this_year or 0 }}</div>
                    <div class="stat-label">هذا العام</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">{{ numbering_system.next_sequence or 1 }}</div>
                    <div class="stat-label">الرقم التالي</div>
                </div>
            </div>
            
            <div class="feature-progress">
                <div class="progress-label">
                    <span>التقدم السنوي</span>
                    <span>{{ numbering_system.total_this_year or 0 }}/1000</span>
                </div>
                <div class="progress-bar-container">
                    <div class="progress-bar" style="width: {{ ((numbering_system.total_this_year or 0) / 1000 * 100)|round }}%"></div>
                </div>
            </div>
            
            <div class="feature-actions">
                <button type="button" class="action-btn btn-primary" onclick="openNumberingSettings()">
                    <i class="fas fa-cog"></i>
                    إعدادات الترقيم
                </button>
                <button type="button" class="action-btn btn-info" onclick="showNumberingStats()">
                    <i class="fas fa-chart-bar"></i>
                    إحصائيات
                </button>
                <button type="button" class="action-btn btn-success" onclick="assignNumbersToDocuments()">
                    <i class="fas fa-hashtag"></i>
                    ترقيم الوثائق
                </button>
            </div>
        </div>

        <!-- Storage Management -->
        <div class="feature-card">
            <div class="feature-header">
                <div class="feature-icon storage-management">
                    <i class="fas fa-archive"></i>
                </div>
                <div class="feature-title">
                    <h3>إدارة التخزين</h3>
                    <p>إدارة شاملة لمواقع التخزين والخزائن</p>
                </div>
            </div>
            
            <div class="feature-stats">
                <div class="stat-item">
                    <div class="stat-number">{{ storage_data.capacity_analysis.total_capacity or 0 }}</div>
                    <div class="stat-label">السعة الإجمالية</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">{{ storage_data.capacity_analysis.utilization_percentage|round or 0 }}%</div>
                    <div class="stat-label">نسبة الاستخدام</div>
                </div>
            </div>
            
            <div class="feature-progress">
                <div class="progress-label">
                    <span>استخدام التخزين</span>
                    <span>{{ storage_data.capacity_analysis.total_used or 0 }}/{{ storage_data.capacity_analysis.total_capacity or 0 }}</span>
                </div>
                <div class="progress-bar-container">
                    <div class="progress-bar" style="width: {{ storage_data.capacity_analysis.utilization_percentage|round or 0 }}%"></div>
                </div>
            </div>
            
            <div class="storage-grid">
                {% for cabinet in storage_data.cabinets[:6] %}
                <div class="cabinet-card">
                    <div class="cabinet-name">
                        <span class="cabinet-status status-{{ cabinet.status }}"></span>
                        {{ cabinet.name }}
                    </div>
                    <div class="cabinet-capacity">{{ cabinet.used }}/{{ cabinet.capacity }}</div>
                </div>
                {% endfor %}
            </div>
            
            <div class="feature-actions">
                <button type="button" class="action-btn btn-success" onclick="addNewCabinet()">
                    <i class="fas fa-plus"></i>
                    إضافة خزانة
                </button>
                <button type="button" class="action-btn btn-info" onclick="showStorageMap()">
                    <i class="fas fa-map"></i>
                    خريطة التخزين
                </button>
                <button type="button" class="action-btn btn-warning" onclick="assignStorageToDocuments()">
                    <i class="fas fa-archive"></i>
                    تخصيص التخزين
                </button>
            </div>
        </div>

        <!-- Location Tracking -->
        <div class="feature-card">
            <div class="feature-header">
                <div class="feature-icon location-tracking">
                    <i class="fas fa-map-marker-alt"></i>
                </div>
                <div class="feature-title">
                    <h3>تتبع المواقع الفيزيائية</h3>
                    <p>تتبع دقيق للمواقع الفيزيائية للوثائق</p>
                </div>
            </div>
            
            <div class="feature-stats">
                <div class="stat-item">
                    <div class="stat-number">{{ location_tracking.total_tracked or 0 }}</div>
                    <div class="stat-label">وثائق متتبعة</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">{{ location_tracking.tracking_statistics.fully_tracked or 0 }}</div>
                    <div class="stat-label">تتبع كامل</div>
                </div>
            </div>
            
            <div class="feature-progress">
                <div class="progress-label">
                    <span>نسبة التتبع</span>
                    <span>{{ ((location_tracking.tracking_statistics.fully_tracked or 0) / (enhanced_stats.total_documents or 1) * 100)|round }}%</span>
                </div>
                <div class="progress-bar-container">
                    <div class="progress-bar" style="width: {{ ((location_tracking.tracking_statistics.fully_tracked or 0) / (enhanced_stats.total_documents or 1) * 100)|round }}%"></div>
                </div>
            </div>
            
            <div class="feature-actions">
                <button type="button" class="action-btn btn-primary" onclick="scanQRLocation()">
                    <i class="fas fa-qrcode"></i>
                    مسح QR
                </button>
                <button type="button" class="action-btn btn-warning" onclick="showUntrackedDocuments()">
                    <i class="fas fa-exclamation-triangle"></i>
                    غير متتبعة
                </button>
                <button type="button" class="action-btn btn-success" onclick="updatePhysicalLocations()">
                    <i class="fas fa-map-marker-alt"></i>
                    تحديث المواقع
                </button>
            </div>
        </div>

        <!-- Metadata Enhancement -->
        <div class="feature-card">
            <div class="feature-header">
                <div class="feature-icon metadata-enhancement">
                    <i class="fas fa-tags"></i>
                </div>
                <div class="feature-title">
                    <h3>تحسين البيانات الوصفية</h3>
                    <p>إدارة شاملة للبيانات الوصفية المحسنة</p>
                </div>
            </div>
            
            <div class="feature-stats">
                <div class="stat-item">
                    <div class="stat-number">{{ enhanced_stats.metadata_completion.complete or 0 }}</div>
                    <div class="stat-label">بيانات كاملة</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">{{ enhanced_stats.metadata_completion.partial or 0 }}</div>
                    <div class="stat-label">بيانات جزئية</div>
                </div>
            </div>
            
            <div class="feature-progress">
                <div class="progress-label">
                    <span>اكتمال البيانات</span>
                    <span>{{ ((enhanced_stats.metadata_completion.complete or 0) / (enhanced_stats.total_documents or 1) * 100)|round }}%</span>
                </div>
                <div class="progress-bar-container">
                    <div class="progress-bar" style="width: {{ ((enhanced_stats.metadata_completion.complete or 0) / (enhanced_stats.total_documents or 1) * 100)|round }}%"></div>
                </div>
            </div>
            
            <div class="feature-actions">
                <button type="button" class="action-btn btn-primary" onclick="enhanceMetadata()">
                    <i class="fas fa-edit"></i>
                    تحسين البيانات
                </button>
                <button type="button" class="action-btn btn-info" onclick="autoEnhanceMetadata()">
                    <i class="fas fa-robot"></i>
                    تحسين تلقائي
                </button>
                <button type="button" class="action-btn btn-warning" onclick="validateMetadata()">
                    <i class="fas fa-check-circle"></i>
                    التحقق من البيانات
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 ENHANCED: Enhanced document management page loaded');
    
    // Initialize interactive features
    initializeFeatureCards();
    initializeProgressBars();
    
    console.log('✅ ENHANCED: Interactive features initialized');
});

function initializeFeatureCards() {
    const featureCards = document.querySelectorAll('.feature-card');
    featureCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-5px) scale(1.02)';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
        });
    });
}

function initializeProgressBars() {
    const progressBars = document.querySelectorAll('.progress-bar');
    progressBars.forEach(bar => {
        const width = bar.style.width;
        bar.style.width = '0%';
        setTimeout(() => {
            bar.style.width = width;
        }, 500);
    });
}

function performEnhancedSearch() {
    const form = document.getElementById('enhancedSearchForm');
    const formData = new FormData(form);
    const params = new URLSearchParams();
    
    for (let [key, value] of formData.entries()) {
        if (value.trim()) {
            params.append(key, value);
        }
    }
    
    console.log('🔍 ENHANCED: Performing enhanced search...');
    
    fetch(`/api/documents/search-enhanced?${params.toString()}`)
        .then(response => response.json())
        .then(data => {
            displaySearchResults(data);
        })
        .catch(error => {
            console.error('❌ ENHANCED: Search error:', error);
            alert('حدث خطأ في البحث');
        });
}

function displaySearchResults(data) {
    const resultsContainer = document.getElementById('searchResults');
    
    if (!data.success || data.results.length === 0) {
        resultsContainer.innerHTML = '<div class="text-center py-3 text-muted">لا توجد نتائج</div>';
        resultsContainer.style.display = 'block';
        return;
    }
    
    let html = '';
    data.results.forEach(result => {
        const trackingBadge = getTrackingBadge(result.tracking_status);
        const completionBadge = getCompletionBadge(result.metadata_completion);
        
        html += `
            <div class="result-item">
                <div class="result-info">
                    <div class="result-title">${result.title}</div>
                    <div class="result-meta">
                        ${result.document_type} • ${new Date(result.created_at).toLocaleDateString('ar-SA')}
                        ${result.document_number ? ' • رقم: ' + result.document_number : ''}
                    </div>
                </div>
                <div class="result-badges">
                    ${trackingBadge}
                    ${completionBadge}
                    <a href="${result.url}" class="action-btn btn-primary btn-sm">
                        <i class="fas fa-eye"></i> عرض
                    </a>
                </div>
            </div>
        `;
    });
    
    resultsContainer.innerHTML = html;
    resultsContainer.style.display = 'block';
    
    console.log(`✅ ENHANCED: Displayed ${data.results.length} search results`);
}

function getTrackingBadge(status) {
    const badges = {
        'fully_tracked': '<span class="badge badge-success">متتبع بالكامل</span>',
        'partially_tracked': '<span class="badge badge-warning">متتبع جزئياً</span>',
        'not_tracked': '<span class="badge badge-danger">غير متتبع</span>'
    };
    return badges[status] || '';
}

function getCompletionBadge(completion) {
    if (completion >= 80) {
        return '<span class="badge badge-success">بيانات كاملة</span>';
    } else if (completion >= 50) {
        return '<span class="badge badge-warning">بيانات جزئية</span>';
    } else {
        return '<span class="badge badge-danger">بيانات ناقصة</span>';
    }
}

// CRITICAL: Enhanced Features Functions
function openNumberingSettings() {
    console.log('🔧 ENHANCED: Opening numbering settings...');

    const modal = createModal('إعدادات نظام الترقيم', `
        <form id="numberingSettingsForm">
            <div class="mb-3">
                <label class="form-label">بادئة الترقيم</label>
                <select class="form-control" name="prefix">
                    <option value="DOC">DOC - وثائق عامة</option>
                    <option value="ARCH">ARCH - أرشيف</option>
                    <option value="ADMIN">ADMIN - إدارية</option>
                    <option value="FIN">FIN - مالية</option>
                    <option value="LEGAL">LEGAL - قانونية</option>
                </select>
            </div>
            <div class="mb-3">
                <label class="form-label">تنسيق الترقيم</label>
                <input type="text" class="form-control" name="format" value="{prefix}-{year}-{sequence:04d}" readonly>
            </div>
            <div class="mb-3">
                <label class="form-label">الترقيم التلقائي</label>
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" name="auto_assign" checked>
                    <label class="form-check-label">تفعيل الترقيم التلقائي للوثائق الجديدة</label>
                </div>
            </div>
        </form>
    `, [
        { text: 'حفظ الإعدادات', class: 'btn-primary', onclick: 'saveNumberingSettings()' },
        { text: 'إلغاء', class: 'btn-secondary', onclick: 'closeModal()' }
    ]);
}

function saveNumberingSettings() {
    const form = document.getElementById('numberingSettingsForm');
    const formData = new FormData(form);

    console.log('💾 ENHANCED: Saving numbering settings...');

    fetch('/api/enhanced/numbering-settings', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('تم حفظ إعدادات الترقيم بنجاح', 'success');
            closeModal();
            location.reload();
        } else {
            showNotification('حدث خطأ في حفظ الإعدادات', 'error');
        }
    })
    .catch(error => {
        console.error('❌ ENHANCED: Error saving settings:', error);
        showNotification('حدث خطأ في الاتصال', 'error');
    });
}

function assignNumbersToDocuments() {
    console.log('🔢 ENHANCED: Assigning numbers to documents...');

    if (!confirm('هل تريد ترقيم جميع الوثائق التي لا تحمل أرقاماً؟')) {
        return;
    }

    fetch('/api/enhanced/assign-numbers', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification(`تم ترقيم ${data.assigned_count} وثيقة بنجاح`, 'success');
            location.reload();
        } else {
            showNotification('حدث خطأ في ترقيم الوثائق', 'error');
        }
    })
    .catch(error => {
        console.error('❌ ENHANCED: Error assigning numbers:', error);
        showNotification('حدث خطأ في الاتصال', 'error');
    });
}

function addNewCabinet() {
    console.log('📦 ENHANCED: Adding new cabinet...');

    const modal = createModal('إضافة خزانة جديدة', `
        <form id="newCabinetForm">
            <div class="mb-3">
                <label class="form-label">اسم الخزانة</label>
                <input type="text" class="form-control" name="name" placeholder="مثال: خزانة A-1" required>
            </div>
            <div class="mb-3">
                <label class="form-label">الموقع</label>
                <select class="form-control" name="location" required>
                    <option value="">اختر الموقع</option>
                    <option value="1">الطابق الأول - القسم الإداري</option>
                    <option value="2">الطابق الأول - القسم المالي</option>
                    <option value="3">الطابق الثاني - القسم القانوني</option>
                    <option value="4">الطابق الثاني - الأرشيف العام</option>
                    <option value="5">الطابق الثالث - الوثائق التاريخية</option>
                </select>
            </div>
            <div class="mb-3">
                <label class="form-label">السعة (عدد الوثائق)</label>
                <input type="number" class="form-control" name="capacity" value="100" min="1" required>
            </div>
            <div class="mb-3">
                <label class="form-label">ملاحظات</label>
                <textarea class="form-control" name="notes" rows="3" placeholder="ملاحظات إضافية (اختياري)"></textarea>
            </div>
        </form>
    `, [
        { text: 'إضافة الخزانة', class: 'btn-success', onclick: 'saveNewCabinet()' },
        { text: 'إلغاء', class: 'btn-secondary', onclick: 'closeModal()' }
    ]);
}

function saveNewCabinet() {
    const form = document.getElementById('newCabinetForm');
    const formData = new FormData(form);

    console.log('💾 ENHANCED: Saving new cabinet...');

    fetch('/api/enhanced/add-cabinet', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('تم إضافة الخزانة بنجاح', 'success');
            closeModal();
            location.reload();
        } else {
            showNotification('حدث خطأ في إضافة الخزانة', 'error');
        }
    })
    .catch(error => {
        console.error('❌ ENHANCED: Error adding cabinet:', error);
        showNotification('حدث خطأ في الاتصال', 'error');
    });
}

function assignStorageToDocuments() {
    console.log('📦 ENHANCED: Assigning storage to documents...');

    const modal = createModal('تخصيص التخزين للوثائق', `
        <form id="storageAssignmentForm">
            <div class="mb-3">
                <label class="form-label">اختيار الوثائق</label>
                <select class="form-control" name="selection_type">
                    <option value="unassigned">الوثائق غير المخصصة فقط</option>
                    <option value="all">جميع الوثائق</option>
                    <option value="by_type">حسب نوع الوثيقة</option>
                </select>
            </div>
            <div class="mb-3" id="documentTypeDiv" style="display: none;">
                <label class="form-label">نوع الوثيقة</label>
                <select class="form-control" name="document_type">
                    <option value="وثائق إدارية">وثائق إدارية</option>
                    <option value="وثائق مالية">وثائق مالية</option>
                    <option value="وثائق قانونية">وثائق قانونية</option>
                </select>
            </div>
            <div class="mb-3">
                <label class="form-label">استراتيجية التخصيص</label>
                <select class="form-control" name="assignment_strategy">
                    <option value="auto">تلقائي (توزيع متوازن)</option>
                    <option value="by_type">حسب نوع الوثيقة</option>
                    <option value="by_date">حسب تاريخ الإنشاء</option>
                </select>
            </div>
        </form>
    `, [
        { text: 'تخصيص التخزين', class: 'btn-primary', onclick: 'executeStorageAssignment()' },
        { text: 'إلغاء', class: 'btn-secondary', onclick: 'closeModal()' }
    ]);

    // Add event listener for selection type
    document.querySelector('select[name="selection_type"]').addEventListener('change', function() {
        const typeDiv = document.getElementById('documentTypeDiv');
        typeDiv.style.display = this.value === 'by_type' ? 'block' : 'none';
    });
}

function executeStorageAssignment() {
    const form = document.getElementById('storageAssignmentForm');
    const formData = new FormData(form);

    console.log('📦 ENHANCED: Executing storage assignment...');

    fetch('/api/enhanced/assign-storage', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification(`تم تخصيص التخزين لـ ${data.assigned_count} وثيقة`, 'success');
            closeModal();
            location.reload();
        } else {
            showNotification('حدث خطأ في تخصيص التخزين', 'error');
        }
    })
    .catch(error => {
        console.error('❌ ENHANCED: Error assigning storage:', error);
        showNotification('حدث خطأ في الاتصال', 'error');
    });
}

// Additional Enhanced Functions
function updatePhysicalLocations() {
    console.log('📍 ENHANCED: Updating physical locations...');

    const modal = createModal('تحديث المواقع الفيزيائية', `
        <div class="mb-3">
            <p>اختر طريقة تحديث المواقع الفيزيائية للوثائق:</p>
        </div>
        <form id="locationUpdateForm">
            <div class="mb-3">
                <label class="form-label">طريقة التحديث</label>
                <select class="form-control" name="update_method">
                    <option value="scan_qr">مسح رموز QR</option>
                    <option value="bulk_update">تحديث جماعي</option>
                    <option value="manual_entry">إدخال يدوي</option>
                </select>
            </div>
            <div class="mb-3">
                <label class="form-label">نطاق التحديث</label>
                <select class="form-control" name="update_scope">
                    <option value="untracked">الوثائق غير المتتبعة فقط</option>
                    <option value="all">جميع الوثائق</option>
                    <option value="specific_location">موقع محدد</option>
                </select>
            </div>
        </form>
    `, [
        { text: 'بدء التحديث', class: 'btn-primary', onclick: 'startLocationUpdate()' },
        { text: 'إلغاء', class: 'btn-secondary', onclick: 'closeModal()' }
    ]);
}

function startLocationUpdate() {
    const form = document.getElementById('locationUpdateForm');
    const formData = new FormData(form);

    console.log('📍 ENHANCED: Starting location update...');

    fetch('/api/enhanced/update-locations', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification(`تم تحديث مواقع ${data.updated_count} وثيقة`, 'success');
            closeModal();
            location.reload();
        } else {
            showNotification('حدث خطأ في تحديث المواقع', 'error');
        }
    })
    .catch(error => {
        console.error('❌ ENHANCED: Error updating locations:', error);
        showNotification('حدث خطأ في الاتصال', 'error');
    });
}

function enhanceMetadata() {
    console.log('🏷️ ENHANCED: Enhancing metadata...');

    const modal = createModal('تحسين البيانات الوصفية', `
        <form id="metadataEnhanceForm">
            <div class="mb-3">
                <label class="form-label">نوع التحسين</label>
                <select class="form-control" name="enhancement_type">
                    <option value="auto_tags">إضافة كلمات مفتاحية تلقائية</option>
                    <option value="categorization">تصنيف تلقائي</option>
                    <option value="validation">التحقق من صحة البيانات</option>
                    <option value="completion">إكمال البيانات الناقصة</option>
                </select>
            </div>
            <div class="mb-3">
                <label class="form-label">نطاق التحسين</label>
                <select class="form-control" name="scope">
                    <option value="incomplete">الوثائق ذات البيانات الناقصة</option>
                    <option value="all">جميع الوثائق</option>
                    <option value="recent">الوثائق الحديثة (آخر 30 يوم)</option>
                </select>
            </div>
            <div class="mb-3">
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" name="backup_original" checked>
                    <label class="form-check-label">إنشاء نسخة احتياطية من البيانات الأصلية</label>
                </div>
            </div>
        </form>
    `, [
        { text: 'بدء التحسين', class: 'btn-primary', onclick: 'executeMetadataEnhancement()' },
        { text: 'إلغاء', class: 'btn-secondary', onclick: 'closeModal()' }
    ]);
}

function executeMetadataEnhancement() {
    const form = document.getElementById('metadataEnhanceForm');
    const formData = new FormData(form);

    console.log('🏷️ ENHANCED: Executing metadata enhancement...');

    fetch('/api/enhanced/enhance-metadata', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification(`تم تحسين بيانات ${data.enhanced_count} وثيقة`, 'success');
            closeModal();
            location.reload();
        } else {
            showNotification('حدث خطأ في تحسين البيانات', 'error');
        }
    })
    .catch(error => {
        console.error('❌ ENHANCED: Error enhancing metadata:', error);
        showNotification('حدث خطأ في الاتصال', 'error');
    });
}

// Helper Functions
function createModal(title, content, buttons) {
    const modalHtml = `
        <div class="modal fade" id="enhancedModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">${title}</h5>
                        <button type="button" class="btn-close" onclick="closeModal()"></button>
                    </div>
                    <div class="modal-body">
                        ${content}
                    </div>
                    <div class="modal-footer">
                        ${buttons.map(btn => `<button type="button" class="btn ${btn.class}" onclick="${btn.onclick}">${btn.text}</button>`).join('')}
                    </div>
                </div>
            </div>
        </div>
    `;

    // Remove existing modal
    const existingModal = document.getElementById('enhancedModal');
    if (existingModal) {
        existingModal.remove();
    }

    // Add new modal
    document.body.insertAdjacentHTML('beforeend', modalHtml);

    // Show modal
    const modal = new bootstrap.Modal(document.getElementById('enhancedModal'));
    modal.show();

    return modal;
}

function closeModal() {
    const modal = document.getElementById('enhancedModal');
    if (modal) {
        const bsModal = bootstrap.Modal.getInstance(modal);
        if (bsModal) {
            bsModal.hide();
        }
        setTimeout(() => modal.remove(), 300);
    }
}

function showNotification(message, type = 'info') {
    const alertClass = type === 'success' ? 'alert-success' :
                     type === 'error' ? 'alert-danger' :
                     type === 'warning' ? 'alert-warning' : 'alert-info';

    const notification = `
        <div class="alert ${alertClass} alert-dismissible fade show position-fixed"
             style="top: 20px; right: 20px; z-index: 9999; min-width: 300px;">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;

    document.body.insertAdjacentHTML('beforeend', notification);

    // Auto-remove after 5 seconds
    setTimeout(() => {
        const alerts = document.querySelectorAll('.alert');
        alerts.forEach(alert => {
            if (alert.textContent.includes(message)) {
                alert.remove();
            }
        });
    }, 5000);
}

// Additional feature functions
function showNumberingStats() {
    console.log('📊 ENHANCED: Showing numbering statistics...');
    // This would show detailed numbering statistics
    alert('إحصائيات الترقيم - قيد التطوير');
}

function showStorageMap() {
    console.log('🗺️ ENHANCED: Showing storage map...');
    // This would show an interactive storage map
    alert('خريطة التخزين التفاعلية - قيد التطوير');
}

function scanQRLocation() {
    console.log('📱 ENHANCED: Starting QR location scan...');
    // This would start QR code scanning for location tracking
    alert('مسح QR للمواقع - قيد التطوير');
}

function showUntrackedDocuments() {
    console.log('📋 ENHANCED: Showing untracked documents...');
    // This would show a list of untracked documents
    alert('عرض الوثائق غير المتتبعة - قيد التطوير');
}

function autoEnhanceMetadata() {
    console.log('🤖 ENHANCED: Auto-enhancing metadata...');
    // This would start automatic metadata enhancement
    alert('التحسين التلقائي للبيانات - قيد التطوير');
}

function validateMetadata() {
    console.log('✅ ENHANCED: Validating metadata...');
    // This would validate all metadata
    alert('التحقق من صحة البيانات - قيد التطوير');
}
</script>
{% endblock %}
