{% extends "base.html" %}

{% block title %}الملف الشخصي - نظام إدارة الأرشيف العام{% endblock %}

{% block extra_css %}
<style>
    .profile-header {
        background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
        color: white;
        border-radius: var(--border-radius);
        padding: 2rem;
        margin-bottom: 2rem;
        position: relative;
        overflow: hidden;
    }
    
    .profile-header::before {
        content: '';
        position: absolute;
        top: -50%;
        right: -50%;
        width: 200%;
        height: 200%;
        background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
        animation: float 6s ease-in-out infinite;
    }
    
    .profile-content {
        position: relative;
        z-index: 2;
        display: flex;
        align-items: center;
        gap: 2rem;
    }
    
    .profile-avatar {
        width: 120px;
        height: 120px;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.2);
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 3rem;
        font-weight: 700;
        border: 4px solid rgba(255, 255, 255, 0.3);
    }
    
    .profile-info h1 {
        margin: 0 0 0.5rem 0;
        font-size: 2rem;
    }
    
    .profile-info p {
        margin: 0;
        opacity: 0.8;
        font-size: 1.1rem;
    }
    
    .info-cards {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 2rem;
        margin-bottom: 2rem;
    }
    
    .info-card {
        background: white;
        border-radius: var(--border-radius);
        padding: 2rem;
        box-shadow: var(--shadow-light);
        transition: all 0.3s ease;
    }
    
    .info-card:hover {
        transform: translateY(-5px);
        box-shadow: var(--shadow-medium);
    }
    
    .card-title {
        font-size: 1.25rem;
        font-weight: 600;
        color: var(--primary-color);
        margin-bottom: 1.5rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }
    
    .info-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0.75rem 0;
        border-bottom: 1px solid #f8f9fa;
    }
    
    .info-row:last-child {
        border-bottom: none;
    }
    
    .info-label {
        font-weight: 600;
        color: #6c757d;
    }
    
    .info-value {
        color: var(--primary-color);
        font-weight: 500;
    }
    
    .activity-stats {
        background: white;
        border-radius: var(--border-radius);
        padding: 2rem;
        box-shadow: var(--shadow-light);
    }
    
    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 1.5rem;
        margin-top: 1.5rem;
    }
    
    .stat-item {
        text-align: center;
        padding: 1.5rem;
        background: #f8f9fa;
        border-radius: var(--border-radius);
        transition: all 0.3s ease;
    }
    
    .stat-item:hover {
        background: var(--primary-color);
        color: white;
        transform: translateY(-3px);
    }
    
    .stat-number {
        font-size: 2rem;
        font-weight: 700;
        margin-bottom: 0.5rem;
    }
    
    .stat-label {
        font-size: 0.9rem;
        opacity: 0.8;
    }
    
    .action-buttons {
        background: white;
        border-radius: var(--border-radius);
        padding: 2rem;
        box-shadow: var(--shadow-light);
        text-align: center;
    }
    
    .btn-action {
        background: linear-gradient(135deg, var(--primary-color) 0%, #2980b9 100%);
        color: white;
        border: none;
        padding: 0.75rem 2rem;
        border-radius: var(--border-radius);
        font-weight: 600;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        margin: 0.5rem;
        transition: all 0.3s ease;
    }
    
    .btn-action:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-medium);
        color: white;
    }
    
    .btn-action.secondary {
        background: #6c757d;
    }
    
    .btn-action.secondary:hover {
        background: #5a6268;
    }
    
    @media (max-width: 768px) {
        .profile-content {
            flex-direction: column;
            text-align: center;
        }
        
        .info-cards {
            grid-template-columns: 1fr;
        }
        
        .stats-grid {
            grid-template-columns: repeat(2, 1fr);
        }
    }
</style>
{% endblock %}

{% block content %}
<!-- Profile Header -->
<div class="profile-header">
    <div class="profile-content">
        <div class="profile-avatar">
            {{ user.full_name[0] if user.full_name else user.username[0] }}
        </div>
        <div class="profile-info">
            <h1>{{ user.full_name or user.username }}</h1>
            <p>
                <i class="fas fa-user-tag me-2"></i>
                {{ 'مدير النظام' if user.role == 'admin' else 'موظف' }}
                {% if user.department %}
                - {{ user.department }}
                {% endif %}
            </p>
        </div>
    </div>
</div>

<!-- Information Cards -->
<div class="info-cards">
    <!-- Personal Information -->
    <div class="info-card">
        <h3 class="card-title">
            <i class="fas fa-user"></i>
            المعلومات الشخصية
        </h3>
        
        <div class="info-row">
            <span class="info-label">الاسم الكامل:</span>
            <span class="info-value">{{ user.full_name or 'غير محدد' }}</span>
        </div>
        
        <div class="info-row">
            <span class="info-label">اسم المستخدم:</span>
            <span class="info-value">{{ user.username }}</span>
        </div>
        
        <div class="info-row">
            <span class="info-label">البريد الإلكتروني:</span>
            <span class="info-value">{{ user.email }}</span>
        </div>
        
        <div class="info-row">
            <span class="info-label">القسم:</span>
            <span class="info-value">{{ user.department or 'غير محدد' }}</span>
        </div>
        
        <div class="info-row">
            <span class="info-label">الدور:</span>
            <span class="info-value">{{ 'مدير النظام' if user.role == 'admin' else 'موظف' }}</span>
        </div>
    </div>
    
    <!-- Account Information -->
    <div class="info-card">
        <h3 class="card-title">
            <i class="fas fa-cog"></i>
            معلومات الحساب
        </h3>
        
        <div class="info-row">
            <span class="info-label">تاريخ الإنشاء:</span>
            <span class="info-value">{{ user.created_at.strftime('%Y/%m/%d') if user.created_at else 'غير محدد' }}</span>
        </div>
        
        <div class="info-row">
            <span class="info-label">آخر تسجيل دخول:</span>
            <span class="info-value">{{ user.last_login.strftime('%Y/%m/%d %H:%M') if user.last_login else 'لم يسجل دخول من قبل' }}</span>
        </div>
        
        <div class="info-row">
            <span class="info-label">حالة الحساب:</span>
            <span class="info-value">
                {% if user.is_active %}
                    <span class="text-success">نشط</span>
                {% else %}
                    <span class="text-danger">معطل</span>
                {% endif %}
            </span>
        </div>
        
        <div class="info-row">
            <span class="info-label">رقم المستخدم:</span>
            <span class="info-value">#{{ user.id }}</span>
        </div>
    </div>
</div>

<!-- Activity Statistics -->
<div class="activity-stats">
    <h3 class="card-title">
        <i class="fas fa-chart-bar"></i>
        إحصائيات النشاط
    </h3>
    
    <div class="stats-grid">
        <div class="stat-item">
            <div class="stat-number">{{ user.documents.count() if user.documents else 0 }}</div>
            <div class="stat-label">الوثائق المنشأة</div>
        </div>
        
        <div class="stat-item">
            <div class="stat-number">{{ user.received_documents.count() if user.received_documents else 0 }}</div>
            <div class="stat-label">الكتب المستلمة</div>
        </div>
        
        <div class="stat-item">
            <div class="stat-number">{{ user.outgoing_documents.count() if user.outgoing_documents else 0 }}</div>
            <div class="stat-label">الكتب المُعدة</div>
        </div>
        
        <div class="stat-item">
            <div class="stat-number">
                {{ (user.documents.count() if user.documents else 0) + 
                   (user.received_documents.count() if user.received_documents else 0) + 
                   (user.outgoing_documents.count() if user.outgoing_documents else 0) }}
            </div>
            <div class="stat-label">إجمالي النشاط</div>
        </div>
    </div>
</div>

<!-- Action Buttons -->
<div class="action-buttons">
    <h3 class="card-title">
        <i class="fas fa-tools"></i>
        الإجراءات المتاحة
    </h3>
    
    <a href="#" class="btn-action" onclick="alert('سيتم تطوير هذه الوظيفة قريباً')">
        <i class="fas fa-edit"></i>
        تعديل الملف الشخصي
    </a>
    
    <a href="#" class="btn-action secondary" onclick="alert('سيتم تطوير هذه الوظيفة قريباً')">
        <i class="fas fa-key"></i>
        تغيير كلمة المرور
    </a>
    
    <a href="{{ url_for('dashboard') }}" class="btn-action secondary">
        <i class="fas fa-home"></i>
        العودة للرئيسية
    </a>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Animation for cards
    document.addEventListener('DOMContentLoaded', function() {
        const cards = document.querySelectorAll('.info-card, .activity-stats, .action-buttons');
        
        cards.forEach((card, index) => {
            setTimeout(() => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(20px)';
                card.style.transition = 'all 0.6s ease';
                
                setTimeout(() => {
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, 100);
            }, index * 100);
        });
    });
</script>
{% endblock %}
