{% extends "base.html" %}

{% block title %}إدارة دورة حياة الوثيقة: {{ document.title }} - نظام إدارة الأرشيف العام{% endblock %}

{% block extra_css %}
<style>
    body {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        font-family: 'Cairo', sans-serif;
    }

    .lifecycle-manage-container {
        max-width: 1000px;
        margin: 2rem auto;
        padding: 0 1rem;
    }

    .document-header {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        border-radius: 20px;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: 0 20px 40px rgba(0,0,0,0.1);
    }

    .document-title {
        color: #2c3e50;
        font-size: 2rem;
        font-weight: 700;
        margin-bottom: 1rem;
        display: flex;
        align-items: center;
        gap: 1rem;
    }

    .document-meta {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
        margin-bottom: 1rem;
    }

    .meta-item {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        color: #6c757d;
    }

    .meta-item i {
        color: #667eea;
        width: 20px;
    }

    .lifecycle-status {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        border-radius: 20px;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: 0 20px 40px rgba(0,0,0,0.1);
    }

    .status-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 2rem;
    }

    .status-header h2 {
        color: #2c3e50;
        font-size: 1.5rem;
        font-weight: 600;
        margin: 0;
    }

    .current-stage {
        padding: 0.5rem 1rem;
        border-radius: 20px;
        font-weight: 600;
        font-size: 0.9rem;
    }

    .stage-creation { background: #ffecd2; color: #8b4513; }
    .stage-active { background: #d4edda; color: #155724; }
    .stage-review { background: #fff3cd; color: #856404; }
    .stage-archived { background: #d1ecf1; color: #0c5460; }
    .stage-unknown { background: #f8d7da; color: #721c24; }

    .lifecycle-info {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 1.5rem;
        margin-bottom: 2rem;
    }

    .info-card {
        background: #f8f9fa;
        border-radius: 15px;
        padding: 1.5rem;
        border-left: 4px solid #667eea;
    }

    .info-card h4 {
        color: #2c3e50;
        font-size: 1.1rem;
        font-weight: 600;
        margin-bottom: 1rem;
    }

    .info-item {
        display: flex;
        justify-content: space-between;
        margin-bottom: 0.5rem;
        font-size: 0.9rem;
    }

    .info-label {
        color: #6c757d;
        font-weight: 500;
    }

    .info-value {
        color: #2c3e50;
        font-weight: 600;
    }

    .warnings-recommendations {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        border-radius: 20px;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: 0 20px 40px rgba(0,0,0,0.1);
    }

    .alert-item {
        padding: 1rem;
        border-radius: 10px;
        margin-bottom: 1rem;
        display: flex;
        align-items: center;
        gap: 1rem;
    }

    .alert-warning {
        background: #fff3cd;
        border-left: 4px solid #ffc107;
        color: #856404;
    }

    .alert-info {
        background: #d1ecf1;
        border-left: 4px solid #17a2b8;
        color: #0c5460;
    }

    .alert-icon {
        font-size: 1.2rem;
    }

    .lifecycle-actions {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        border-radius: 20px;
        padding: 2rem;
        box-shadow: 0 20px 40px rgba(0,0,0,0.1);
    }

    .actions-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 2rem;
    }

    .action-section {
        border: 1px solid #e9ecef;
        border-radius: 15px;
        padding: 1.5rem;
        transition: all 0.3s ease;
    }

    .action-section:hover {
        border-color: #667eea;
        box-shadow: 0 5px 15px rgba(102, 126, 234, 0.1);
    }

    .action-header {
        display: flex;
        align-items: center;
        gap: 1rem;
        margin-bottom: 1rem;
    }

    .action-icon {
        width: 40px;
        height: 40px;
        border-radius: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1.2rem;
    }

    .action-archive { background: #6f42c1; }
    .action-restore { background: #28a745; }
    .action-retention { background: #17a2b8; }
    .action-extend { background: #ffc107; color: #212529; }
    .action-review { background: #fd7e14; }

    .action-title {
        color: #2c3e50;
        font-size: 1.1rem;
        font-weight: 600;
        margin: 0;
    }

    .action-description {
        color: #6c757d;
        font-size: 0.9rem;
        margin-bottom: 1rem;
    }

    .action-form {
        margin-bottom: 1rem;
    }

    .form-group {
        margin-bottom: 1rem;
    }

    .form-label {
        font-weight: 600;
        color: #2c3e50;
        margin-bottom: 0.5rem;
        display: block;
        font-size: 0.9rem;
    }

    .form-control, .form-select {
        width: 100%;
        padding: 0.5rem;
        border: 1px solid #ddd;
        border-radius: 8px;
        font-size: 0.9rem;
    }

    .form-control:focus, .form-select:focus {
        outline: none;
        border-color: #667eea;
        box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.1);
    }

    .action-btn {
        width: 100%;
        padding: 0.75rem;
        border: none;
        border-radius: 8px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.5rem;
    }

    .btn-primary {
        background: #667eea;
        color: white;
    }

    .btn-success {
        background: #28a745;
        color: white;
    }

    .btn-warning {
        background: #ffc107;
        color: #212529;
    }

    .btn-info {
        background: #17a2b8;
        color: white;
    }

    .btn-secondary {
        background: #6c757d;
        color: white;
    }

    .action-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0,0,0,0.2);
    }

    .action-btn:disabled {
        opacity: 0.6;
        cursor: not-allowed;
        transform: none;
    }

    @media (max-width: 768px) {
        .lifecycle-manage-container {
            margin: 1rem;
            padding: 0;
        }

        .document-meta {
            grid-template-columns: 1fr;
        }

        .lifecycle-info {
            grid-template-columns: 1fr;
        }

        .actions-grid {
            grid-template-columns: 1fr;
        }

        .status-header {
            flex-direction: column;
            gap: 1rem;
            text-align: center;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="lifecycle-manage-container">
    <!-- Document Header -->
    <div class="document-header">
        <div class="document-title">
            <i class="fas fa-file-alt"></i>
            {{ document.title }}
        </div>
        
        <div class="document-meta">
            <div class="meta-item">
                <i class="fas fa-hashtag"></i>
                <span>رقم الوثيقة: {{ document.id }}</span>
            </div>
            <div class="meta-item">
                <i class="fas fa-tag"></i>
                <span>النوع: {{ document.document_type }}</span>
            </div>
            <div class="meta-item">
                <i class="fas fa-calendar"></i>
                <span>تاريخ الإنشاء: {{ document.created_at.strftime('%Y-%m-%d') }}</span>
            </div>
            <div class="meta-item">
                <i class="fas fa-user"></i>
                <span>المنشئ: {{ document.creator.username if document.creator else 'غير محدد' }}</span>
            </div>
        </div>
    </div>

    <!-- Lifecycle Status -->
    <div class="lifecycle-status">
        <div class="status-header">
            <h2>
                <i class="fas fa-recycle me-2"></i>
                حالة دورة الحياة
            </h2>
            <span class="current-stage stage-{{ lifecycle_info.current_stage }}">
                {{ lifecycle_info.current_stage_name }}
            </span>
        </div>

        <div class="lifecycle-info">
            <div class="info-card">
                <h4>معلومات عامة</h4>
                <div class="info-item">
                    <span class="info-label">عمر الوثيقة:</span>
                    <span class="info-value">{{ lifecycle_info.age_text }}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">المرحلة الحالية:</span>
                    <span class="info-value">{{ lifecycle_info.current_stage_name }}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">الحالة:</span>
                    <span class="info-value">{{ document.status }}</span>
                </div>
            </div>

            <div class="info-card">
                <h4>سياسة الاحتفاظ</h4>
                <div class="info-item">
                    <span class="info-label">فترة الاحتفاظ:</span>
                    <span class="info-value">
                        {% if lifecycle_info.retention_days %}
                            {{ lifecycle_info.retention_days }} يوم
                        {% else %}
                            غير محددة
                        {% endif %}
                    </span>
                </div>
                <div class="info-item">
                    <span class="info-label">تاريخ انتهاء الاحتفاظ:</span>
                    <span class="info-value">
                        {% if lifecycle_info.retention_expires %}
                            {{ lifecycle_info.retention_expires.strftime('%Y-%m-%d') }}
                        {% else %}
                            غير محدد
                        {% endif %}
                    </span>
                </div>
                <div class="info-item">
                    <span class="info-label">تمديد إضافي:</span>
                    <span class="info-value">
                        {% if lifecycle_info.extension_days %}
                            {{ lifecycle_info.extension_days }} يوم
                        {% else %}
                            لا يوجد
                        {% endif %}
                    </span>
                </div>
            </div>

            <div class="info-card">
                <h4>الإجراءات المتاحة</h4>
                {% for action in lifecycle_info.actions_available %}
                <div class="info-item">
                    <span class="info-label">
                        {% if action == 'archive' %}أرشفة{% endif %}
                        {% if action == 'restore' %}استعادة{% endif %}
                        {% if action == 'extend' %}تمديد{% endif %}
                        {% if action == 'review' %}مراجعة{% endif %}
                    </span>
                    <span class="info-value">متاح</span>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>

    <!-- Warnings and Recommendations -->
    {% if lifecycle_info.warnings or lifecycle_info.recommendations %}
    <div class="warnings-recommendations">
        <h2>
            <i class="fas fa-exclamation-triangle me-2"></i>
            تحذيرات وتوصيات
        </h2>

        {% for warning in lifecycle_info.warnings %}
        <div class="alert-item alert-warning">
            <i class="fas fa-exclamation-triangle alert-icon"></i>
            <span>{{ warning }}</span>
        </div>
        {% endfor %}

        {% for recommendation in lifecycle_info.recommendations %}
        <div class="alert-item alert-info">
            <i class="fas fa-lightbulb alert-icon"></i>
            <span>{{ recommendation }}</span>
        </div>
        {% endfor %}
    </div>
    {% endif %}

    <!-- Lifecycle Actions -->
    <div class="lifecycle-actions">
        <h2>
            <i class="fas fa-cogs me-2"></i>
            إجراءات دورة الحياة
        </h2>

        <div class="actions-grid">
            <!-- Archive Action -->
            {% if lifecycle_info.can_archive %}
            <div class="action-section">
                <div class="action-header">
                    <div class="action-icon action-archive">
                        <i class="fas fa-archive"></i>
                    </div>
                    <h3 class="action-title">أرشفة الوثيقة</h3>
                </div>
                <p class="action-description">نقل الوثيقة إلى الأرشيف مع الاحتفاظ بإمكانية الوصول</p>
                
                <form method="POST" class="action-form">
                    <input type="hidden" name="action" value="archive">
                    <button type="submit" class="action-btn btn-primary">
                        <i class="fas fa-archive"></i>
                        أرشفة الوثيقة
                    </button>
                </form>
            </div>
            {% endif %}

            <!-- Restore Action -->
            {% if lifecycle_info.can_restore %}
            <div class="action-section">
                <div class="action-header">
                    <div class="action-icon action-restore">
                        <i class="fas fa-undo"></i>
                    </div>
                    <h3 class="action-title">استعادة الوثيقة</h3>
                </div>
                <p class="action-description">استعادة الوثيقة من الأرشيف إلى الحالة النشطة</p>
                
                <form method="POST" class="action-form">
                    <input type="hidden" name="action" value="restore">
                    <button type="submit" class="action-btn btn-success">
                        <i class="fas fa-undo"></i>
                        استعادة الوثيقة
                    </button>
                </form>
            </div>
            {% endif %}

            <!-- Set Retention Policy -->
            <div class="action-section">
                <div class="action-header">
                    <div class="action-icon action-retention">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                    <h3 class="action-title">تحديد سياسة الاحتفاظ</h3>
                </div>
                <p class="action-description">تحديد فترة الاحتفاظ بالوثيقة وسياسة الأرشفة</p>
                
                <form method="POST" class="action-form">
                    <input type="hidden" name="action" value="set_retention">
                    
                    <div class="form-group">
                        <label class="form-label">فترة الاحتفاظ (بالأيام)</label>
                        <select name="retention_period" class="form-select" required>
                            <option value="">اختر فترة الاحتفاظ</option>
                            <option value="365">سنة واحدة</option>
                            <option value="730">سنتان</option>
                            <option value="1095">ثلاث سنوات</option>
                            <option value="1825">خمس سنوات</option>
                            <option value="2555">سبع سنوات</option>
                            <option value="3650">عشر سنوات</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">سبب تحديد السياسة</label>
                        <input type="text" name="retention_reason" class="form-control" placeholder="أدخل السبب...">
                    </div>
                    
                    <button type="submit" class="action-btn btn-info">
                        <i class="fas fa-shield-alt"></i>
                        تحديد السياسة
                    </button>
                </form>
            </div>

            <!-- Extend Lifecycle -->
            {% if lifecycle_info.can_extend %}
            <div class="action-section">
                <div class="action-header">
                    <div class="action-icon action-extend">
                        <i class="fas fa-clock"></i>
                    </div>
                    <h3 class="action-title">تمديد دورة الحياة</h3>
                </div>
                <p class="action-description">تمديد فترة الاحتفاظ بالوثيقة لفترة إضافية</p>
                
                <form method="POST" class="action-form">
                    <input type="hidden" name="action" value="extend_lifecycle">
                    
                    <div class="form-group">
                        <label class="form-label">فترة التمديد (بالأيام)</label>
                        <select name="extension_period" class="form-select" required>
                            <option value="">اختر فترة التمديد</option>
                            <option value="90">3 أشهر</option>
                            <option value="180">6 أشهر</option>
                            <option value="365">سنة واحدة</option>
                            <option value="730">سنتان</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">سبب التمديد</label>
                        <input type="text" name="extension_reason" class="form-control" placeholder="أدخل السبب..." required>
                    </div>
                    
                    <button type="submit" class="action-btn btn-warning">
                        <i class="fas fa-clock"></i>
                        تمديد دورة الحياة
                    </button>
                </form>
            </div>
            {% endif %}

            <!-- Mark for Review -->
            {% if lifecycle_info.can_review %}
            <div class="action-section">
                <div class="action-header">
                    <div class="action-icon action-review">
                        <i class="fas fa-eye"></i>
                    </div>
                    <h3 class="action-title">تحديد للمراجعة</h3>
                </div>
                <p class="action-description">تحديد الوثيقة للمراجعة وتقييم حالتها</p>
                
                <form method="POST" class="action-form">
                    <input type="hidden" name="action" value="mark_for_review">
                    
                    <div class="form-group">
                        <label class="form-label">تاريخ المراجعة المطلوبة</label>
                        <input type="date" name="review_date" class="form-control">
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">سبب المراجعة</label>
                        <input type="text" name="review_reason" class="form-control" placeholder="أدخل السبب..." required>
                    </div>
                    
                    <button type="submit" class="action-btn btn-secondary">
                        <i class="fas fa-eye"></i>
                        تحديد للمراجعة
                    </button>
                </form>
            </div>
            {% endif %}
        </div>
    </div>

    <!-- Back Button -->
    <div class="text-center mt-4">
        <a href="{{ url_for('view_document', id=document.id) }}" class="action-btn btn-secondary" style="width: auto; padding: 0.75rem 2rem;">
            <i class="fas fa-arrow-right"></i>
            العودة إلى الوثيقة
        </a>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 LIFECYCLE: Document lifecycle management page loaded');
    
    // Add form validation
    const forms = document.querySelectorAll('form');
    forms.forEach(form => {
        form.addEventListener('submit', function(e) {
            const action = this.querySelector('input[name="action"]').value;
            
            // Confirm destructive actions
            if (action === 'archive') {
                if (!confirm('هل أنت متأكد من أرشفة هذه الوثيقة؟')) {
                    e.preventDefault();
                    return false;
                }
            }
            
            // Disable submit button to prevent double submission
            const submitBtn = this.querySelector('button[type="submit"]');
            if (submitBtn) {
                submitBtn.disabled = true;
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري المعالجة...';
            }
        });
    });
    
    console.log('✅ LIFECYCLE: Form validation initialized');
});
</script>
{% endblock %}
