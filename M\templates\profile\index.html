{% extends "base.html" %}

{% block title %}الملف الشخصي - {{ user.full_name }} - نظام إدارة الأرشيف العام{% endblock %}

{% block extra_css %}
<style>
    body {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        font-family: 'Cairo', sans-serif;
    }

    .profile-container {
        max-width: 1200px;
        margin: 2rem auto;
        padding: 0 1rem;
    }

    .profile-header {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        border-radius: 20px;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        text-align: center;
    }

    .profile-avatar {
        width: 120px;
        height: 120px;
        border-radius: 50%;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 1.5rem;
        font-size: 3rem;
        color: white;
        font-weight: bold;
    }

    .profile-name {
        color: #2c3e50;
        font-size: 2rem;
        font-weight: 700;
        margin-bottom: 0.5rem;
    }

    .profile-role {
        color: #6c757d;
        font-size: 1.1rem;
        margin-bottom: 1rem;
    }

    .profile-meta {
        display: flex;
        justify-content: center;
        gap: 2rem;
        flex-wrap: wrap;
        margin-bottom: 1.5rem;
    }

    .meta-item {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        color: #6c757d;
        font-size: 0.9rem;
    }

    .meta-item i {
        color: #667eea;
        width: 20px;
    }

    .profile-actions {
        display: flex;
        justify-content: center;
        gap: 1rem;
        flex-wrap: wrap;
    }

    .action-btn {
        padding: 0.75rem 1.5rem;
        border: none;
        border-radius: 10px;
        font-weight: 600;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        transition: all 0.3s ease;
        cursor: pointer;
    }

    .btn-primary {
        background: #667eea;
        color: white;
    }

    .btn-secondary {
        background: #6c757d;
        color: white;
    }

    .btn-info {
        background: #17a2b8;
        color: white;
    }

    .action-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        color: white;
        text-decoration: none;
    }

    .profile-content {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 2rem;
        margin-bottom: 2rem;
    }

    .profile-card {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        border-radius: 20px;
        padding: 2rem;
        box-shadow: 0 20px 40px rgba(0,0,0,0.1);
    }

    .card-header {
        display: flex;
        align-items: center;
        gap: 1rem;
        margin-bottom: 1.5rem;
        padding-bottom: 1rem;
        border-bottom: 2px solid #f8f9fa;
    }

    .card-icon {
        width: 50px;
        height: 50px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1.3rem;
    }

    .card-title {
        color: #2c3e50;
        font-size: 1.3rem;
        font-weight: 600;
        margin: 0;
    }

    .stats-grid {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
        margin-bottom: 1.5rem;
    }

    .stat-item {
        text-align: center;
        padding: 1rem;
        background: #f8f9fa;
        border-radius: 10px;
        transition: all 0.3s ease;
    }

    .stat-item:hover {
        background: #e9ecef;
        transform: translateY(-2px);
    }

    .stat-number {
        font-size: 1.8rem;
        font-weight: 700;
        color: #667eea;
        margin-bottom: 0.25rem;
    }

    .stat-label {
        font-size: 0.8rem;
        color: #6c757d;
        margin: 0;
    }

    .activity-list {
        max-height: 300px;
        overflow-y: auto;
    }

    .activity-item {
        display: flex;
        align-items: center;
        gap: 1rem;
        padding: 0.75rem;
        border-radius: 8px;
        margin-bottom: 0.5rem;
        transition: all 0.3s ease;
    }

    .activity-item:hover {
        background: #f8f9fa;
    }

    .activity-icon {
        width: 35px;
        height: 35px;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 0.9rem;
    }

    .activity-content {
        flex: 1;
    }

    .activity-description {
        color: #2c3e50;
        font-weight: 500;
        margin-bottom: 0.25rem;
        font-size: 0.9rem;
    }

    .activity-time {
        color: #6c757d;
        font-size: 0.8rem;
    }

    .icon-stats { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
    .icon-activities { background: linear-gradient(135deg, #28a745 0%, #20c997 100%); }
    .icon-create { background: #28a745; }
    .icon-update { background: #17a2b8; }
    .icon-delete { background: #dc3545; }
    .icon-view { background: #6c757d; }

    .info-grid {
        display: grid;
        gap: 1rem;
    }

    .info-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0.75rem;
        background: #f8f9fa;
        border-radius: 8px;
        border-left: 4px solid #667eea;
    }

    .info-label {
        font-weight: 600;
        color: #2c3e50;
        font-size: 0.9rem;
    }

    .info-value {
        color: #6c757d;
        font-size: 0.9rem;
        text-align: left;
    }

    @media (max-width: 768px) {
        .profile-container {
            margin: 1rem;
            padding: 0;
        }

        .profile-content {
            grid-template-columns: 1fr;
        }

        .profile-meta {
            flex-direction: column;
            gap: 0.5rem;
        }

        .profile-actions {
            flex-direction: column;
        }

        .stats-grid {
            grid-template-columns: 1fr;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="profile-container">
    <!-- Profile Header -->
    <div class="profile-header">
        <div class="profile-avatar">
            {{ user.full_name[0] if user.full_name else user.username[0] }}
        </div>
        
        <div class="profile-name">{{ user.full_name or user.username }}</div>
        <div class="profile-role">
            {% if user.role == 'admin' %}
                <i class="fas fa-crown me-1"></i>مدير النظام
            {% elif user.role == 'employee' %}
                <i class="fas fa-user me-1"></i>موظف
            {% else %}
                <i class="fas fa-eye me-1"></i>مشاهد
            {% endif %}
        </div>
        
        <div class="profile-meta">
            <div class="meta-item">
                <i class="fas fa-envelope"></i>
                <span>{{ user.email }}</span>
            </div>
            <div class="meta-item">
                <i class="fas fa-building"></i>
                <span>{{ user.department or 'غير محدد' }}</span>
            </div>
            <div class="meta-item">
                <i class="fas fa-calendar"></i>
                <span>انضم في {{ user.created_at.strftime('%Y-%m-%d') if user.created_at else 'غير محدد' }}</span>
            </div>
            {% if user_stats.last_login %}
            <div class="meta-item">
                <i class="fas fa-clock"></i>
                <span>آخر دخول: {{ user_stats.last_login.strftime('%Y-%m-%d %H:%M') }}</span>
            </div>
            {% endif %}
        </div>
        
        <div class="profile-actions">
            <a href="{{ url_for('edit_profile') }}" class="action-btn btn-primary">
                <i class="fas fa-edit"></i>
                تعديل الملف الشخصي
            </a>
            <a href="{{ url_for('profile_preferences') }}" class="action-btn btn-info">
                <i class="fas fa-cog"></i>
                التفضيلات
            </a>
            <a href="{{ url_for('dashboard') }}" class="action-btn btn-secondary">
                <i class="fas fa-arrow-right"></i>
                العودة للرئيسية
            </a>
        </div>
    </div>

    <!-- Profile Content -->
    <div class="profile-content">
        <!-- Statistics Card -->
        <div class="profile-card">
            <div class="card-header">
                <div class="card-icon icon-stats">
                    <i class="fas fa-chart-bar"></i>
                </div>
                <h3 class="card-title">إحصائيات النشاط</h3>
            </div>

            <div class="stats-grid">
                <div class="stat-item">
                    <div class="stat-number">{{ user_stats.documents_created or 0 }}</div>
                    <div class="stat-label">وثائق منشأة</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">{{ user_stats.incoming_processed or 0 }}</div>
                    <div class="stat-label">واردة معالجة</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">{{ user_stats.outgoing_prepared or 0 }}</div>
                    <div class="stat-label">صادرة محضرة</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">{{ user_stats.signatures_created or 0 }}</div>
                    <div class="stat-label">توقيعات رقمية</div>
                </div>
            </div>

            <div class="info-grid">
                <div class="info-item">
                    <span class="info-label">إجمالي الأنشطة:</span>
                    <span class="info-value">{{ user_stats.total_activities or 0 }}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">حالة الحساب:</span>
                    <span class="info-value">
                        {% if user.is_active %}
                            <span style="color: #28a745;">نشط</span>
                        {% else %}
                            <span style="color: #dc3545;">غير نشط</span>
                        {% endif %}
                    </span>
                </div>
            </div>
        </div>

        <!-- Recent Activities Card -->
        <div class="profile-card">
            <div class="card-header">
                <div class="card-icon icon-activities">
                    <i class="fas fa-history"></i>
                </div>
                <h3 class="card-title">الأنشطة الأخيرة</h3>
            </div>

            <div class="activity-list">
                {% if recent_activities %}
                    {% for activity in recent_activities %}
                    <div class="activity-item">
                        <div class="activity-icon icon-{{ activity.action }}">
                            {% if activity.action == 'create' %}
                                <i class="fas fa-plus"></i>
                            {% elif activity.action == 'update' %}
                                <i class="fas fa-edit"></i>
                            {% elif activity.action == 'delete' %}
                                <i class="fas fa-trash"></i>
                            {% else %}
                                <i class="fas fa-eye"></i>
                            {% endif %}
                        </div>
                        <div class="activity-content">
                            <div class="activity-description">{{ activity.description }}</div>
                            <div class="activity-time">{{ activity.timestamp.strftime('%Y-%m-%d %H:%M') if activity.timestamp else 'غير محدد' }}</div>
                        </div>
                    </div>
                    {% endfor %}
                {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-history fa-3x text-muted mb-3"></i>
                        <p class="text-muted">لا توجد أنشطة حديثة</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 PROFILE: Profile page loaded');
    
    // Add interactive features
    const statItems = document.querySelectorAll('.stat-item');
    statItems.forEach(item => {
        item.addEventListener('click', function() {
            // Add click animation
            this.style.transform = 'scale(0.95)';
            setTimeout(() => {
                this.style.transform = 'translateY(-2px)';
            }, 150);
        });
    });
    
    // Activity items hover effect
    const activityItems = document.querySelectorAll('.activity-item');
    activityItems.forEach(item => {
        item.addEventListener('mouseenter', function() {
            this.style.transform = 'translateX(5px)';
        });
        
        item.addEventListener('mouseleave', function() {
            this.style.transform = 'translateX(0)';
        });
    });
    
    console.log('✅ PROFILE: Interactive features initialized');
});
</script>
{% endblock %}
