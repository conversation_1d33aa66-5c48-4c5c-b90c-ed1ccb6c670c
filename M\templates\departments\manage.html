{% extends "base.html" %}

{% block title %}إدارة الأقسام - نظام إدارة الأرشيف العام{% endblock %}

{% block extra_css %}
<style>
    .departments-header {
        background: linear-gradient(135deg, #9b59b6 0%, #8e44ad 100%);
        color: white;
        border-radius: var(--border-radius);
        padding: 2rem;
        margin-bottom: 2rem;
        text-align: center;
    }
    
    .departments-title {
        font-size: 2rem;
        font-weight: 700;
        margin: 0;
    }
    
    .departments-subtitle {
        opacity: 0.9;
        margin: 0.5rem 0 0 0;
    }
    
    .stats-row {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
        margin-bottom: 2rem;
    }
    
    .stat-card {
        background: white;
        border-radius: var(--border-radius);
        padding: 1.5rem;
        text-align: center;
        box-shadow: var(--shadow-light);
        transition: all 0.3s ease;
    }
    
    .stat-card:hover {
        transform: translateY(-5px);
        box-shadow: var(--shadow-medium);
    }
    
    .stat-icon {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 1rem;
        font-size: 1.5rem;
        color: white;
    }
    
    .stat-icon.total {
        background: linear-gradient(135deg, #9b59b6 0%, #8e44ad 100%);
    }
    
    .stat-icon.active {
        background: linear-gradient(135deg, #2ecc71 0%, #27ae60 100%);
    }
    
    .stat-icon.employees {
        background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
    }
    
    .stat-number {
        font-size: 2rem;
        font-weight: 700;
        color: #2c3e50;
        margin-bottom: 0.5rem;
    }
    
    .stat-label {
        color: #7f8c8d;
        font-weight: 600;
    }
    
    .departments-controls {
        background: white;
        border-radius: var(--border-radius);
        padding: 1.5rem;
        margin-bottom: 2rem;
        box-shadow: var(--shadow-light);
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-wrap: wrap;
        gap: 1rem;
    }
    
    .btn-add-department {
        background: linear-gradient(135deg, #9b59b6 0%, #8e44ad 100%);
        color: white;
        border: none;
        padding: 0.75rem 1.5rem;
        border-radius: var(--border-radius);
        font-weight: 600;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        transition: all 0.3s ease;
    }
    
    .btn-add-department:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(155, 89, 182, 0.3);
        color: white;
    }
    
    .departments-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
        gap: 2rem;
    }
    
    .department-card {
        background: white;
        border-radius: var(--border-radius);
        padding: 2rem;
        box-shadow: var(--shadow-light);
        transition: all 0.3s ease;
        border-left: 4px solid #9b59b6;
    }
    
    .department-card:hover {
        transform: translateY(-5px);
        box-shadow: var(--shadow-medium);
    }
    
    .department-card.inactive {
        opacity: 0.7;
        border-left-color: #95a5a6;
    }
    
    .department-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 1rem;
    }
    
    .department-name {
        font-size: 1.25rem;
        font-weight: 700;
        color: #2c3e50;
        margin: 0;
    }
    
    .department-status {
        padding: 0.25rem 0.75rem;
        border-radius: 20px;
        font-size: 0.875rem;
        font-weight: 600;
    }
    
    .department-status.active {
        background: #d4edda;
        color: #155724;
    }
    
    .department-status.inactive {
        background: #f8d7da;
        color: #721c24;
    }
    
    .department-description {
        color: #7f8c8d;
        margin-bottom: 1.5rem;
        line-height: 1.6;
    }
    
    .department-manager {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        margin-bottom: 1rem;
        padding: 1rem;
        background: #f8f9fa;
        border-radius: var(--border-radius);
    }
    
    .manager-avatar {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background: linear-gradient(135deg, #9b59b6 0%, #8e44ad 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: 700;
    }
    
    .manager-info h6 {
        margin: 0;
        font-weight: 600;
        color: #2c3e50;
    }
    
    .manager-info small {
        color: #7f8c8d;
    }
    
    .department-stats {
        display: flex;
        justify-content: space-between;
        margin-bottom: 1.5rem;
    }
    
    .dept-stat {
        text-align: center;
    }
    
    .dept-stat-number {
        font-size: 1.5rem;
        font-weight: 700;
        color: #9b59b6;
    }
    
    .dept-stat-label {
        font-size: 0.875rem;
        color: #7f8c8d;
    }
    
    .department-actions {
        display: flex;
        gap: 0.5rem;
    }
    
    .btn-action {
        padding: 0.5rem 1rem;
        border: none;
        border-radius: 5px;
        font-size: 0.875rem;
        cursor: pointer;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 0.25rem;
        flex: 1;
        justify-content: center;
    }
    
    .btn-edit {
        background: #f39c12;
        color: white;
    }
    
    .btn-edit:hover {
        background: #e67e22;
        color: white;
    }
    
    .btn-toggle {
        background: #17a2b8;
        color: white;
    }
    
    .btn-toggle:hover {
        background: #138496;
        color: white;
    }
    
    @media (max-width: 768px) {
        .departments-controls {
            flex-direction: column;
            align-items: stretch;
        }
        
        .departments-grid {
            grid-template-columns: 1fr;
        }
        
        .stats-row {
            grid-template-columns: repeat(2, 1fr);
        }
    }
</style>
{% endblock %}

{% block content %}
<!-- Header -->
<div class="departments-header">
    <h1 class="departments-title">
        <i class="fas fa-building me-3"></i>
        إدارة الأقسام
    </h1>
    <p class="departments-subtitle">إدارة أقسام ووحدات المؤسسة</p>
</div>

<!-- Statistics -->
<div class="stats-row">
    <div class="stat-card">
        <div class="stat-icon total">
            <i class="fas fa-building"></i>
        </div>
        <div class="stat-number">{{ dept_stats.total_departments }}</div>
        <div class="stat-label">إجمالي الأقسام</div>
    </div>
    
    <div class="stat-card">
        <div class="stat-icon active">
            <i class="fas fa-check-circle"></i>
        </div>
        <div class="stat-number">{{ dept_stats.active_departments }}</div>
        <div class="stat-label">الأقسام النشطة</div>
    </div>
    
    <div class="stat-card">
        <div class="stat-icon employees">
            <i class="fas fa-users"></i>
        </div>
        <div class="stat-number">{{ dept_stats.total_employees }}</div>
        <div class="stat-label">إجمالي الموظفين</div>
    </div>
</div>

<!-- Controls -->
<div class="departments-controls">
    <div>
        <h5 class="mb-0">قائمة الأقسام</h5>
        <small class="text-muted">إدارة جميع أقسام ووحدات المؤسسة</small>
    </div>
    
    <a href="{{ url_for('add_department') }}" class="btn-add-department">
        <i class="fas fa-plus"></i>
        إضافة قسم جديد
    </a>
</div>

<!-- Departments Grid -->
<div class="departments-grid">
    {% for department in departments %}
    <div class="department-card {{ 'inactive' if not department.is_active }}">
        <div class="department-header">
            <h4 class="department-name">{{ department.name }}</h4>
            <span class="department-status {{ 'active' if department.is_active else 'inactive' }}">
                {% if department.is_active %}
                    <i class="fas fa-check-circle me-1"></i>نشط
                {% else %}
                    <i class="fas fa-times-circle me-1"></i>غير نشط
                {% endif %}
            </span>
        </div>
        
        {% if department.description %}
        <p class="department-description">{{ department.description }}</p>
        {% endif %}
        
        <!-- Manager Info -->
        <div class="department-manager">
            {% if department.manager %}
            <div class="manager-avatar">
                {{ department.manager.full_name[0] if department.manager.full_name else department.manager.username[0] }}
            </div>
            <div class="manager-info">
                <h6>{{ department.manager.full_name or department.manager.username }}</h6>
                <small>مدير القسم</small>
            </div>
            {% else %}
            <div class="manager-avatar" style="background: #95a5a6;">
                <i class="fas fa-user-slash"></i>
            </div>
            <div class="manager-info">
                <h6>لا يوجد مدير</h6>
                <small>غير محدد</small>
            </div>
            {% endif %}
        </div>
        
        <!-- Department Stats -->
        <div class="department-stats">
            <div class="dept-stat">
                <div class="dept-stat-number">{{ department.employees|length }}</div>
                <div class="dept-stat-label">الموظفين</div>
            </div>
            <div class="dept-stat">
                <div class="dept-stat-number">{{ department.created_at.strftime('%Y') if department.created_at else 'غير محدد' }}</div>
                <div class="dept-stat-label">سنة الإنشاء</div>
            </div>
        </div>
        
        <!-- Actions -->
        <div class="department-actions">
            <a href="#" class="btn-action btn-edit">
                <i class="fas fa-edit"></i>
                تعديل
            </a>
            <a href="#" class="btn-action btn-toggle"
               onclick="return confirm('هل تريد تغيير حالة هذا القسم؟')">
                <i class="fas fa-{{ 'pause' if department.is_active else 'play' }}"></i>
                {{ 'إيقاف' if department.is_active else 'تفعيل' }}
            </a>
        </div>
    </div>
    {% endfor %}
    
    {% if not departments %}
    <div class="col-12">
        <div class="text-center py-5">
            <i class="fas fa-building fa-3x text-muted mb-3"></i>
            <h4>لا توجد أقسام</h4>
            <p class="text-muted">لم يتم إنشاء أي أقسام بعد</p>
            <a href="{{ url_for('add_department') }}" class="btn-add-department">
                <i class="fas fa-plus me-2"></i>
                إضافة أول قسم
            </a>
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script>
    // تحسين تجربة المستخدم
    document.addEventListener('DOMContentLoaded', function() {
        // إضافة تأثيرات للبطاقات
        const cards = document.querySelectorAll('.department-card');
        cards.forEach((card, index) => {
            setTimeout(() => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(20px)';
                card.style.transition = 'all 0.6s ease';
                
                setTimeout(() => {
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, 50);
            }, index * 100);
        });
    });
</script>
{% endblock %}
