{% extends "base.html" %}

{% block title %}تعديل إعدادات النظام - نظام إدارة الأرشيف العام{% endblock %}

{% block extra_css %}
<style>
    .form-container {
        background: white;
        border-radius: var(--border-radius);
        padding: 2rem;
        box-shadow: var(--shadow-light);
        max-width: 900px;
        margin: 0 auto;
    }
    
    .form-header {
        background: linear-gradient(135deg, #34495e 0%, #2c3e50 100%);
        color: white;
        padding: 1.5rem;
        margin: -2rem -2rem 2rem -2rem;
        border-radius: var(--border-radius) var(--border-radius) 0 0;
    }
    
    .settings-tabs {
        display: flex;
        border-bottom: 2px solid #e9ecef;
        margin-bottom: 2rem;
        overflow-x: auto;
    }
    
    .tab-button {
        background: none;
        border: none;
        padding: 1rem 1.5rem;
        color: #6c757d;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        border-bottom: 3px solid transparent;
        white-space: nowrap;
    }
    
    .tab-button.active {
        color: var(--primary-color);
        border-bottom-color: var(--primary-color);
    }
    
    .tab-button:hover {
        color: var(--primary-color);
        background: rgba(52, 152, 219, 0.05);
    }
    
    .tab-content {
        display: none;
    }
    
    .tab-content.active {
        display: block;
    }
    
    .form-section {
        margin-bottom: 2rem;
        padding: 1.5rem;
        border: 1px solid #e9ecef;
        border-radius: var(--border-radius);
        background: #f8f9fa;
    }
    
    .section-title {
        font-size: 1.1rem;
        font-weight: 600;
        color: var(--primary-color);
        margin-bottom: 1rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }
    
    .form-group {
        margin-bottom: 1.5rem;
    }
    
    .form-label {
        font-weight: 600;
        color: var(--primary-color);
        margin-bottom: 0.5rem;
    }
    
    .form-control, .form-select {
        border: 2px solid #e9ecef;
        border-radius: var(--border-radius);
        padding: 0.75rem;
        transition: all 0.3s ease;
    }
    
    .form-control:focus, .form-select:focus {
        border-color: #34495e;
        box-shadow: 0 0 0 0.2rem rgba(52, 73, 94, 0.25);
    }
    
    .form-row {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 1rem;
    }
    
    .form-actions {
        display: flex;
        gap: 1rem;
        justify-content: flex-end;
        margin-top: 2rem;
        padding-top: 2rem;
        border-top: 1px solid #e9ecef;
    }
    
    .btn-primary {
        background: linear-gradient(135deg, #34495e 0%, #2c3e50 100%);
        border: none;
        padding: 0.75rem 2rem;
        border-radius: var(--border-radius);
        font-weight: 600;
        transition: all 0.3s ease;
    }
    
    .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-medium);
    }
    
    .btn-secondary {
        background: #6c757d;
        border: none;
        padding: 0.75rem 2rem;
        border-radius: var(--border-radius);
        font-weight: 600;
        color: white;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        transition: all 0.3s ease;
    }
    
    .btn-secondary:hover {
        background: #5a6268;
        color: white;
        transform: translateY(-2px);
    }
    
    .required {
        color: #e74c3c;
    }
    
    .form-help {
        font-size: 0.875rem;
        color: #6c757d;
        margin-top: 0.25rem;
    }
    
    .invalid-feedback {
        display: block;
        color: #e74c3c;
        font-size: 0.875rem;
        margin-top: 0.25rem;
    }
    
    .alert {
        padding: 1rem;
        border-radius: var(--border-radius);
        margin-bottom: 1rem;
    }
    
    .alert-warning {
        background: rgba(241, 196, 15, 0.1);
        border: 1px solid rgba(241, 196, 15, 0.3);
        color: #856404;
    }
    
    .alert-info {
        background: rgba(52, 152, 219, 0.1);
        border: 1px solid rgba(52, 152, 219, 0.3);
        color: #0c5460;
    }
    
    @media (max-width: 768px) {
        .form-row {
            grid-template-columns: 1fr;
        }
        
        .settings-tabs {
            flex-direction: column;
        }
        
        .tab-button {
            text-align: right;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">
        <i class="fas fa-edit me-2"></i>
        تعديل إعدادات النظام
    </h1>
    <a href="{{ url_for('settings') }}" class="btn btn-outline-secondary">
        <i class="fas fa-arrow-right me-2"></i>
        العودة للإعدادات
    </a>
</div>

<div class="form-container">
    <div class="form-header">
        <h4 class="mb-0">
            <i class="fas fa-cog me-2"></i>
            تخصيص إعدادات النظام
        </h4>
    </div>
    
    <!-- Settings Tabs -->
    <div class="settings-tabs">
        <button class="tab-button active" onclick="showTab('general')">
            <i class="fas fa-cog me-2"></i>
            الإعدادات العامة
        </button>
        <button class="tab-button" onclick="showTab('security')">
            <i class="fas fa-shield-alt me-2"></i>
            الأمان
        </button>
        <button class="tab-button" onclick="showTab('archive')">
            <i class="fas fa-archive me-2"></i>
            الأرشفة
        </button>
        <button class="tab-button" onclick="showTab('backup')">
            <i class="fas fa-save me-2"></i>
            النسخ الاحتياطي
        </button>
    </div>
    
    <form method="POST" id="settingsForm">
        {{ form.hidden_tag() }}
        
        <!-- General Settings Tab -->
        <div id="general" class="tab-content active">
            <div class="form-section">
                <h5 class="section-title">
                    <i class="fas fa-building"></i>
                    معلومات المؤسسة
                </h5>
                
                <div class="form-row">
                    <div class="form-group">
                        {{ form.system_name.label(class="form-label") }}
                        <span class="required">*</span>
                        {{ form.system_name(class="form-control") }}
                        {% if form.system_name.errors %}
                            {% for error in form.system_name.errors %}
                                <div class="invalid-feedback">{{ error }}</div>
                            {% endfor %}
                        {% endif %}
                        <div class="form-help">اسم النظام الذي سيظهر في العنوان</div>
                    </div>
                    
                    <div class="form-group">
                        {{ form.organization_name.label(class="form-label") }}
                        <span class="required">*</span>
                        {{ form.organization_name(class="form-control") }}
                        {% if form.organization_name.errors %}
                            {% for error in form.organization_name.errors %}
                                <div class="invalid-feedback">{{ error }}</div>
                            {% endfor %}
                        {% endif %}
                        <div class="form-help">اسم المؤسسة أو الجهة</div>
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        {{ form.contact_email.label(class="form-label") }}
                        {{ form.contact_email(class="form-control", type="email") }}
                        {% if form.contact_email.errors %}
                            {% for error in form.contact_email.errors %}
                                <div class="invalid-feedback">{{ error }}</div>
                            {% endfor %}
                        {% endif %}
                        <div class="form-help">البريد الإلكتروني للتواصل</div>
                    </div>
                    
                    <div class="form-group">
                        {{ form.contact_phone.label(class="form-label") }}
                        {{ form.contact_phone(class="form-control") }}
                        {% if form.contact_phone.errors %}
                            {% for error in form.contact_phone.errors %}
                                <div class="invalid-feedback">{{ error }}</div>
                            {% endfor %}
                        {% endif %}
                        <div class="form-help">رقم الهاتف للتواصل</div>
                    </div>
                </div>
                
                <div class="form-group">
                    {{ form.address.label(class="form-label") }}
                    {{ form.address(class="form-control", rows="3") }}
                    {% if form.address.errors %}
                        {% for error in form.address.errors %}
                            <div class="invalid-feedback">{{ error }}</div>
                        {% endfor %}
                    {% endif %}
                    <div class="form-help">عنوان المؤسسة الكامل</div>
                </div>
            </div>
        </div>
        
        <!-- Security Settings Tab -->
        <div id="security" class="tab-content">
            <div class="alert alert-warning">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <strong>تحذير:</strong> تغيير إعدادات الأمان قد يؤثر على وصول المستخدمين للنظام.
            </div>
            
            <div class="form-section">
                <h5 class="section-title">
                    <i class="fas fa-lock"></i>
                    إعدادات الجلسة وكلمة المرور
                </h5>
                
                <div class="form-row">
                    <div class="form-group">
                        {{ form.session_timeout.label(class="form-label") }}
                        {{ form.session_timeout(class="form-control", type="number", min="5", max="480") }}
                        {% if form.session_timeout.errors %}
                            {% for error in form.session_timeout.errors %}
                                <div class="invalid-feedback">{{ error }}</div>
                            {% endfor %}
                        {% endif %}
                        <div class="form-help">مدة انتهاء الجلسة بالدقائق (5-480)</div>
                    </div>
                    
                    <div class="form-group">
                        {{ form.password_min_length.label(class="form-label") }}
                        {{ form.password_min_length(class="form-control", type="number", min="4", max="20") }}
                        {% if form.password_min_length.errors %}
                            {% for error in form.password_min_length.errors %}
                                <div class="invalid-feedback">{{ error }}</div>
                            {% endfor %}
                        {% endif %}
                        <div class="form-help">الحد الأدنى لطول كلمة المرور (4-20)</div>
                    </div>
                </div>
                
                <div class="form-group">
                    {{ form.enable_two_factor.label(class="form-label") }}
                    {{ form.enable_two_factor(class="form-select") }}
                    {% if form.enable_two_factor.errors %}
                        {% for error in form.enable_two_factor.errors %}
                            <div class="invalid-feedback">{{ error }}</div>
                        {% endfor %}
                    {% endif %}
                    <div class="form-help">تفعيل المصادقة الثنائية للمستخدمين</div>
                </div>
            </div>
        </div>
        
        <!-- Archive Settings Tab -->
        <div id="archive" class="tab-content">
            <div class="form-section">
                <h5 class="section-title">
                    <i class="fas fa-file-archive"></i>
                    إعدادات الملفات والأرشفة
                </h5>
                
                <div class="form-row">
                    <div class="form-group">
                        {{ form.auto_archive_days.label(class="form-label") }}
                        {{ form.auto_archive_days(class="form-control", type="number", min="30", max="3650") }}
                        {% if form.auto_archive_days.errors %}
                            {% for error in form.auto_archive_days.errors %}
                                <div class="invalid-feedback">{{ error }}</div>
                            {% endfor %}
                        {% endif %}
                        <div class="form-help">عدد الأيام للأرشفة التلقائية (30-3650)</div>
                    </div>
                    
                    <div class="form-group">
                        {{ form.max_file_size.label(class="form-label") }}
                        {{ form.max_file_size(class="form-control", type="number", min="1", max="500") }}
                        {% if form.max_file_size.errors %}
                            {% for error in form.max_file_size.errors %}
                                <div class="invalid-feedback">{{ error }}</div>
                            {% endfor %}
                        {% endif %}
                        <div class="form-help">الحد الأقصى لحجم الملف بالميجابايت (1-500)</div>
                    </div>
                </div>
                
                <div class="form-group">
                    {{ form.allowed_extensions.label(class="form-label") }}
                    {{ form.allowed_extensions(class="form-control") }}
                    {% if form.allowed_extensions.errors %}
                        {% for error in form.allowed_extensions.errors %}
                            <div class="invalid-feedback">{{ error }}</div>
                        {% endfor %}
                    {% endif %}
                    <div class="form-help">امتدادات الملفات المسموحة مفصولة بفواصل (مثال: pdf,doc,jpg)</div>
                </div>
            </div>
        </div>
        
        <!-- Backup Settings Tab -->
        <div id="backup" class="tab-content">
            <div class="alert alert-info">
                <i class="fas fa-info-circle me-2"></i>
                <strong>معلومة:</strong> النسخ الاحتياطي المنتظم يضمن حماية بياناتك من الفقدان.
            </div>
            
            <div class="form-section">
                <h5 class="section-title">
                    <i class="fas fa-cloud-download-alt"></i>
                    إعدادات النسخ الاحتياطي
                </h5>
                
                <div class="form-row">
                    <div class="form-group">
                        {{ form.backup_frequency.label(class="form-label") }}
                        {{ form.backup_frequency(class="form-select") }}
                        {% if form.backup_frequency.errors %}
                            {% for error in form.backup_frequency.errors %}
                                <div class="invalid-feedback">{{ error }}</div>
                            {% endfor %}
                        {% endif %}
                        <div class="form-help">تكرار إنشاء النسخ الاحتياطية التلقائية</div>
                    </div>
                    
                    <div class="form-group">
                        {{ form.backup_retention_days.label(class="form-label") }}
                        {{ form.backup_retention_days(class="form-control", type="number", min="7", max="365") }}
                        {% if form.backup_retention_days.errors %}
                            {% for error in form.backup_retention_days.errors %}
                                <div class="invalid-feedback">{{ error }}</div>
                            {% endfor %}
                        {% endif %}
                        <div class="form-help">عدد أيام الاحتفاظ بالنسخ الاحتياطية (7-365)</div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Form Actions -->
        <div class="form-actions">
            <a href="{{ url_for('settings') }}" class="btn-secondary">
                <i class="fas fa-times me-2"></i>
                إلغاء
            </a>
            <button type="submit" class="btn btn-primary">
                <i class="fas fa-save me-2"></i>
                حفظ الإعدادات
            </button>
        </div>
    </form>
</div>
{% endblock %}

{% block extra_js %}
<script>
    function showTab(tabName) {
        // Hide all tab contents
        const tabContents = document.querySelectorAll('.tab-content');
        tabContents.forEach(content => {
            content.classList.remove('active');
        });
        
        // Remove active class from all tab buttons
        const tabButtons = document.querySelectorAll('.tab-button');
        tabButtons.forEach(button => {
            button.classList.remove('active');
        });
        
        // Show selected tab content
        document.getElementById(tabName).classList.add('active');
        
        // Add active class to clicked button
        event.target.classList.add('active');
    }
    
    // Form validation
    document.getElementById('settingsForm').addEventListener('submit', function(e) {
        const systemName = document.querySelector('input[name="system_name"]').value.trim();
        const organizationName = document.querySelector('input[name="organization_name"]').value.trim();
        
        if (!systemName) {
            e.preventDefault();
            alert('يرجى إدخال اسم النظام');
            showTab('general');
            document.querySelector('input[name="system_name"]').focus();
            return;
        }
        
        if (!organizationName) {
            e.preventDefault();
            alert('يرجى إدخال اسم المؤسسة');
            showTab('general');
            document.querySelector('input[name="organization_name"]').focus();
            return;
        }
        
        // Show loading
        showLoading();
    });
</script>
{% endblock %}
