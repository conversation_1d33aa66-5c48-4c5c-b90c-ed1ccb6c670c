{% extends "base.html" %}

{% block title %}تسجيل الدخول - نظام إدارة الأرشيف العام{% endblock %}

{% block extra_css %}
<style>
    body {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        display: flex;
        align-items: center;
        justify-content: center;
    }
    
    .login-container {
        max-width: 450px;
        width: 100%;
        margin: 2rem;
    }
    
    .login-card {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border-radius: 20px;
        box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        overflow: hidden;
        animation: slideUp 0.6s ease-out;
    }
    
    @keyframes slideUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
    
    .login-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        text-align: center;
        padding: 3rem 2rem 2rem;
        position: relative;
    }
    
    .login-header::before {
        content: '';
        position: absolute;
        bottom: -10px;
        left: 50%;
        transform: translateX(-50%);
        width: 0;
        height: 0;
        border-left: 20px solid transparent;
        border-right: 20px solid transparent;
        border-top: 20px solid rgba(255,255,255,0.1);
    }
    
    .login-logo {
        font-size: 4rem;
        margin-bottom: 1rem;
        animation: pulse 2s infinite;
    }
    
    @keyframes pulse {
        0% { transform: scale(1); }
        50% { transform: scale(1.05); }
        100% { transform: scale(1); }
    }
    
    .login-title {
        font-size: 1.8rem;
        font-weight: 700;
        margin-bottom: 0.5rem;
    }
    
    .login-subtitle {
        font-size: 1rem;
        opacity: 0.9;
        margin-bottom: 0;
    }
    
    .login-body {
        padding: 3rem 2rem;
    }
    
    .form-floating {
        margin-bottom: 1.5rem;
    }
    
    .form-floating .form-control {
        border: 2px solid #e9ecef;
        border-radius: 15px;
        height: 60px;
        font-size: 1rem;
        transition: all 0.3s ease;
    }
    
    .form-floating .form-control:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        transform: translateY(-2px);
    }
    
    .form-floating label {
        color: #6c757d;
        font-weight: 500;
    }
    
    .login-btn {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        border-radius: 15px;
        padding: 1rem 2rem;
        font-size: 1.1rem;
        font-weight: 600;
        width: 100%;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }
    
    .login-btn::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
        transition: left 0.5s;
    }
    
    .login-btn:hover::before {
        left: 100%;
    }
    
    .login-btn:hover {
        transform: translateY(-3px);
        box-shadow: 0 10px 25px rgba(102, 126, 234, 0.4);
    }
    
    .login-footer {
        text-align: center;
        padding: 1rem 2rem 2rem;
        color: #6c757d;
        font-size: 0.9rem;
    }
    
    .features-list {
        list-style: none;
        padding: 0;
        margin: 2rem 0 0;
    }
    
    .features-list li {
        padding: 0.5rem 0;
        color: #6c757d;
        font-size: 0.9rem;
    }
    
    .features-list li i {
        color: #667eea;
        margin-left: 0.5rem;
        width: 20px;
    }

    .forgot-password-link {
        color: #667eea;
        text-decoration: none;
        font-size: 0.9rem;
        transition: all 0.3s ease;
    }

    .forgot-password-link:hover {
        color: #764ba2;
        text-decoration: underline;
    }

    .language-selector {
        text-align: center;
        margin-bottom: 1rem;
    }

    .language-title {
        font-size: 0.9rem;
        color: #6c757d;
        margin-bottom: 0.5rem;
    }

    .language-options {
        display: flex;
        justify-content: center;
        gap: 1rem;
    }

    .language-option {
        padding: 0.5rem 1rem;
        border: 2px solid #e9ecef;
        border-radius: 20px;
        text-decoration: none;
        color: #6c757d;
        font-size: 0.85rem;
        transition: all 0.3s ease;
        background: white;
    }

    .language-option:hover {
        border-color: #667eea;
        color: #667eea;
        transform: translateY(-2px);
    }

    .language-option.active {
        border-color: #667eea;
        background: #667eea;
        color: white;
    }
    
    .demo-credentials {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        color: white;
        padding: 1rem;
        border-radius: 15px;
        margin-bottom: 1.5rem;
        text-align: center;
        animation: glow 2s infinite alternate;
    }
    
    @keyframes glow {
        from { box-shadow: 0 0 10px rgba(240, 147, 251, 0.5); }
        to { box-shadow: 0 0 20px rgba(240, 147, 251, 0.8); }
    }
    
    .demo-credentials h6 {
        margin-bottom: 0.5rem;
        font-weight: 600;
    }
    
    .demo-credentials small {
        opacity: 0.9;
    }
    
    @media (max-width: 576px) {
        .login-container {
            margin: 1rem;
        }
        
        .login-header {
            padding: 2rem 1.5rem 1.5rem;
        }
        
        .login-body {
            padding: 2rem 1.5rem;
        }
        
        .login-logo {
            font-size: 3rem;
        }
        
        .login-title {
            font-size: 1.5rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="login-container">
    <div class="login-card">
        <div class="login-header">
            <div class="login-logo">🏛️</div>
            <h1 class="login-title">نظام إدارة الأرشيف العام</h1>
            <p class="login-subtitle">Archive Management System</p>
        </div>
        
        <div class="login-body">
            <!-- Demo Credentials -->
            <div class="demo-credentials">
                <h6><i class="fas fa-key me-2"></i>بيانات تجريبية</h6>
                <small>
                    <strong>المستخدم:</strong> admin<br>
                    <strong>كلمة المرور:</strong> admin123
                </small>
            </div>
            
            <form method="POST" id="loginForm">
                {{ form.hidden_tag() }}
                
                <div class="form-floating">
                    {{ form.username(class="form-control", placeholder="اسم المستخدم", id="username") }}
                    {{ form.username.label(class="form-label") }}
                    {% if form.username.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.username.errors %}
                                <small>{{ error }}</small>
                            {% endfor %}
                        </div>
                    {% endif %}
                </div>
                
                <div class="form-floating">
                    {{ form.password(class="form-control", placeholder="كلمة المرور", id="password") }}
                    {{ form.password.label(class="form-label") }}
                    {% if form.password.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.password.errors %}
                                <small>{{ error }}</small>
                            {% endfor %}
                        </div>
                    {% endif %}
                </div>
                
                <button type="submit" class="btn login-btn">
                    <i class="fas fa-sign-in-alt me-2"></i>
                    تسجيل الدخول
                </button>
            </form>

            <!-- Forgot Password Link -->
            <div class="text-center mt-3">
                <a href="{{ url_for('forgot_password') }}" class="forgot-password-link">
                    <i class="fas fa-key me-2"></i>
                    نسيت كلمة المرور؟
                </a>
            </div>
            
            <ul class="features-list">
                <li><i class="fas fa-check"></i>إدارة شاملة للوثائق والأرشيف</li>
                <li><i class="fas fa-check"></i>نظام الكتب الواردة والصادرة</li>
                <li><i class="fas fa-check"></i>تقارير تفاعلية ومتقدمة</li>
                <li><i class="fas fa-check"></i>أمان عالي وحماية البيانات</li>
                <li><i class="fas fa-check"></i>واجهة عربية حديثة ومتجاوبة</li>
            </ul>
        </div>
        
        <div class="login-footer">
            <!-- Language Selector -->
            <div class="language-selector mb-3">
                <div class="language-title">اللغة / Language</div>
                <div class="language-options">
                    <a href="{{ url_for('set_language', language='ar') }}"
                       class="language-option {{ 'active' if session.get('language', 'ar') == 'ar' else '' }}">
                        🇸🇦 العربية
                    </a>
                    <a href="{{ url_for('set_language', language='en') }}"
                       class="language-option {{ 'active' if session.get('language', 'ar') == 'en' else '' }}">
                        🇺🇸 English
                    </a>
                </div>
            </div>

            <p class="mb-0">
                <i class="fas fa-shield-alt me-1"></i>
                نظام آمن ومحمي
                <span class="mx-2">•</span>
                <i class="fas fa-mobile-alt me-1"></i>
                متوافق مع جميع الأجهزة
            </p>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Auto-fill demo credentials
    document.addEventListener('DOMContentLoaded', function() {
        const usernameField = document.getElementById('username');
        const passwordField = document.getElementById('password');
        
        // Pre-fill with demo credentials
        usernameField.value = 'admin';
        passwordField.value = 'admin123';
        
        // Add form submission handling
        document.getElementById('loginForm').addEventListener('submit', function(e) {
            showLoading();
        });
        
        // Add enter key support
        [usernameField, passwordField].forEach(field => {
            field.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    document.getElementById('loginForm').submit();
                }
            });
        });
        
        // Add focus effects
        const formControls = document.querySelectorAll('.form-control');
        formControls.forEach(control => {
            control.addEventListener('focus', function() {
                this.parentElement.style.transform = 'translateY(-2px)';
            });
            
            control.addEventListener('blur', function() {
                this.parentElement.style.transform = 'translateY(0)';
            });
        });
    });
    
    // Hide loading when page loads
    window.addEventListener('load', function() {
        hideLoading();
    });
</script>
{% endblock %}
