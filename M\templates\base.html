<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}نظام إدارة الأرشيف العام{% endblock %}</title>
    
    <!-- Bootstrap 5 RTL -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.rtl.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- FullCalendar CSS -->
    <link href="https://cdn.jsdelivr.net/npm/fullcalendar@6.1.8/index.global.min.css" rel="stylesheet">
    
    <!-- Google Fonts - Arabic -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <!-- Custom CSS -->
    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #3498db;
            --success-color: #27ae60;
            --warning-color: #f39c12;
            --danger-color: #e74c3c;
            --light-color: #ecf0f1;
            --dark-color: #2c3e50;
            --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --gradient-secondary: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            --shadow-light: 0 2px 10px rgba(0,0,0,0.1);
            --shadow-medium: 0 4px 20px rgba(0,0,0,0.15);
            --border-radius: 12px;
        }
        
        * {
            font-family: 'Cairo', sans-serif;
        }
        
        body {
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
        }
        
        .navbar {
            background: var(--gradient-primary) !important;
            box-shadow: var(--shadow-medium);
            padding: 1rem 0;
        }
        
        .navbar-brand {
            font-weight: 700;
            font-size: 1.5rem;
            color: white !important;
        }
        
        .navbar-nav .nav-link {
            color: rgba(255,255,255,0.9) !important;
            font-weight: 500;
            margin: 0 0.5rem;
            padding: 0.5rem 1rem !important;
            border-radius: var(--border-radius);
            transition: all 0.3s ease;
        }
        
        .navbar-nav .nav-link:hover {
            background: rgba(255,255,255,0.2);
            color: white !important;
            transform: translateY(-2px);
        }
        
        .card {
            border: none;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-light);
            transition: all 0.3s ease;
            overflow: hidden;
        }
        
        .card:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow-medium);
        }
        
        .card-header {
            background: var(--gradient-primary);
            color: white;
            font-weight: 600;
            border: none;
            padding: 1.25rem;
        }
        
        .btn {
            border-radius: var(--border-radius);
            font-weight: 500;
            padding: 0.75rem 1.5rem;
            transition: all 0.3s ease;
            border: none;
        }
        
        .btn-primary {
            background: var(--gradient-primary);
            box-shadow: var(--shadow-light);
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-medium);
        }
        
        .btn-success {
            background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
        }
        
        .btn-warning {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        }
        
        .btn-danger {
            background: linear-gradient(135deg, #fc466b 0%, #3f5efb 100%);
        }
        
        .form-control, .form-select {
            border-radius: var(--border-radius);
            border: 2px solid #e9ecef;
            padding: 0.75rem;
            transition: all 0.3s ease;
        }
        
        .form-control:focus, .form-select:focus {
            border-color: var(--secondary-color);
            box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
        }
        
        .alert {
            border-radius: var(--border-radius);
            border: none;
            font-weight: 500;
        }
        
        .stats-card {
            background: white;
            border-radius: var(--border-radius);
            padding: 2rem;
            text-align: center;
            box-shadow: var(--shadow-light);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        
        .stats-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--gradient-primary);
        }
        
        .stats-card:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow-medium);
        }
        
        .stats-number {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--primary-color);
            margin-bottom: 0.5rem;
        }
        
        .stats-label {
            color: #6c757d;
            font-weight: 500;
        }
        
        .sidebar {
            background: white;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-light);
            padding: 0;
            overflow: hidden;
        }
        
        .sidebar .nav-link {
            color: var(--dark-color);
            padding: 1rem 1.5rem;
            border-bottom: 1px solid #f8f9fa;
            transition: all 0.3s ease;
        }
        
        .sidebar .nav-link:hover {
            background: var(--light-color);
            color: var(--secondary-color);
            padding-right: 2rem;
        }
        
        .sidebar .nav-link.active {
            background: var(--gradient-primary);
            color: white;
        }
        
        .table {
            border-radius: var(--border-radius);
            overflow: hidden;
            box-shadow: var(--shadow-light);
        }
        
        .table thead th {
            background: var(--gradient-primary);
            color: white;
            border: none;
            font-weight: 600;
        }
        
        .table tbody tr:hover {
            background: rgba(52, 152, 219, 0.1);
        }
        
        .footer {
            background: var(--primary-color);
            color: white;
            text-align: center;
            padding: 2rem 0;
            margin-top: 3rem;
        }
        
        .loading {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            z-index: 9999;
        }
        
        .loading-spinner {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            font-size: 2rem;
        }
        
        @media (max-width: 768px) {
            .stats-card {
                margin-bottom: 1rem;
            }
            
            .navbar-brand {
                font-size: 1.2rem;
            }
        }
    </style>
    
    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- Loading Overlay -->
    <div class="loading" id="loadingOverlay">
        <div class="loading-spinner">
            <i class="fas fa-spinner fa-spin"></i>
            <div class="mt-3">جاري التحميل...</div>
        </div>
    </div>

    {% if current_user.is_authenticated %}
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg">
        <div class="container">
            <a class="navbar-brand" href="{{ url_for('dashboard') }}">
                <i class="fas fa-archive me-2"></i>
                نظام إدارة الأرشيف العام
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('dashboard') }}">
                            <i class="fas fa-tachometer-alt me-1"></i>
                            الرئيسية
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('documents') }}">
                            <i class="fas fa-folder me-1"></i>
                            الوثائق
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('incoming_documents') }}">
                            <i class="fas fa-inbox me-1"></i>
                            الواردة
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('outgoing_documents') }}">
                            <i class="fas fa-paper-plane me-1"></i>
                            الصادرة
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('search') }}">
                            <i class="fas fa-search me-1"></i>
                            البحث
                        </a>
                    </li>

                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('calendar') }}">
                            <i class="fas fa-calendar-alt me-1"></i>
                            التقويم
                        </a>
                    </li>

                    <!-- قائمة الميزات المتقدمة -->
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="advancedDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-magic me-1"></i>
                            الميزات المتقدمة
                        </a>
                        <ul class="dropdown-menu">
                            <li><h6 class="dropdown-header">
                                <i class="fas fa-eye me-2"></i>
                                استخراج النص (OCR)
                            </h6></li>
                            <li><a class="dropdown-item" href="{{ url_for('ocr_scanner') }}">
                                <i class="fas fa-file-image me-2"></i>
                                ماسح OCR المتقدم
                            </a></li>
                            <li><hr class="dropdown-divider"></li>

                            <li><h6 class="dropdown-header">
                                <i class="fas fa-signature me-2"></i>
                                التوقيع الرقمي
                            </h6></li>
                            <li><a class="dropdown-item" href="{{ url_for('digital_signatures_manager') }}">
                                <i class="fas fa-certificate me-2"></i>
                                إدارة التوقيعات
                            </a></li>
                            <li><hr class="dropdown-divider"></li>

                            <li><h6 class="dropdown-header">
                                <i class="fas fa-qrcode me-2"></i>
                                الرموز والملصقات
                            </h6></li>
                            <li><a class="dropdown-item" href="{{ url_for('documents') }}">
                                <i class="fas fa-barcode me-2"></i>
                                توليد الباركود والQR
                            </a></li>
                            <li><hr class="dropdown-divider"></li>

                            <li><h6 class="dropdown-header">
                                <i class="fas fa-recycle me-2"></i>
                                إدارة دورة الحياة
                            </h6></li>
                            <li><a class="dropdown-item" href="{{ url_for('document_lifecycle') }}">
                                <i class="fas fa-recycle me-2"></i>
                                دورة حياة الوثائق
                            </a></li>
                            <li><hr class="dropdown-divider"></li>

                            <li><h6 class="dropdown-header">
                                <i class="fas fa-cogs me-2"></i>
                                الميزات المحسنة
                            </h6></li>
                            <li><a class="dropdown-item" href="{{ url_for('enhanced_document_management') }}">
                                <i class="fas fa-cogs me-2"></i>
                                إدارة الوثائق المحسنة
                            </a></li>
                        </ul>
                    </li>
                    {% if current_user.is_admin() %}
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="reportsDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-chart-bar me-1"></i>
                            التقارير
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{{ url_for('reports') }}">
                                <i class="fas fa-chart-bar me-2"></i>
                                لوحة التقارير
                            </a></li>
                            <li><a class="dropdown-item" href="{{ url_for('generate_report') }}">
                                <i class="fas fa-file-export me-2"></i>
                                توليد تقرير
                            </a></li>
                            <li><a class="dropdown-item" href="{{ url_for('statistics') }}">
                                <i class="fas fa-chart-line me-2"></i>
                                الإحصائيات المتقدمة
                            </a></li>
                        </ul>
                    </li>

                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="settingsDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-cog me-1"></i>
                            الإعدادات
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{{ url_for('settings') }}">
                                <i class="fas fa-cog me-2"></i>
                                إعدادات النظام
                            </a></li>
                            <li><a class="dropdown-item" href="{{ url_for('edit_settings') }}">
                                <i class="fas fa-edit me-2"></i>
                                تعديل الإعدادات
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{{ url_for('create_backup') }}"
                                   onclick="return confirm('هل أنت متأكد من إنشاء نسخة احتياطية؟')">
                                <i class="fas fa-download me-2"></i>
                                نسخة احتياطية
                            </a></li>
                        </ul>
                    </li>

                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('activity_log') }}">
                            <i class="fas fa-history me-1"></i>
                            سجل الأنشطة
                        </a>
                    </li>

                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('trash') }}">
                            <i class="fas fa-trash-alt me-1"></i>
                            سلة المهملات
                        </a>
                    </li>

                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('manage_users') }}">
                            <i class="fas fa-users me-1"></i>
                            إدارة المستخدمين
                        </a>
                    </li>

                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('manage_departments') }}">
                            <i class="fas fa-building me-1"></i>
                            إدارة الأقسام
                        </a>
                    </li>

                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('maintenance') }}">
                            <i class="fas fa-tools me-1"></i>
                            صيانة النظام
                        </a>
                    </li>
                    {% endif %}
                </ul>

                <!-- Notifications -->
                <ul class="navbar-nav me-3">
                    <li class="nav-item">
                        <a class="nav-link position-relative" href="{{ url_for('notifications') }}" id="notificationsLink">
                            <i class="fas fa-bell"></i>
                            <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger"
                                  id="notificationsBadge" style="display: none;">
                                0
                            </span>
                        </a>
                    </li>
                </ul>

                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user me-1"></i>
                            {{ current_user.full_name }}
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#" onclick="showComingSoon()">
                                <i class="fas fa-user-cog me-2"></i>الملف الشخصي
                            </a></li>
                            <li><a class="dropdown-item" href="#" onclick="showComingSoon()">
                                <i class="fas fa-cog me-2"></i>الإعدادات
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{{ url_for('logout') }}">
                                <i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج
                            </a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>
    {% endif %}

    <!-- Flash Messages -->
    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            <div class="container mt-3">
                {% for category, message in messages %}
                    <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                        <i class="fas fa-{{ 'exclamation-triangle' if category == 'error' else 'check-circle' if category == 'success' else 'info-circle' }} me-2"></i>
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            </div>
        {% endif %}
    {% endwith %}

    <!-- Main Content -->
    <main class="{% if current_user.is_authenticated %}container mt-4{% endif %}">
        {% block content %}{% endblock %}
    </main>

    {% if current_user.is_authenticated %}
    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <p class="mb-0">
                &copy; 2024 نظام إدارة الأرشيف العام. جميع الحقوق محفوظة.
                <span class="mx-3">|</span>
                <i class="fas fa-code me-1"></i>
                تم التطوير بواسطة فريق الأنظمة
            </p>
        </div>
    </footer>
    {% endif %}

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <!-- FullCalendar JS -->
    <script src="https://cdn.jsdelivr.net/npm/fullcalendar@6.1.8/index.global.min.js"></script>
    
    <!-- Custom JavaScript -->
    <script>
        // Show loading overlay
        function showLoading() {
            document.getElementById('loadingOverlay').style.display = 'block';
        }
        
        // Hide loading overlay
        function hideLoading() {
            document.getElementById('loadingOverlay').style.display = 'none';
        }
        
        // Show coming soon message
        function showComingSoon() {
            alert('هذه الميزة قيد التطوير وستكون متاحة قريباً');
        }
        
        // Auto-hide alerts after 5 seconds
        setTimeout(function() {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(function(alert) {
                const bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            });
        }, 5000);
        
        // Add smooth scrolling
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth'
                    });
                }
            });
        });

        // Update notifications count
        function updateNotificationsCount() {
            fetch('/api/notifications/unread-count')
                .then(response => response.json())
                .then(data => {
                    const badge = document.getElementById('notificationsBadge');
                    if (data.count > 0) {
                        badge.textContent = data.count;
                        badge.style.display = 'inline-block';
                    } else {
                        badge.style.display = 'none';
                    }
                })
                .catch(error => {
                    console.error('خطأ في تحديث عدد الإشعارات:', error);
                });
        }

        // Update notifications count on page load and every 30 seconds
        {% if current_user.is_authenticated %}
        updateNotificationsCount();
        setInterval(updateNotificationsCount, 30000);
        {% endif %}
    </script>
    
    {% block extra_js %}{% endblock %}
</body>
</html>
