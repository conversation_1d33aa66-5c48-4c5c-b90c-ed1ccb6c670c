{% extends "base.html" %}

{% block title %}توليد التقارير - نظام إدارة الأرشيف العام{% endblock %}

{% block extra_css %}
<style>
    .form-container {
        background: white;
        border-radius: var(--border-radius);
        padding: 2rem;
        box-shadow: var(--shadow-light);
        max-width: 800px;
        margin: 0 auto;
    }
    
    .form-header {
        background: linear-gradient(135deg, #8e44ad 0%, #9b59b6 100%);
        color: white;
        padding: 1.5rem;
        margin: -2rem -2rem 2rem -2rem;
        border-radius: var(--border-radius) var(--border-radius) 0 0;
    }
    
    .form-group {
        margin-bottom: 1.5rem;
    }
    
    .form-label {
        font-weight: 600;
        color: var(--primary-color);
        margin-bottom: 0.5rem;
    }
    
    .form-control, .form-select {
        border: 2px solid #e9ecef;
        border-radius: var(--border-radius);
        padding: 0.75rem;
        transition: all 0.3s ease;
    }
    
    .form-control:focus, .form-select:focus {
        border-color: #8e44ad;
        box-shadow: 0 0 0 0.2rem rgba(142, 68, 173, 0.25);
    }
    
    .form-row {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 1rem;
    }
    
    .form-actions {
        display: flex;
        gap: 1rem;
        justify-content: flex-end;
        margin-top: 2rem;
        padding-top: 2rem;
        border-top: 1px solid #e9ecef;
    }
    
    .btn-primary {
        background: linear-gradient(135deg, #8e44ad 0%, #9b59b6 100%);
        border: none;
        padding: 0.75rem 2rem;
        border-radius: var(--border-radius);
        font-weight: 600;
        transition: all 0.3s ease;
    }
    
    .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-medium);
    }
    
    .btn-secondary {
        background: #6c757d;
        border: none;
        padding: 0.75rem 2rem;
        border-radius: var(--border-radius);
        font-weight: 600;
        color: white;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        transition: all 0.3s ease;
    }
    
    .btn-secondary:hover {
        background: #5a6268;
        color: white;
        transform: translateY(-2px);
    }
    
    .required {
        color: #e74c3c;
    }
    
    .form-help {
        font-size: 0.875rem;
        color: #6c757d;
        margin-top: 0.25rem;
    }
    
    .invalid-feedback {
        display: block;
        color: #e74c3c;
        font-size: 0.875rem;
        margin-top: 0.25rem;
    }
    
    .report-preview {
        background: #f8f9fa;
        border-radius: var(--border-radius);
        padding: 1.5rem;
        margin-top: 1rem;
        border: 1px solid #dee2e6;
    }
    
    .preview-title {
        font-weight: 600;
        color: var(--primary-color);
        margin-bottom: 1rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }
    
    .preview-item {
        display: flex;
        justify-content: space-between;
        padding: 0.5rem 0;
        border-bottom: 1px solid #e9ecef;
    }
    
    .preview-item:last-child {
        border-bottom: none;
    }
    
    .preview-label {
        font-weight: 600;
        color: #495057;
    }
    
    .preview-value {
        color: #6c757d;
    }
    
    @media (max-width: 768px) {
        .form-row {
            grid-template-columns: 1fr;
        }
        
        .form-actions {
            flex-direction: column;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">
        <i class="fas fa-file-export me-2"></i>
        توليد التقارير
    </h1>
    <a href="{{ url_for('reports') }}" class="btn btn-outline-secondary">
        <i class="fas fa-arrow-right me-2"></i>
        العودة للتقارير
    </a>
</div>

<div class="form-container">
    <div class="form-header">
        <h4 class="mb-0">
            <i class="fas fa-chart-bar me-2"></i>
            إعدادات التقرير
        </h4>
    </div>
    
    <form method="POST" id="reportForm">
        {{ form.hidden_tag() }}
        
        <!-- نوع التقرير -->
        <div class="form-group">
            {{ form.report_type.label(class="form-label") }}
            <span class="required">*</span>
            {{ form.report_type(class="form-select", id="reportType") }}
            {% if form.report_type.errors %}
                {% for error in form.report_type.errors %}
                    <div class="invalid-feedback">{{ error }}</div>
                {% endfor %}
            {% endif %}
            <div class="form-help">اختر نوع التقرير المطلوب توليده</div>
        </div>
        
        <!-- فترة التقرير -->
        <div class="form-row">
            <div class="form-group">
                {{ form.date_from.label(class="form-label") }}
                {{ form.date_from(class="form-control", type="date") }}
                {% if form.date_from.errors %}
                    {% for error in form.date_from.errors %}
                        <div class="invalid-feedback">{{ error }}</div>
                    {% endfor %}
                {% endif %}
                <div class="form-help">تاريخ بداية التقرير (اختياري)</div>
            </div>
            
            <div class="form-group">
                {{ form.date_to.label(class="form-label") }}
                {{ form.date_to(class="form-control", type="date") }}
                {% if form.date_to.errors %}
                    {% for error in form.date_to.errors %}
                        <div class="invalid-feedback">{{ error }}</div>
                    {% endfor %}
                {% endif %}
                <div class="form-help">تاريخ نهاية التقرير (اختياري)</div>
            </div>
        </div>
        
        <!-- فلاتر التقرير -->
        <div class="form-row">
            <div class="form-group">
                {{ form.status_filter.label(class="form-label") }}
                {{ form.status_filter(class="form-select") }}
                {% if form.status_filter.errors %}
                    {% for error in form.status_filter.errors %}
                        <div class="invalid-feedback">{{ error }}</div>
                    {% endfor %}
                {% endif %}
                <div class="form-help">فلترة البيانات حسب الحالة</div>
            </div>
            
            <div class="form-group">
                {{ form.priority_filter.label(class="form-label") }}
                {{ form.priority_filter(class="form-select") }}
                {% if form.priority_filter.errors %}
                    {% for error in form.priority_filter.errors %}
                        <div class="invalid-feedback">{{ error }}</div>
                    {% endfor %}
                {% endif %}
                <div class="form-help">فلترة البيانات حسب الأولوية</div>
            </div>
        </div>
        
        <!-- فلتر المستخدم -->
        <div class="form-group">
            {{ form.user_filter.label(class="form-label") }}
            {{ form.user_filter(class="form-select") }}
            {% if form.user_filter.errors %}
                {% for error in form.user_filter.errors %}
                    <div class="invalid-feedback">{{ error }}</div>
                {% endfor %}
            {% endif %}
            <div class="form-help">فلترة البيانات حسب المستخدم المنشئ</div>
        </div>
        
        <!-- تنسيق التصدير -->
        <div class="form-group">
            {{ form.export_format.label(class="form-label") }}
            <span class="required">*</span>
            {{ form.export_format(class="form-select") }}
            {% if form.export_format.errors %}
                {% for error in form.export_format.errors %}
                    <div class="invalid-feedback">{{ error }}</div>
                {% endfor %}
            {% endif %}
            <div class="form-help">اختر تنسيق عرض أو تصدير التقرير</div>
        </div>
        
        <!-- معاينة التقرير -->
        <div class="report-preview" id="reportPreview" style="display: none;">
            <div class="preview-title">
                <i class="fas fa-eye"></i>
                معاينة إعدادات التقرير
            </div>
            <div id="previewContent"></div>
        </div>
        
        <!-- أزرار الإجراءات -->
        <div class="form-actions">
            <a href="{{ url_for('reports') }}" class="btn-secondary">
                <i class="fas fa-times me-2"></i>
                إلغاء
            </a>
            <button type="submit" class="btn btn-primary">
                <i class="fas fa-file-export me-2"></i>
                توليد التقرير
            </button>
        </div>
    </form>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const form = document.getElementById('reportForm');
        const reportType = document.getElementById('reportType');
        const preview = document.getElementById('reportPreview');
        const previewContent = document.getElementById('previewContent');
        
        // Update preview when form changes
        form.addEventListener('change', updatePreview);
        form.addEventListener('input', updatePreview);
        
        function updatePreview() {
            const formData = new FormData(form);
            let previewHtml = '';
            
            // Report type
            const reportTypeValue = formData.get('report_type');
            const reportTypeText = reportType.options[reportType.selectedIndex].text;
            previewHtml += `<div class="preview-item">
                <span class="preview-label">نوع التقرير:</span>
                <span class="preview-value">${reportTypeText}</span>
            </div>`;
            
            // Date range
            const dateFrom = formData.get('date_from');
            const dateTo = formData.get('date_to');
            if (dateFrom || dateTo) {
                const dateRange = `${dateFrom || 'غير محدد'} إلى ${dateTo || 'غير محدد'}`;
                previewHtml += `<div class="preview-item">
                    <span class="preview-label">الفترة الزمنية:</span>
                    <span class="preview-value">${dateRange}</span>
                </div>`;
            }
            
            // Filters
            const statusFilter = formData.get('status_filter');
            if (statusFilter) {
                previewHtml += `<div class="preview-item">
                    <span class="preview-label">فلتر الحالة:</span>
                    <span class="preview-value">${statusFilter}</span>
                </div>`;
            }
            
            const priorityFilter = formData.get('priority_filter');
            if (priorityFilter) {
                previewHtml += `<div class="preview-item">
                    <span class="preview-label">فلتر الأولوية:</span>
                    <span class="preview-value">${priorityFilter}</span>
                </div>`;
            }
            
            const userFilter = formData.get('user_filter');
            if (userFilter) {
                const userSelect = document.querySelector('select[name="user_filter"]');
                const userText = userSelect.options[userSelect.selectedIndex].text;
                previewHtml += `<div class="preview-item">
                    <span class="preview-label">فلتر المستخدم:</span>
                    <span class="preview-value">${userText}</span>
                </div>`;
            }
            
            // Export format
            const exportFormat = formData.get('export_format');
            const exportSelect = document.querySelector('select[name="export_format"]');
            const exportText = exportSelect.options[exportSelect.selectedIndex].text;
            previewHtml += `<div class="preview-item">
                <span class="preview-label">تنسيق التصدير:</span>
                <span class="preview-value">${exportText}</span>
            </div>`;
            
            previewContent.innerHTML = previewHtml;
            preview.style.display = 'block';
        }
        
        // Form validation
        form.addEventListener('submit', function(e) {
            const reportType = document.querySelector('select[name="report_type"]').value;
            const exportFormat = document.querySelector('select[name="export_format"]').value;
            
            if (!reportType) {
                e.preventDefault();
                alert('يرجى اختيار نوع التقرير');
                return;
            }
            
            if (!exportFormat) {
                e.preventDefault();
                alert('يرجى اختيار تنسيق التصدير');
                return;
            }
            
            // Show loading
            showLoading();
        });
        
        // Initialize preview
        updatePreview();
    });
</script>
{% endblock %}
