"""
إعدادات الأمان الشاملة لنظام إدارة الأرشيف العام
Security Configuration for Public Archive Management System
"""

import os
import secrets
from datetime import timedelta

class SecurityConfig:
    """إعدادات الأمان الأساسية"""
    
    # CSRF Protection
    WTF_CSRF_ENABLED = True
    WTF_CSRF_TIME_LIMIT = 3600  # 1 hour
    WTF_CSRF_SSL_STRICT = False  # Set to True in production with HTTPS
    
    # Session Security
    SESSION_COOKIE_SECURE = True  # Set to True in production with HTTPS
    SESSION_COOKIE_HTTPONLY = True
    SESSION_COOKIE_SAMESITE = 'Lax'
    PERMANENT_SESSION_LIFETIME = timedelta(hours=2)
    
    # File Upload Security
    MAX_CONTENT_LENGTH = 50 * 1024 * 1024  # 50MB
    ALLOWED_EXTENSIONS = {
        'pdf', 'doc', 'docx', 'txt', 'rtf',
        'jpg', 'jpeg', 'png', 'gif', 'bmp', 'tiff'
    }
    
    # Rate Limiting
    RATELIMIT_STORAGE_URL = "memory://"
    RATELIMIT_DEFAULT = "200 per day, 50 per hour"
    RATELIMIT_HEADERS_ENABLED = True
    
    # Password Security
    PASSWORD_MIN_LENGTH = 8
    PASSWORD_REQUIRE_UPPERCASE = True
    PASSWORD_REQUIRE_LOWERCASE = True
    PASSWORD_REQUIRE_NUMBERS = True
    PASSWORD_REQUIRE_SPECIAL = True
    
    # Encryption
    ENCRYPTION_KEY = os.environ.get('ENCRYPTION_KEY') or secrets.token_urlsafe(32)
    
    # Security Headers (Talisman)
    SECURITY_HEADERS = {
        'force_https': False,  # Set to True in production
        'strict_transport_security': True,
        'strict_transport_security_max_age': 31536000,
        'content_security_policy': {
            'default-src': "'self'",
            'script-src': [
                "'self'",
                "'unsafe-inline'",
                "'unsafe-eval'",
                "https://cdn.jsdelivr.net",
                "https://cdnjs.cloudflare.com"
            ],
            'style-src': [
                "'self'",
                "'unsafe-inline'",
                "https://cdn.jsdelivr.net",
                "https://cdnjs.cloudflare.com",
                "https://fonts.googleapis.com"
            ],
            'font-src': [
                "'self'",
                "https://fonts.gstatic.com",
                "https://cdn.jsdelivr.net"
            ],
            'img-src': [
                "'self'",
                "data:",
                "https:"
            ],
            'connect-src': "'self'",
            'frame-ancestors': "'none'"
        }
    }
    
    # Logging Security Events
    SECURITY_LOG_LEVEL = 'WARNING'
    SECURITY_LOG_FILE = 'logs/security.log'
    
    # Failed Login Protection
    MAX_FAILED_LOGINS = 5
    FAILED_LOGIN_LOCKOUT_TIME = timedelta(minutes=15)
    
    # File Validation
    DANGEROUS_FILE_PATTERNS = [
        r'\.\./', r'\.\.\\', r'<script', r'javascript:', r'vbscript:',
        r'onload=', r'onerror=', r'<iframe', r'<object', r'<embed'
    ]
    
    # Database Security
    SQLALCHEMY_ENGINE_OPTIONS = {
        'pool_pre_ping': True,
        'pool_recycle': 300,
        'connect_args': {
            'check_same_thread': False,
            'timeout': 20
        }
    }

class ProductionSecurityConfig(SecurityConfig):
    """إعدادات الأمان للإنتاج"""
    
    # Enhanced security for production
    SESSION_COOKIE_SECURE = True
    WTF_CSRF_SSL_STRICT = True
    
    # Stricter rate limiting
    RATELIMIT_DEFAULT = "100 per day, 20 per hour"
    
    # Force HTTPS
    SECURITY_HEADERS = {
        **SecurityConfig.SECURITY_HEADERS,
        'force_https': True
    }
    
    # Enhanced logging
    SECURITY_LOG_LEVEL = 'INFO'

class DevelopmentSecurityConfig(SecurityConfig):
    """إعدادات الأمان للتطوير"""
    
    # Relaxed settings for development
    SESSION_COOKIE_SECURE = False
    WTF_CSRF_SSL_STRICT = False
    
    # More permissive rate limiting
    RATELIMIT_DEFAULT = "1000 per day, 200 per hour"
    
    # Debug logging
    SECURITY_LOG_LEVEL = 'DEBUG'

# Security validation functions
def validate_password_strength(password):
    """فحص قوة كلمة المرور"""
    errors = []
    
    if len(password) < SecurityConfig.PASSWORD_MIN_LENGTH:
        errors.append(f"كلمة المرور يجب أن تكون {SecurityConfig.PASSWORD_MIN_LENGTH} أحرف على الأقل")
    
    if SecurityConfig.PASSWORD_REQUIRE_UPPERCASE and not any(c.isupper() for c in password):
        errors.append("كلمة المرور يجب أن تحتوي على حرف كبير واحد على الأقل")
    
    if SecurityConfig.PASSWORD_REQUIRE_LOWERCASE and not any(c.islower() for c in password):
        errors.append("كلمة المرور يجب أن تحتوي على حرف صغير واحد على الأقل")
    
    if SecurityConfig.PASSWORD_REQUIRE_NUMBERS and not any(c.isdigit() for c in password):
        errors.append("كلمة المرور يجب أن تحتوي على رقم واحد على الأقل")
    
    if SecurityConfig.PASSWORD_REQUIRE_SPECIAL:
        special_chars = "!@#$%^&*(),.?\":{}|<>"
        if not any(c in special_chars for c in password):
            errors.append("كلمة المرور يجب أن تحتوي على رمز خاص واحد على الأقل")
    
    return len(errors) == 0, errors

def get_security_config(environment='development'):
    """الحصول على إعدادات الأمان حسب البيئة"""
    if environment == 'production':
        return ProductionSecurityConfig
    elif environment == 'development':
        return DevelopmentSecurityConfig
    else:
        return SecurityConfig

# Security middleware functions
def setup_security_logging():
    """إعداد تسجيل الأحداث الأمنية"""
    import logging
    from pathlib import Path
    
    # Create logs directory
    Path('logs').mkdir(exist_ok=True)
    
    # Setup security logger
    security_logger = logging.getLogger('security')
    security_logger.setLevel(getattr(logging, SecurityConfig.SECURITY_LOG_LEVEL))
    
    # File handler
    file_handler = logging.FileHandler(SecurityConfig.SECURITY_LOG_FILE)
    file_handler.setLevel(logging.WARNING)
    
    # Formatter
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    file_handler.setFormatter(formatter)
    
    security_logger.addHandler(file_handler)
    
    return security_logger

def validate_file_security(file):
    """التحقق من أمان الملف"""
    import re
    
    if not file or not file.filename:
        return False, "لم يتم اختيار ملف"
    
    # Check file extension
    file_ext = file.filename.rsplit('.', 1)[1].lower() if '.' in file.filename else ''
    if file_ext not in SecurityConfig.ALLOWED_EXTENSIONS:
        return False, f"نوع الملف {file_ext} غير مسموح"
    
    # Check file size
    file.seek(0, 2)
    file_size = file.tell()
    file.seek(0)
    
    if file_size > SecurityConfig.MAX_CONTENT_LENGTH:
        return False, "حجم الملف كبير جداً"
    
    # Check for dangerous patterns
    filename_lower = file.filename.lower()
    for pattern in SecurityConfig.DANGEROUS_FILE_PATTERNS:
        if re.search(pattern, filename_lower):
            return False, "اسم الملف يحتوي على محتوى مشبوه"
    
    return True, "الملف آمن"

# Export main configuration
__all__ = [
    'SecurityConfig',
    'ProductionSecurityConfig', 
    'DevelopmentSecurityConfig',
    'get_security_config',
    'validate_password_strength',
    'setup_security_logging',
    'validate_file_security'
]
