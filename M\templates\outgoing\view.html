{% extends "base.html" %}

{% block title %}{{ document.subject }} - الكتب الصادرة{% endblock %}

{% block extra_css %}
<style>
    .document-header {
        background: linear-gradient(135deg, #e67e22 0%, #f39c12 100%);
        color: white;
        border-radius: var(--border-radius);
        padding: 2rem;
        margin-bottom: 2rem;
        position: relative;
        overflow: hidden;
    }
    
    .document-header::before {
        content: '';
        position: absolute;
        top: -50%;
        right: -50%;
        width: 200%;
        height: 200%;
        background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
        animation: float 6s ease-in-out infinite;
    }
    
    .document-content {
        position: relative;
        z-index: 2;
    }
    
    .document-title {
        font-size: 2rem;
        font-weight: 700;
        margin-bottom: 1rem;
    }
    
    .document-meta {
        display: flex;
        flex-wrap: wrap;
        gap: 2rem;
        opacity: 0.9;
    }
    
    .meta-item {
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }
    
    .details-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 2rem;
        margin-bottom: 2rem;
    }
    
    .detail-card {
        background: white;
        border-radius: var(--border-radius);
        padding: 2rem;
        box-shadow: var(--shadow-light);
        transition: all 0.3s ease;
    }
    
    .detail-card:hover {
        transform: translateY(-5px);
        box-shadow: var(--shadow-medium);
    }
    
    .card-title {
        font-size: 1.25rem;
        font-weight: 600;
        color: var(--primary-color);
        margin-bottom: 1.5rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }
    
    .detail-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0.75rem 0;
        border-bottom: 1px solid #f8f9fa;
    }
    
    .detail-row:last-child {
        border-bottom: none;
    }
    
    .detail-label {
        font-weight: 600;
        color: #6c757d;
    }
    
    .detail-value {
        color: var(--primary-color);
        font-weight: 500;
    }
    
    .priority-badge {
        padding: 0.5rem 1rem;
        border-radius: 20px;
        font-weight: 600;
        font-size: 0.9rem;
    }
    
    .priority-عادي {
        background: rgba(149, 165, 166, 0.1);
        color: #95a5a6;
    }
    
    .priority-مهم {
        background: rgba(241, 196, 15, 0.1);
        color: #f1c40f;
    }
    
    .priority-عاجل {
        background: rgba(231, 76, 60, 0.1);
        color: #e74c3c;
    }
    
    .status-badge {
        padding: 0.5rem 1rem;
        border-radius: 20px;
        font-weight: 600;
        font-size: 0.9rem;
    }
    
    .status-مسودة {
        background: rgba(149, 165, 166, 0.1);
        color: #95a5a6;
    }
    
    .status-جاهز_للإرسال {
        background: rgba(241, 196, 15, 0.1);
        color: #f1c40f;
    }
    
    .status-تم_الإرسال {
        background: rgba(39, 174, 96, 0.1);
        color: #27ae60;
    }
    
    .status-مؤرشف {
        background: rgba(52, 152, 219, 0.1);
        color: #3498db;
    }
    
    .action-buttons {
        background: white;
        border-radius: var(--border-radius);
        padding: 2rem;
        box-shadow: var(--shadow-light);
        display: flex;
        gap: 1rem;
        justify-content: center;
        flex-wrap: wrap;
    }
    
    .btn-action {
        padding: 0.75rem 2rem;
        border-radius: var(--border-radius);
        font-weight: 600;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        transition: all 0.3s ease;
        border: none;
        cursor: pointer;
    }
    
    .btn-edit {
        background: linear-gradient(135deg, #f39c12 0%, #f1c40f 100%);
        color: white;
    }
    
    .btn-delete {
        background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
        color: white;
    }
    
    .btn-back {
        background: #6c757d;
        color: white;
    }
    
    .btn-action:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-medium);
        color: white;
    }

    .file-attachment {
        display: flex;
        justify-content: space-between;
        align-items: center;
        background: #f8f9fa;
        border-radius: var(--border-radius);
        padding: 1rem;
        border: 1px solid #dee2e6;
    }

    .file-info {
        display: flex;
        align-items: center;
        gap: 1rem;
    }

    .file-icon {
        font-size: 2rem;
        color: #e67e22;
    }

    .file-name {
        font-weight: 600;
        color: var(--primary-color);
    }

    .file-size {
        color: #6c757d;
    }

    .btn-download {
        background: linear-gradient(135deg, #e67e22 0%, #f39c12 100%);
        color: white;
        border: none;
        border-radius: var(--border-radius);
        padding: 0.5rem 1rem;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        transition: all 0.3s ease;
    }

    .btn-download:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-light);
        color: white;
    }
    
    @media (max-width: 768px) {
        .details-grid {
            grid-template-columns: 1fr;
        }
        
        .document-meta {
            flex-direction: column;
            gap: 1rem;
        }
        
        .action-buttons {
            flex-direction: column;
        }
    }
</style>
{% endblock %}

{% block content %}
<!-- Document Header -->
<div class="document-header">
    <div class="document-content">
        <h1 class="document-title">{{ document.subject }}</h1>
        <div class="document-meta">
            <div class="meta-item">
                <i class="fas fa-hashtag"></i>
                <span><strong>{{ document.outgoing_number }}</strong></span>
            </div>
            <div class="meta-item">
                <i class="fas fa-flag"></i>
                <span class="priority-badge priority-{{ document.priority }}">
                    {{ document.priority }}
                </span>
            </div>
            <div class="meta-item">
                <i class="fas fa-info-circle"></i>
                <span class="status-badge status-{{ document.status.replace(' ', '_') }}">
                    {{ document.status }}
                </span>
            </div>
            <div class="meta-item">
                <i class="fas fa-calendar"></i>
                <span>{{ document.sent_date.strftime('%Y/%m/%d') }}</span>
            </div>
        </div>
    </div>
</div>

<div class="details-grid">
    <!-- Document Details -->
    <div class="detail-card">
        <h3 class="card-title">
            <i class="fas fa-paper-plane"></i>
            تفاصيل الكتاب الصادر
        </h3>
        
        <div class="detail-row">
            <span class="detail-label">رقم الصادر:</span>
            <span class="detail-value">{{ document.outgoing_number }}</span>
        </div>
        
        <div class="detail-row">
            <span class="detail-label">الموضوع:</span>
            <span class="detail-value">{{ document.subject }}</span>
        </div>
        
        <div class="detail-row">
            <span class="detail-label">الجهة المرسل إليها:</span>
            <span class="detail-value">{{ document.recipient_name }}</span>
        </div>
        
        <div class="detail-row">
            <span class="detail-label">تاريخ الإرسال:</span>
            <span class="detail-value">{{ document.sent_date.strftime('%Y/%m/%d') }}</span>
        </div>
        
        <div class="detail-row">
            <span class="detail-label">الأولوية:</span>
            <span class="priority-badge priority-{{ document.priority }}">{{ document.priority }}</span>
        </div>
        
        <div class="detail-row">
            <span class="detail-label">الحالة:</span>
            <span class="status-badge status-{{ document.status.replace(' ', '_') }}">{{ document.status }}</span>
        </div>
        
        {% if document.notes %}
        <div class="detail-row">
            <span class="detail-label">الملاحظات:</span>
            <span class="detail-value">{{ document.notes }}</span>
        </div>
        {% endif %}

        {% if document.file_name %}
        <div class="detail-row">
            <span class="detail-label">الملف المرفق:</span>
            <div class="file-attachment">
                <div class="file-info">
                    <i class="fas fa-file-{{ 'pdf' if document.file_type == 'pdf' else 'word' if document.file_type == 'document' else 'image' if document.file_type == 'image' else 'alt' }} file-icon"></i>
                    <div class="file-details">
                        <div class="file-name">{{ document.file_name }}</div>
                        <small class="file-size">{{ (document.file_size / 1024 / 1024) | round(2) }} ميجابايت</small>
                    </div>
                </div>
                <a href="{{ url_for('download_file', filename=document.file_path.split('/')[-1]) }}"
                   class="btn-download" title="تحميل الملف">
                    <i class="fas fa-download"></i>
                </a>
            </div>
        </div>
        {% endif %}
    </div>
    
    <!-- System Information -->
    <div class="detail-card">
        <h3 class="card-title">
            <i class="fas fa-cog"></i>
            معلومات النظام
        </h3>
        
        <div class="detail-row">
            <span class="detail-label">رقم السجل:</span>
            <span class="detail-value">#{{ document.id }}</span>
        </div>
        
        <div class="detail-row">
            <span class="detail-label">تاريخ الإنشاء:</span>
            <span class="detail-value">{{ document.created_at.strftime('%Y/%m/%d %H:%M') }}</span>
        </div>
        
        <div class="detail-row">
            <span class="detail-label">المُعِد:</span>
            <span class="detail-value">{{ document.preparer.full_name if document.preparer else 'غير محدد' }}</span>
        </div>
        
        <div class="detail-row">
            <span class="detail-label">القسم:</span>
            <span class="detail-value">{{ document.preparer.department if document.preparer else 'غير محدد' }}</span>
        </div>
    </div>
</div>

<!-- Action Buttons -->
<div class="action-buttons">
    <a href="{{ url_for('outgoing_documents') }}" class="btn-action btn-back">
        <i class="fas fa-arrow-right"></i>
        العودة للقائمة
    </a>
    
    {% if current_user.is_admin() or document.prepared_by == current_user.id %}
    <a href="{{ url_for('edit_outgoing', id=document.id) }}" class="btn-action btn-edit">
        <i class="fas fa-edit"></i>
        تعديل الكتاب
    </a>
    
    <button type="button" class="btn-action btn-delete" 
            onclick="confirmDelete({{ document.id }}, '{{ document.outgoing_number }}')">
        <i class="fas fa-trash"></i>
        حذف الكتاب
    </button>
    {% endif %}
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تأكيد الحذف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من حذف الكتاب الصادر رقم "<span id="documentNumber"></span>"؟</p>
                <p class="text-danger"><strong>تحذير:</strong> هذا الإجراء لا يمكن التراجع عنه.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-trash me-2"></i>
                        حذف نهائياً
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    function confirmDelete(documentId, documentNumber) {
        document.getElementById('documentNumber').textContent = documentNumber;
        document.getElementById('deleteForm').action = `/outgoing/${documentId}/delete`;
        
        const modal = new bootstrap.Modal(document.getElementById('deleteModal'));
        modal.show();
    }
</script>
{% endblock %}
