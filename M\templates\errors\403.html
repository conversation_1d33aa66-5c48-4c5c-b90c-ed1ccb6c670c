{% extends "base.html" %}

{% block title %}ممنوع الوصول - نظام إدارة الأرشيف العام{% endblock %}

{% block extra_css %}
<style>
    .error-container {
        min-height: 70vh;
        display: flex;
        align-items: center;
        justify-content: center;
        text-align: center;
    }
    
    .error-content {
        max-width: 600px;
        padding: 2rem;
    }
    
    .error-icon {
        font-size: 8rem;
        color: #f39c12;
        margin-bottom: 2rem;
        animation: swing 2s infinite;
    }
    
    @keyframes swing {
        0%, 100% { transform: rotate(0deg); }
        25% { transform: rotate(10deg); }
        75% { transform: rotate(-10deg); }
    }
    
    .error-title {
        font-size: 3rem;
        font-weight: 700;
        color: #2c3e50;
        margin-bottom: 1rem;
    }
    
    .error-message {
        font-size: 1.2rem;
        color: #7f8c8d;
        margin-bottom: 2rem;
        line-height: 1.6;
    }
    
    .error-actions {
        display: flex;
        gap: 1rem;
        justify-content: center;
        flex-wrap: wrap;
    }
    
    .btn-error {
        padding: 0.75rem 2rem;
        border-radius: 25px;
        font-weight: 600;
        text-decoration: none;
        transition: all 0.3s ease;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
    }
    
    .btn-primary-error {
        background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
        color: white;
        border: none;
    }
    
    .btn-primary-error:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(243, 156, 18, 0.3);
        color: white;
    }
    
    .btn-secondary-error {
        background: #ecf0f1;
        color: #2c3e50;
        border: 2px solid #bdc3c7;
    }
    
    .btn-secondary-error:hover {
        background: #d5dbdb;
        color: #2c3e50;
        transform: translateY(-2px);
    }
    
    .permission-info {
        background: #fff8e1;
        border: 1px solid #ffcc02;
        border-radius: 10px;
        padding: 1.5rem;
        margin-top: 2rem;
        text-align: right;
    }
    
    .permission-info h6 {
        color: #f57c00;
        margin-bottom: 1rem;
    }
    
    .permission-info p {
        color: #e65100;
        margin: 0;
        font-size: 0.9rem;
    }
    
    .contact-admin {
        background: #f3e5f5;
        border: 1px solid #ce93d8;
        border-radius: 10px;
        padding: 1.5rem;
        margin-top: 2rem;
        text-align: right;
    }
    
    .contact-admin h6 {
        color: #7b1fa2;
        margin-bottom: 1rem;
    }
    
    .contact-admin p {
        color: #4a148c;
        margin: 0;
        font-size: 0.9rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="error-container">
    <div class="error-content">
        <div class="error-icon">
            <i class="fas fa-lock"></i>
        </div>
        
        <h1 class="error-title">403</h1>
        
        <p class="error-message">
            عذراً، ليس لديك صلاحية للوصول إلى هذه الصفحة.<br>
            يرجى التأكد من أن لديك الصلاحيات المناسبة أو التواصل مع مدير النظام.
        </p>
        
        <div class="error-actions">
            <a href="{{ url_for('dashboard') }}" class="btn-error btn-primary-error">
                <i class="fas fa-home"></i>
                العودة للرئيسية
            </a>
            
            <button onclick="history.back()" class="btn-error btn-secondary-error">
                <i class="fas fa-arrow-right"></i>
                الصفحة السابقة
            </button>
        </div>
        
        <div class="permission-info">
            <h6><i class="fas fa-shield-alt me-2"></i>معلومات الصلاحيات:</h6>
            <p>
                هذه الصفحة تتطلب صلاحيات خاصة للوصول إليها. قد تحتاج إلى:
            </p>
            <ul class="mt-2 text-start">
                <li>صلاحيات إدارية للوصول لإعدادات النظام</li>
                <li>صلاحيات خاصة لعرض بيانات معينة</li>
                <li>أن تكون مالك المحتوى المطلوب</li>
            </ul>
        </div>
        
        <div class="contact-admin">
            <h6><i class="fas fa-user-shield me-2"></i>طلب صلاحيات:</h6>
            <p>
                إذا كنت تعتقد أنه يجب أن يكون لديك صلاحية للوصول إلى هذه الصفحة،
                يرجى التواصل مع مدير النظام لمراجعة صلاحياتك.
            </p>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // إضافة تأثيرات تفاعلية
    document.addEventListener('DOMContentLoaded', function() {
        // تأثير الظهور التدريجي
        const content = document.querySelector('.error-content');
        content.style.opacity = '0';
        content.style.transform = 'translateY(30px)';
        content.style.transition = 'all 0.6s ease';
        
        setTimeout(() => {
            content.style.opacity = '1';
            content.style.transform = 'translateY(0)';
        }, 100);
        
        // تسجيل محاولة الوصول غير المصرح بها
        console.warn('محاولة وصول غير مصرح بها - خطأ 403');
    });
</script>
{% endblock %}
