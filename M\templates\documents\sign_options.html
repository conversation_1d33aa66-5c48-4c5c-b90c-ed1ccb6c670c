{% extends "base.html" %}

{% block title %}التوقيع الرقمي - {{ document.title }}{% endblock %}

{% block extra_css %}
<style>
    .signature-container {
        max-width: 800px;
        margin: 0 auto;
        padding: 2rem;
    }
    
    .document-info {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 15px;
        padding: 2rem;
        margin-bottom: 2rem;
        text-align: center;
    }
    
    .document-title {
        font-size: 1.5rem;
        font-weight: 700;
        margin-bottom: 1rem;
    }
    
    .document-details {
        display: flex;
        justify-content: center;
        gap: 2rem;
        flex-wrap: wrap;
        margin-top: 1rem;
    }
    
    .detail-item {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        font-size: 0.9rem;
        opacity: 0.9;
    }
    
    .signature-options {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
        gap: 2rem;
        margin-bottom: 2rem;
    }
    
    .signature-option {
        background: white;
        border: 2px solid #e9ecef;
        border-radius: 15px;
        padding: 2rem;
        text-align: center;
        transition: all 0.3s ease;
        cursor: pointer;
        position: relative;
        overflow: hidden;
    }
    
    .signature-option:hover {
        border-color: #007bff;
        transform: translateY(-5px);
        box-shadow: 0 10px 30px rgba(0, 123, 255, 0.1);
    }
    
    .signature-option.selected {
        border-color: #007bff;
        background: linear-gradient(135deg, #f8f9ff 0%, #e3f2fd 100%);
    }
    
    .signature-icon {
        font-size: 3rem;
        margin-bottom: 1rem;
        color: #007bff;
    }
    
    .signature-title {
        font-size: 1.3rem;
        font-weight: 700;
        color: #2c3e50;
        margin-bottom: 1rem;
    }
    
    .signature-description {
        color: #6c757d;
        line-height: 1.6;
        margin-bottom: 1.5rem;
    }
    
    .signature-features {
        list-style: none;
        padding: 0;
        text-align: right;
    }
    
    .signature-features li {
        padding: 0.5rem 0;
        border-bottom: 1px solid #f1f3f4;
        color: #495057;
        font-size: 0.9rem;
    }
    
    .signature-features li:last-child {
        border-bottom: none;
    }
    
    .signature-features i {
        color: #28a745;
        margin-left: 0.5rem;
    }
    
    .existing-signatures {
        background: #fff3cd;
        border: 1px solid #ffeaa7;
        border-radius: 10px;
        padding: 1.5rem;
        margin-bottom: 2rem;
    }
    
    .existing-signatures h6 {
        color: #856404;
        margin-bottom: 1rem;
    }
    
    .signature-badge {
        display: inline-block;
        padding: 0.5rem 1rem;
        border-radius: 20px;
        font-size: 0.8rem;
        margin: 0.25rem;
    }
    
    .signature-badge.normal {
        background: #d1ecf1;
        color: #0c5460;
    }
    
    .signature-badge.certified {
        background: #d4edda;
        color: #155724;
    }
    
    .notes-section {
        margin: 2rem 0;
    }
    
    .notes-textarea {
        width: 100%;
        min-height: 100px;
        border: 2px solid #e9ecef;
        border-radius: 10px;
        padding: 1rem;
        font-family: inherit;
        resize: vertical;
    }
    
    .notes-textarea:focus {
        border-color: #007bff;
        outline: none;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    }
    
    .action-buttons {
        display: flex;
        gap: 1rem;
        justify-content: center;
        margin-top: 2rem;
    }
    
    .btn-sign {
        padding: 1rem 2rem;
        border-radius: 25px;
        font-weight: 600;
        font-size: 1.1rem;
        border: none;
        cursor: pointer;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }
    
    .btn-sign:disabled {
        opacity: 0.6;
        cursor: not-allowed;
    }
    
    .btn-primary-sign {
        background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
        color: white;
    }
    
    .btn-primary-sign:hover:not(:disabled) {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0, 123, 255, 0.3);
    }
    
    .btn-secondary-sign {
        background: #6c757d;
        color: white;
    }
    
    .btn-secondary-sign:hover {
        background: #5a6268;
    }
    
    .loading-spinner {
        display: none;
        width: 20px;
        height: 20px;
        border: 2px solid #ffffff;
        border-top: 2px solid transparent;
        border-radius: 50%;
        animation: spin 1s linear infinite;
    }
    
    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }
</style>
{% endblock %}

{% block content %}
<div class="signature-container">
    <!-- معلومات الوثيقة -->
    <div class="document-info">
        <div class="document-title">
            <i class="fas fa-file-alt me-2"></i>
            {{ document.title }}
        </div>
        <div class="document-details">
            <div class="detail-item">
                <i class="fas fa-tag"></i>
                <span>{{ document.document_type }}</span>
            </div>
            <div class="detail-item">
                <i class="fas fa-user"></i>
                <span>{{ document.created_by.full_name or document.created_by.username }}</span>
            </div>
            <div class="detail-item">
                <i class="fas fa-calendar"></i>
                <span>{{ document.created_at.strftime('%Y/%m/%d') }}</span>
            </div>
        </div>
    </div>
    
    <!-- التوقيعات الموجودة -->
    {% if existing_signatures %}
    <div class="existing-signatures">
        <h6><i class="fas fa-info-circle me-2"></i>التوقيعات الموجودة:</h6>
        {% for sig in existing_signatures %}
        <span class="signature-badge {{ sig.signature_type }}">
            {{ sig.get_signature_type_display() }} - {{ sig.timestamp.strftime('%Y/%m/%d %H:%M') }}
        </span>
        {% endfor %}
    </div>
    {% endif %}
    
    <!-- خيارات التوقيع -->
    <div class="signature-options">
        <!-- التوقيع العادي -->
        <div class="signature-option" data-type="normal">
            <div class="signature-icon">
                <i class="fas fa-pen"></i>
            </div>
            <div class="signature-title">توقيع عادي</div>
            <div class="signature-description">
                للمراجعة والموافقة الأولية على الوثائق
            </div>
            <ul class="signature-features">
                <li><i class="fas fa-check"></i>مراجعة وموافقة أولية</li>
                <li><i class="fas fa-check"></i>تشفير SHA-256</li>
                <li><i class="fas fa-check"></i>صالح للاستخدام العام</li>
                <li><i class="fas fa-check"></i>سهل وسريع</li>
            </ul>
        </div>
        
        <!-- التوقيع المعتمد -->
        <div class="signature-option" data-type="certified">
            <div class="signature-icon">
                <i class="fas fa-certificate"></i>
            </div>
            <div class="signature-title">توقيع معتمد</div>
            <div class="signature-description">
                للاعتماد النهائي والتصديق الرسمي على الوثائق
            </div>
            <ul class="signature-features">
                <li><i class="fas fa-check"></i>اعتماد نهائي رسمي</li>
                <li><i class="fas fa-check"></i>تشفير SHA-512 المتقدم</li>
                <li><i class="fas fa-check"></i>مستوى أمان عالي</li>
                <li><i class="fas fa-check"></i>قيمة قانونية أكبر</li>
            </ul>
        </div>
    </div>
    
    <!-- قسم الملاحظات -->
    <div class="notes-section">
        <label for="signatureNotes" class="form-label">
            <i class="fas fa-sticky-note me-2"></i>ملاحظات التوقيع (اختياري)
        </label>
        <textarea id="signatureNotes" class="notes-textarea" 
                  placeholder="أضف أي ملاحظات أو تعليقات حول التوقيع..."></textarea>
    </div>
    
    <!-- أزرار العمل -->
    <div class="action-buttons">
        <button id="signBtn" class="btn-sign btn-primary-sign" disabled>
            <div class="loading-spinner"></div>
            <i class="fas fa-signature"></i>
            <span>توقيع الوثيقة</span>
        </button>
        
        <a href="{{ url_for('view_document', id=document.id) }}" class="btn-sign btn-secondary-sign">
            <i class="fas fa-times"></i>
            <span>إلغاء</span>
        </a>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    let selectedType = null;
    const signBtn = document.getElementById('signBtn');
    const notesTextarea = document.getElementById('signatureNotes');
    const loadingSpinner = signBtn.querySelector('.loading-spinner');
    
    // اختيار نوع التوقيع
    document.querySelectorAll('.signature-option').forEach(option => {
        option.addEventListener('click', function() {
            // إزالة التحديد السابق
            document.querySelectorAll('.signature-option').forEach(opt => {
                opt.classList.remove('selected');
            });
            
            // تحديد الخيار الحالي
            this.classList.add('selected');
            selectedType = this.dataset.type;
            
            // تفعيل زر التوقيع
            signBtn.disabled = false;
            
            // تحديث نص الزر
            const typeText = selectedType === 'normal' ? 'التوقيع العادي' : 'التوقيع المعتمد';
            signBtn.querySelector('span').textContent = `تطبيق ${typeText}`;
        });
    });
    
    // تنفيذ التوقيع
    signBtn.addEventListener('click', function() {
        if (!selectedType) {
            alert('يرجى اختيار نوع التوقيع أولاً');
            return;
        }
        
        // إظهار التحميل
        this.disabled = true;
        loadingSpinner.style.display = 'block';
        
        // إرسال طلب التوقيع
        fetch(`/documents/{{ document.id }}/sign/${selectedType}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                notes: notesTextarea.value.trim()
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // إظهار رسالة نجاح
                showAlert('success', data.message);
                
                // إعادة توجيه بعد ثانيتين
                setTimeout(() => {
                    window.location.href = '{{ url_for("view_document", id=document.id) }}';
                }, 2000);
            } else {
                showAlert('error', data.message);
                this.disabled = false;
                loadingSpinner.style.display = 'none';
            }
        })
        .catch(error => {
            console.error('خطأ:', error);
            showAlert('error', 'حدث خطأ في التوقيع الرقمي');
            this.disabled = false;
            loadingSpinner.style.display = 'none';
        });
    });
    
    function showAlert(type, message) {
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type === 'success' ? 'success' : 'danger'} alert-dismissible fade show`;
        alertDiv.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        document.querySelector('.signature-container').insertBefore(alertDiv, document.querySelector('.signature-container').firstChild);
        
        // إزالة التنبيه بعد 5 ثوان
        setTimeout(() => {
            alertDiv.remove();
        }, 5000);
    }
});
</script>
{% endblock %}
