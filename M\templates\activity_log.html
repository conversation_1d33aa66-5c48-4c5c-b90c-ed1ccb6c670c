{% extends "base.html" %}

{% block title %}سجل الأنشطة - نظام إدارة الأرشيف العام{% endblock %}

{% block extra_css %}
<style>
    .activity-header {
        background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
        color: white;
        border-radius: var(--border-radius);
        padding: 2rem;
        margin-bottom: 2rem;
        text-align: center;
    }
    
    .activity-title {
        font-size: 2rem;
        font-weight: 700;
        margin: 0;
    }
    
    .activity-subtitle {
        opacity: 0.9;
        margin: 0.5rem 0 0 0;
    }
    
    .filters-card {
        background: white;
        border-radius: var(--border-radius);
        padding: 1.5rem;
        margin-bottom: 2rem;
        box-shadow: var(--shadow-light);
    }
    
    .filters-row {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
        align-items: end;
    }
    
    .filter-group {
        display: flex;
        flex-direction: column;
    }
    
    .filter-label {
        font-weight: 600;
        margin-bottom: 0.5rem;
        color: #2c3e50;
    }
    
    .filter-select {
        padding: 0.75rem;
        border: 2px solid #e9ecef;
        border-radius: var(--border-radius);
        background: white;
        transition: border-color 0.3s ease;
    }
    
    .filter-select:focus {
        outline: none;
        border-color: var(--primary-color);
    }
    
    .btn-filter {
        background: var(--primary-color);
        color: white;
        border: none;
        padding: 0.75rem 1.5rem;
        border-radius: var(--border-radius);
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
    }
    
    .btn-filter:hover {
        background: #2980b9;
        transform: translateY(-2px);
    }
    
    .btn-clear {
        background: #6c757d;
        color: white;
        border: none;
        padding: 0.75rem 1.5rem;
        border-radius: var(--border-radius);
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        margin-right: 0.5rem;
    }
    
    .btn-clear:hover {
        background: #5a6268;
    }
    
    .activity-table {
        background: white;
        border-radius: var(--border-radius);
        overflow: hidden;
        box-shadow: var(--shadow-light);
    }
    
    .table {
        margin: 0;
        width: 100%;
    }
    
    .table th {
        background: #f8f9fa;
        font-weight: 600;
        color: #2c3e50;
        border: none;
        padding: 1rem;
    }
    
    .table td {
        padding: 1rem;
        border-top: 1px solid #e9ecef;
        vertical-align: middle;
    }
    
    .table tbody tr:hover {
        background: #f8f9fa;
    }
    
    .action-badge {
        padding: 0.25rem 0.75rem;
        border-radius: 20px;
        font-size: 0.875rem;
        font-weight: 600;
        text-transform: uppercase;
    }
    
    .action-create {
        background: #d4edda;
        color: #155724;
    }
    
    .action-update {
        background: #d1ecf1;
        color: #0c5460;
    }
    
    .action-delete {
        background: #f8d7da;
        color: #721c24;
    }
    
    .action-view {
        background: #fff3cd;
        color: #856404;
    }
    
    .action-login {
        background: #e2e3e5;
        color: #383d41;
    }
    
    .entity-badge {
        padding: 0.25rem 0.5rem;
        border-radius: 15px;
        font-size: 0.8rem;
        background: #e9ecef;
        color: #495057;
    }
    
    .user-info {
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }
    
    .user-avatar {
        width: 32px;
        height: 32px;
        border-radius: 50%;
        background: var(--primary-color);
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 600;
        font-size: 0.875rem;
    }
    
    .time-info {
        font-size: 0.875rem;
        color: #6c757d;
    }
    
    .details-btn {
        background: none;
        border: 1px solid #dee2e6;
        color: #6c757d;
        padding: 0.25rem 0.5rem;
        border-radius: 15px;
        font-size: 0.8rem;
        cursor: pointer;
        transition: all 0.3s ease;
    }
    
    .details-btn:hover {
        background: #f8f9fa;
        border-color: #adb5bd;
    }
    
    .pagination-wrapper {
        display: flex;
        justify-content: center;
        margin-top: 2rem;
    }
    
    .no-activities {
        text-align: center;
        padding: 3rem;
        color: #6c757d;
    }
    
    .no-activities i {
        font-size: 3rem;
        margin-bottom: 1rem;
        opacity: 0.5;
    }
    
    @media (max-width: 768px) {
        .filters-row {
            grid-template-columns: 1fr;
        }
        
        .table-responsive {
            font-size: 0.875rem;
        }
        
        .user-info {
            flex-direction: column;
            align-items: flex-start;
        }
    }
</style>
{% endblock %}

{% block content %}
<!-- Header -->
<div class="activity-header">
    <h1 class="activity-title">
        <i class="fas fa-history me-3"></i>
        سجل الأنشطة
    </h1>
    <p class="activity-subtitle">تتبع جميع العمليات والأنشطة في النظام</p>
</div>

<!-- Filters -->
<div class="filters-card">
    <form method="GET" action="{{ url_for('activity_log') }}">
        <div class="filters-row">
            <div class="filter-group">
                <label class="filter-label">المستخدم</label>
                <select name="user_id" class="filter-select">
                    <option value="">جميع المستخدمين</option>
                    {% for user in users %}
                        <option value="{{ user.id }}" 
                                {{ 'selected' if current_filters.user_id == user.id else '' }}>
                            {{ user.full_name or user.username }}
                        </option>
                    {% endfor %}
                </select>
            </div>
            
            <div class="filter-group">
                <label class="filter-label">نوع العملية</label>
                <select name="action" class="filter-select">
                    <option value="">جميع العمليات</option>
                    {% for action in actions %}
                        <option value="{{ action }}" 
                                {{ 'selected' if current_filters.action == action else '' }}>
                            {% if action == 'create' %}إنشاء
                            {% elif action == 'update' %}تعديل
                            {% elif action == 'delete' %}حذف
                            {% elif action == 'view' %}عرض
                            {% elif action == 'login' %}تسجيل دخول
                            {% elif action == 'logout' %}تسجيل خروج
                            {% elif action == 'password_reset' %}إعادة تعيين كلمة المرور
                            {% else %}{{ action }}
                            {% endif %}
                        </option>
                    {% endfor %}
                </select>
            </div>
            
            <div class="filter-group">
                <label class="filter-label">نوع العنصر</label>
                <select name="entity_type" class="filter-select">
                    <option value="">جميع الأنواع</option>
                    {% for entity_type in entity_types %}
                        <option value="{{ entity_type }}" 
                                {{ 'selected' if current_filters.entity_type == entity_type else '' }}>
                            {% if entity_type == 'document' %}وثيقة
                            {% elif entity_type == 'incoming' %}كتاب وارد
                            {% elif entity_type == 'outgoing' %}كتاب صادر
                            {% elif entity_type == 'user' %}مستخدم
                            {% elif entity_type == 'system' %}النظام
                            {% else %}{{ entity_type }}
                            {% endif %}
                        </option>
                    {% endfor %}
                </select>
            </div>
            
            <div class="filter-group">
                <button type="submit" class="btn-filter">
                    <i class="fas fa-filter me-2"></i>
                    تطبيق الفلاتر
                </button>
                <a href="{{ url_for('activity_log') }}" class="btn-clear">
                    <i class="fas fa-times me-2"></i>
                    مسح الفلاتر
                </a>
            </div>
        </div>
    </form>
</div>

<!-- Activities Table -->
<div class="activity-table">
    {% if activities.items %}
        <div class="table-responsive">
            <table class="table">
                <thead>
                    <tr>
                        <th>المستخدم</th>
                        <th>العملية</th>
                        <th>النوع</th>
                        <th>العنصر</th>
                        <th>التوقيت</th>
                        <th>التفاصيل</th>
                    </tr>
                </thead>
                <tbody>
                    {% for activity in activities.items %}
                        <tr>
                            <td>
                                <div class="user-info">
                                    <div class="user-avatar">
                                        {{ activity.user.full_name[0] if activity.user.full_name else activity.user.username[0] }}
                                    </div>
                                    <div>
                                        <div>{{ activity.user.full_name or activity.user.username }}</div>
                                        <small class="text-muted">{{ activity.ip_address }}</small>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <span class="action-badge action-{{ activity.action }}">
                                    {% if activity.action == 'create' %}إنشاء
                                    {% elif activity.action == 'update' %}تعديل
                                    {% elif activity.action == 'delete' %}حذف
                                    {% elif activity.action == 'view' %}عرض
                                    {% elif activity.action == 'login' %}دخول
                                    {% elif activity.action == 'logout' %}خروج
                                    {% elif activity.action == 'password_reset' %}إعادة تعيين
                                    {% else %}{{ activity.action }}
                                    {% endif %}
                                </span>
                            </td>
                            <td>
                                <span class="entity-badge">
                                    {% if activity.entity_type == 'document' %}وثيقة
                                    {% elif activity.entity_type == 'incoming' %}كتاب وارد
                                    {% elif activity.entity_type == 'outgoing' %}كتاب صادر
                                    {% elif activity.entity_type == 'user' %}مستخدم
                                    {% elif activity.entity_type == 'system' %}النظام
                                    {% else %}{{ activity.entity_type }}
                                    {% endif %}
                                </span>
                            </td>
                            <td>
                                <div>{{ activity.entity_name or 'غير محدد' }}</div>
                                <small class="text-muted">#{{ activity.entity_id }}</small>
                            </td>
                            <td>
                                <div class="time-info">
                                    <div>{{ activity.created_at.strftime('%Y/%m/%d') }}</div>
                                    <div>{{ activity.created_at.strftime('%H:%M:%S') }}</div>
                                </div>
                            </td>
                            <td>
                                {% if activity.details %}
                                    <button class="details-btn" onclick="showDetails('{{ activity.details|e }}')">
                                        <i class="fas fa-eye me-1"></i>
                                        عرض
                                    </button>
                                {% else %}
                                    <span class="text-muted">-</span>
                                {% endif %}
                            </td>
                        </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        
        <!-- Pagination -->
        {% if activities.pages > 1 %}
            <div class="pagination-wrapper">
                <nav>
                    <ul class="pagination">
                        {% if activities.has_prev %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('activity_log', page=activities.prev_num, **current_filters) }}">السابق</a>
                            </li>
                        {% endif %}
                        
                        {% for page_num in activities.iter_pages() %}
                            {% if page_num %}
                                {% if page_num != activities.page %}
                                    <li class="page-item">
                                        <a class="page-link" href="{{ url_for('activity_log', page=page_num, **current_filters) }}">{{ page_num }}</a>
                                    </li>
                                {% else %}
                                    <li class="page-item active">
                                        <span class="page-link">{{ page_num }}</span>
                                    </li>
                                {% endif %}
                            {% else %}
                                <li class="page-item disabled">
                                    <span class="page-link">…</span>
                                </li>
                            {% endif %}
                        {% endfor %}
                        
                        {% if activities.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('activity_log', page=activities.next_num, **current_filters) }}">التالي</a>
                            </li>
                        {% endif %}
                    </ul>
                </nav>
            </div>
        {% endif %}
    {% else %}
        <div class="no-activities">
            <i class="fas fa-history"></i>
            <h4>لا توجد أنشطة</h4>
            <p>لم يتم العثور على أنشطة تطابق الفلاتر المحددة</p>
        </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script>
    function showDetails(details) {
        try {
            const data = JSON.parse(details);
            let content = '<div class="details-content">';
            
            for (const [key, value] of Object.entries(data)) {
                content += `<div class="detail-item">
                    <strong>${key}:</strong> ${value}
                </div>`;
            }
            
            content += '</div>';
            
            // استخدام SweetAlert أو modal بسيط
            alert('التفاصيل:\n' + JSON.stringify(data, null, 2));
        } catch (e) {
            alert('التفاصيل:\n' + details);
        }
    }
    
    // تحسين تجربة المستخدم
    document.addEventListener('DOMContentLoaded', function() {
        // إضافة تأثيرات للجدول
        const rows = document.querySelectorAll('.table tbody tr');
        rows.forEach((row, index) => {
            setTimeout(() => {
                row.style.opacity = '0';
                row.style.transform = 'translateY(20px)';
                row.style.transition = 'all 0.3s ease';
                
                setTimeout(() => {
                    row.style.opacity = '1';
                    row.style.transform = 'translateY(0)';
                }, 50);
            }, index * 50);
        });
    });
</script>
{% endblock %}
