{% extends "base.html" %}

{% block title %}تفضيلات المستخدم - {{ user.full_name }} - نظام إدارة الأرشيف العام{% endblock %}

{% block extra_css %}
<style>
    body {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        font-family: 'Cairo', sans-serif;
    }

    .preferences-container {
        max-width: 900px;
        margin: 2rem auto;
        padding: 0 1rem;
    }

    .preferences-header {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        border-radius: 20px;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        text-align: center;
    }

    .preferences-header h1 {
        color: #2c3e50;
        font-size: 2rem;
        font-weight: 700;
        margin-bottom: 0.5rem;
    }

    .preferences-header p {
        color: #6c757d;
        font-size: 1rem;
        margin: 0;
    }

    .preferences-form {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        border-radius: 20px;
        padding: 2rem;
        box-shadow: 0 20px 40px rgba(0,0,0,0.1);
    }

    .preference-section {
        margin-bottom: 2rem;
        padding-bottom: 2rem;
        border-bottom: 2px solid #f8f9fa;
    }

    .preference-section:last-child {
        border-bottom: none;
        margin-bottom: 0;
    }

    .section-header {
        display: flex;
        align-items: center;
        gap: 1rem;
        margin-bottom: 1.5rem;
    }

    .section-icon {
        width: 45px;
        height: 45px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1.2rem;
    }

    .section-title {
        color: #2c3e50;
        font-size: 1.3rem;
        font-weight: 600;
        margin: 0;
    }

    .icon-appearance { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
    .icon-notifications { background: linear-gradient(135deg, #28a745 0%, #20c997 100%); }
    .icon-dashboard { background: linear-gradient(135deg, #17a2b8 0%, #6f42c1 100%); }
    .icon-language { background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%); }

    .preference-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 1.5rem;
    }

    .preference-item {
        background: #f8f9fa;
        border-radius: 12px;
        padding: 1.5rem;
        transition: all 0.3s ease;
        border: 2px solid transparent;
    }

    .preference-item:hover {
        background: #e9ecef;
        border-color: #667eea;
        transform: translateY(-2px);
    }

    .preference-label {
        font-weight: 600;
        color: #2c3e50;
        margin-bottom: 0.5rem;
        display: block;
        font-size: 1rem;
    }

    .preference-description {
        color: #6c757d;
        font-size: 0.85rem;
        margin-bottom: 1rem;
        line-height: 1.4;
    }

    .form-control, .form-select {
        width: 100%;
        padding: 0.75rem;
        border: 2px solid #e9ecef;
        border-radius: 8px;
        font-size: 0.9rem;
        transition: all 0.3s ease;
        background: white;
    }

    .form-control:focus, .form-select:focus {
        outline: none;
        border-color: #667eea;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    }

    .switch-container {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-top: 0.5rem;
    }

    .switch {
        position: relative;
        display: inline-block;
        width: 60px;
        height: 34px;
    }

    .switch input {
        opacity: 0;
        width: 0;
        height: 0;
    }

    .slider {
        position: absolute;
        cursor: pointer;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: #ccc;
        transition: .4s;
        border-radius: 34px;
    }

    .slider:before {
        position: absolute;
        content: "";
        height: 26px;
        width: 26px;
        left: 4px;
        bottom: 4px;
        background-color: white;
        transition: .4s;
        border-radius: 50%;
    }

    input:checked + .slider {
        background-color: #667eea;
    }

    input:checked + .slider:before {
        transform: translateX(26px);
    }

    .switch-label {
        font-weight: 500;
        color: #2c3e50;
    }

    .theme-options {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
        gap: 1rem;
        margin-top: 0.5rem;
    }

    .theme-option {
        text-align: center;
        padding: 1rem;
        border: 2px solid #e9ecef;
        border-radius: 8px;
        cursor: pointer;
        transition: all 0.3s ease;
        background: white;
    }

    .theme-option:hover {
        border-color: #667eea;
        transform: translateY(-2px);
    }

    .theme-option.selected {
        border-color: #667eea;
        background: rgba(102, 126, 234, 0.1);
    }

    .theme-preview {
        width: 40px;
        height: 30px;
        border-radius: 4px;
        margin: 0 auto 0.5rem;
    }

    .theme-light { background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%); }
    .theme-dark { background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%); }
    .theme-blue { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }

    .theme-name {
        font-size: 0.8rem;
        font-weight: 500;
        color: #2c3e50;
    }

    .form-actions {
        display: flex;
        justify-content: space-between;
        align-items: center;
        gap: 1rem;
        margin-top: 2rem;
        padding-top: 2rem;
        border-top: 2px solid #f8f9fa;
        flex-wrap: wrap;
    }

    .action-btn {
        padding: 0.75rem 2rem;
        border: none;
        border-radius: 10px;
        font-weight: 600;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        transition: all 0.3s ease;
        cursor: pointer;
        font-size: 0.9rem;
    }

    .btn-primary {
        background: #667eea;
        color: white;
    }

    .btn-secondary {
        background: #6c757d;
        color: white;
    }

    .btn-info {
        background: #17a2b8;
        color: white;
    }

    .action-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        color: white;
        text-decoration: none;
    }

    .action-btn:disabled {
        opacity: 0.6;
        cursor: not-allowed;
        transform: none;
    }

    @media (max-width: 768px) {
        .preferences-container {
            margin: 1rem;
            padding: 0;
        }

        .preference-grid {
            grid-template-columns: 1fr;
        }

        .theme-options {
            grid-template-columns: repeat(2, 1fr);
        }

        .form-actions {
            flex-direction: column;
        }

        .action-btn {
            width: 100%;
            justify-content: center;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="preferences-container">
    <!-- Header -->
    <div class="preferences-header">
        <h1>
            <i class="fas fa-cog me-2"></i>
            تفضيلات المستخدم
        </h1>
        <p>تخصيص إعدادات النظام حسب تفضيلاتك الشخصية</p>
    </div>

    <!-- Preferences Form -->
    <div class="preferences-form">
        <form method="POST" id="preferencesForm">
            <!-- Appearance Section -->
            <div class="preference-section">
                <div class="section-header">
                    <div class="section-icon icon-appearance">
                        <i class="fas fa-palette"></i>
                    </div>
                    <h3 class="section-title">المظهر والواجهة</h3>
                </div>

                <div class="preference-grid">
                    <div class="preference-item">
                        <label class="preference-label">المظهر العام</label>
                        <div class="preference-description">اختر المظهر المفضل لديك للنظام</div>
                        
                        <div class="theme-options">
                            <div class="theme-option {{ 'selected' if preferences.theme == 'light' else '' }}" 
                                 onclick="selectTheme('light')">
                                <div class="theme-preview theme-light"></div>
                                <div class="theme-name">فاتح</div>
                                <input type="radio" name="theme" value="light" 
                                       {{ 'checked' if preferences.theme == 'light' else '' }} style="display: none;">
                            </div>
                            
                            <div class="theme-option {{ 'selected' if preferences.theme == 'dark' else '' }}" 
                                 onclick="selectTheme('dark')">
                                <div class="theme-preview theme-dark"></div>
                                <div class="theme-name">داكن</div>
                                <input type="radio" name="theme" value="dark" 
                                       {{ 'checked' if preferences.theme == 'dark' else '' }} style="display: none;">
                            </div>
                            
                            <div class="theme-option {{ 'selected' if preferences.theme == 'blue' else '' }}" 
                                 onclick="selectTheme('blue')">
                                <div class="theme-preview theme-blue"></div>
                                <div class="theme-name">أزرق</div>
                                <input type="radio" name="theme" value="blue" 
                                       {{ 'checked' if preferences.theme == 'blue' else '' }} style="display: none;">
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Language Section -->
            <div class="preference-section">
                <div class="section-header">
                    <div class="section-icon icon-language">
                        <i class="fas fa-language"></i>
                    </div>
                    <h3 class="section-title">اللغة والمنطقة</h3>
                </div>

                <div class="preference-grid">
                    <div class="preference-item">
                        <label class="preference-label" for="language">لغة الواجهة</label>
                        <div class="preference-description">اختر اللغة المفضلة لعرض واجهة النظام</div>
                        <select class="form-select" id="language" name="language">
                            <option value="ar" {{ 'selected' if preferences.language == 'ar' else '' }}>العربية</option>
                            <option value="en" {{ 'selected' if preferences.language == 'en' else '' }}>English</option>
                        </select>
                    </div>
                </div>
            </div>

            <!-- Notifications Section -->
            <div class="preference-section">
                <div class="section-header">
                    <div class="section-icon icon-notifications">
                        <i class="fas fa-bell"></i>
                    </div>
                    <h3 class="section-title">الإشعارات</h3>
                </div>

                <div class="preference-grid">
                    <div class="preference-item">
                        <label class="preference-label">إشعارات المتصفح</label>
                        <div class="preference-description">تلقي إشعارات فورية في المتصفح عند حدوث أنشطة مهمة</div>
                        <div class="switch-container">
                            <span class="switch-label">تفعيل الإشعارات</span>
                            <label class="switch">
                                <input type="checkbox" name="notifications_enabled" 
                                       {{ 'checked' if preferences.notifications_enabled else '' }}>
                                <span class="slider"></span>
                            </label>
                        </div>
                    </div>

                    <div class="preference-item">
                        <label class="preference-label">إشعارات البريد الإلكتروني</label>
                        <div class="preference-description">تلقي إشعارات عبر البريد الإلكتروني للأحداث المهمة</div>
                        <div class="switch-container">
                            <span class="switch-label">تفعيل البريد الإلكتروني</span>
                            <label class="switch">
                                <input type="checkbox" name="email_notifications" 
                                       {{ 'checked' if preferences.email_notifications else '' }}>
                                <span class="slider"></span>
                            </label>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Dashboard Section -->
            <div class="preference-section">
                <div class="section-header">
                    <div class="section-icon icon-dashboard">
                        <i class="fas fa-tachometer-alt"></i>
                    </div>
                    <h3 class="section-title">لوحة التحكم</h3>
                </div>

                <div class="preference-grid">
                    <div class="preference-item">
                        <label class="preference-label" for="dashboard_layout">تخطيط لوحة التحكم</label>
                        <div class="preference-description">اختر التخطيط المفضل لعرض لوحة التحكم الرئيسية</div>
                        <select class="form-select" id="dashboard_layout" name="dashboard_layout">
                            <option value="default" {{ 'selected' if preferences.dashboard_layout == 'default' else '' }}>افتراضي</option>
                            <option value="compact" {{ 'selected' if preferences.dashboard_layout == 'compact' else '' }}>مضغوط</option>
                            <option value="detailed" {{ 'selected' if preferences.dashboard_layout == 'detailed' else '' }}>مفصل</option>
                            <option value="cards" {{ 'selected' if preferences.dashboard_layout == 'cards' else '' }}>بطاقات</option>
                        </select>
                    </div>
                </div>
            </div>

            <!-- Form Actions -->
            <div class="form-actions">
                <div>
                    <button type="submit" class="action-btn btn-primary" id="saveBtn">
                        <i class="fas fa-save"></i>
                        حفظ التفضيلات
                    </button>
                    <button type="button" class="action-btn btn-info" onclick="resetToDefaults()">
                        <i class="fas fa-undo"></i>
                        إعادة تعيين
                    </button>
                </div>
                <div>
                    <a href="{{ url_for('profile') }}" class="action-btn btn-secondary">
                        <i class="fas fa-arrow-right"></i>
                        العودة للملف الشخصي
                    </a>
                </div>
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 PREFERENCES: Preferences page loaded');
    
    // Form submission
    const form = document.getElementById('preferencesForm');
    const saveBtn = document.getElementById('saveBtn');
    
    form.addEventListener('submit', function(e) {
        saveBtn.disabled = true;
        saveBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الحفظ...';
    });
    
    console.log('✅ PREFERENCES: Form handlers initialized');
});

function selectTheme(theme) {
    // Remove selected class from all theme options
    document.querySelectorAll('.theme-option').forEach(option => {
        option.classList.remove('selected');
    });
    
    // Add selected class to clicked option
    event.currentTarget.classList.add('selected');
    
    // Check the corresponding radio button
    document.querySelector(`input[name="theme"][value="${theme}"]`).checked = true;
    
    console.log(`🎨 PREFERENCES: Theme selected: ${theme}`);
}

function resetToDefaults() {
    if (confirm('هل أنت متأكد من إعادة تعيين جميع التفضيلات إلى القيم الافتراضية؟')) {
        // Reset theme
        selectTheme('light');
        
        // Reset language
        document.getElementById('language').value = 'ar';
        
        // Reset notifications
        document.querySelector('input[name="notifications_enabled"]').checked = true;
        document.querySelector('input[name="email_notifications"]').checked = true;
        
        // Reset dashboard layout
        document.getElementById('dashboard_layout').value = 'default';
        
        console.log('🔄 PREFERENCES: Reset to defaults');
        alert('تم إعادة تعيين التفضيلات إلى القيم الافتراضية');
    }
}
</script>
{% endblock %}
