{% extends "base.html" %}

{% block title %}إضافة وثيقة جديدة - نظام إدارة الأرشيف العام{% endblock %}

{% block extra_css %}
<style>
    body {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        font-family: 'Cairo', sans-serif;
    }

    .add-document-container {
        max-width: 800px;
        margin: 2rem auto;
        padding: 0 1rem;
    }

    .document-card {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        border-radius: 20px;
        box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        overflow: hidden;
        animation: slideUp 0.6s ease-out;
    }

    @keyframes slideUp {
        from { opacity: 0; transform: translateY(30px); }
        to { opacity: 1; transform: translateY(0); }
    }

    .card-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 2rem;
        text-align: center;
    }

    .card-header h1 {
        font-size: 2rem;
        font-weight: 700;
        margin: 0;
    }

    .card-body {
        padding: 2rem;
    }

    .form-group {
        margin-bottom: 1.5rem;
    }

    .form-label {
        font-weight: 600;
        color: #2c3e50;
        margin-bottom: 0.5rem;
        display: block;
    }

    .required {
        color: #e74c3c;
        margin-left: 0.25rem;
    }

    .form-control, .form-select {
        width: 100%;
        padding: 0.75rem 1rem;
        border: 2px solid #e9ecef;
        border-radius: 10px;
        font-size: 1rem;
        transition: all 0.3s ease;
        background: white;
    }

    .form-control:focus, .form-select:focus {
        outline: none;
        border-color: #667eea;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        transform: translateY(-2px);
    }

    .form-select {
        cursor: pointer;
        background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
        background-position: left 1rem center;
        background-repeat: no-repeat;
        background-size: 1.5em 1.5em;
        padding-left: 3rem;
    }

    .help-text {
        font-size: 0.875rem;
        color: #6b7280;
        margin-top: 0.25rem;
    }

    .file-upload {
        border: 2px dashed #d1d5db;
        border-radius: 10px;
        padding: 1.5rem;
        text-align: center;
        background: #f8fafc;
        transition: all 0.3s ease;
    }

    .file-upload:hover {
        border-color: #667eea;
        background: #f0f4ff;
    }

    .form-actions {
        display: flex;
        gap: 1rem;
        justify-content: space-between;
        margin-top: 2rem;
        padding-top: 1.5rem;
        border-top: 1px solid #e9ecef;
    }

    .btn-save {
        background: linear-gradient(135deg, #10b981 0%, #34d399 100%);
        border: none;
        color: white;
        padding: 0.75rem 2rem;
        border-radius: 10px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .btn-save:hover {
        transform: translateY(-2px);
        box-shadow: 0 10px 25px rgba(16, 185, 129, 0.3);
    }

    .btn-cancel {
        background: #6b7280;
        color: white;
        padding: 0.75rem 1.5rem;
        border-radius: 10px;
        text-decoration: none;
        display: flex;
        align-items: center;
        gap: 0.5rem;
        transition: all 0.3s ease;
    }

    .btn-cancel:hover {
        background: #4b5563;
        color: white;
        text-decoration: none;
        transform: translateY(-2px);
    }

    .error-message {
        color: #ef4444;
        font-size: 0.875rem;
        margin-top: 0.25rem;
    }

    @media (max-width: 768px) {
        .add-document-container {
            margin: 1rem;
            padding: 0;
        }
        .card-body {
            padding: 1.5rem;
        }
        .form-actions {
            flex-direction: column;
        }
        .btn-save, .btn-cancel {
            width: 100%;
            justify-content: center;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="add-document-container">
    <div class="document-card">
        <div class="card-header">
            <h1>
                <i class="fas fa-file-plus me-2"></i>
                إضافة وثيقة جديدة
            </h1>
            <p>أدخل بيانات الوثيقة الجديدة</p>
        </div>

        <div class="card-body">
            <form method="POST" enctype="multipart/form-data" id="documentForm">
                {{ form.hidden_tag() }}

                <!-- عنوان الوثيقة -->
                <div class="form-group">
                    <label for="title" class="form-label">
                        عنوان الوثيقة <span class="required">*</span>
                    </label>
                    {{ form.title(class="form-control", placeholder="أدخل عنوان الوثيقة", required=true) }}
                    {% if form.title.errors %}
                        {% for error in form.title.errors %}
                            <div class="error-message">{{ error }}</div>
                        {% endfor %}
                    {% endif %}
                    <div class="help-text">عنوان واضح ومختصر للوثيقة</div>
                </div>

                <!-- نوع الوثيقة -->
                <div class="form-group">
                    <label for="document_type" class="form-label">
                        نوع الوثيقة <span class="required">*</span>
                    </label>
                    {{ form.document_type(class="form-select", required=true) }}
                    {% if form.document_type.errors %}
                        {% for error in form.document_type.errors %}
                            <div class="error-message">{{ error }}</div>
                        {% endfor %}
                    {% endif %}
                    <div class="help-text">اختر نوع الوثيقة المناسب</div>
                </div>

                <!-- وصف الوثيقة -->
                <div class="form-group">
                    <label for="description" class="form-label">وصف الوثيقة</label>
                    {{ form.description(class="form-control", rows="3", placeholder="أدخل وصف الوثيقة (اختياري)") }}
                    {% if form.description.errors %}
                        {% for error in form.description.errors %}
                            <div class="error-message">{{ error }}</div>
                        {% endfor %}
                    {% endif %}
                    <div class="help-text">وصف تفصيلي لمحتوى الوثيقة</div>
                </div>

                <!-- الكلمات المفتاحية -->
                <div class="form-group">
                    <label for="tags" class="form-label">الكلمات المفتاحية</label>
                    {{ form.tags(class="form-control", placeholder="مثال: عقد، مالية، إدارة") }}
                    {% if form.tags.errors %}
                        {% for error in form.tags.errors %}
                            <div class="error-message">{{ error }}</div>
                        {% endfor %}
                    {% endif %}
                    <div class="help-text">كلمات مفتاحية لتسهيل البحث</div>
                </div>

                <!-- رفع الملف -->
                <div class="form-group">
                    <label for="file" class="form-label">الملف المرفق</label>
                    <div class="file-upload">
                        <i class="fas fa-cloud-upload-alt fa-2x mb-2" style="color: #667eea;"></i>
                        {{ form.file(class="form-control") }}
                        <p class="mb-0 mt-1">اختر ملف للرفع (PDF, DOC, DOCX, JPG, PNG)</p>
                        <small class="text-muted">الحد الأقصى: 50 ميجابايت</small>
                    </div>
                    {% if form.file.errors %}
                        {% for error in form.file.errors %}
                            <div class="error-message">{{ error }}</div>
                        {% endfor %}
                    {% endif %}
                </div>

                <!-- CRITICAL FIX: EXACTLY ONE SUBMIT BUTTON ONLY -->
                <div class="form-actions">
                    <a href="{{ url_for('documents') }}" class="btn-cancel">
                        <i class="fas fa-times"></i>
                        إلغاء
                    </a>

                    <!-- SINGLE SUBMIT BUTTON - NO OTHER BUTTONS -->
                    <button type="submit" class="btn-save" id="saveButton">
                        <i class="fas fa-save"></i>
                        حفظ الوثيقة
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 CRITICAL VERIFICATION: Document Add Page Loading...');

    // CRITICAL CHECK 1: Document Types Verification
    const docTypeSelect = document.querySelector('select[name="document_type"]');
    if (docTypeSelect) {
        const optionsCount = docTypeSelect.options.length - 1; // Exclude default option
        console.log('📊 CRITICAL CHECK: Document types available:', optionsCount);

        // Print all document types for verification
        console.log('📋 ALL DOCUMENT TYPES:');
        for (let i = 1; i < docTypeSelect.options.length; i++) {
            console.log('   ' + i + '. ' + docTypeSelect.options[i].text + ' (value: ' + docTypeSelect.options[i].value + ')');
        }

        // VERIFICATION RESULT
        if (optionsCount >= 24) {
            console.log('✅ CRITICAL SUCCESS: 24+ document types loaded correctly!');
            console.log('🎉 VERIFICATION PASSED: ' + optionsCount + ' document types available');
        } else {
            console.error('❌ CRITICAL FAILURE: Only ' + optionsCount + ' document types found, need 24+');
        }
    } else {
        console.error('❌ CRITICAL ERROR: Document type select element not found!');
    }

    // CRITICAL CHECK 2: Submit Buttons Verification
    const submitButtons = document.querySelectorAll('button[type="submit"]');
    const submitButtonsCount = submitButtons.length;

    console.log('📊 CRITICAL CHECK: Submit buttons count:', submitButtonsCount);

    if (submitButtonsCount === 1) {
        console.log('✅ CRITICAL SUCCESS: Exactly ONE submit button found!');
        console.log('🎉 VERIFICATION PASSED: Single button requirement met');
    } else {
        console.error('❌ CRITICAL FAILURE: Found ' + submitButtonsCount + ' submit buttons, need exactly 1');
        submitButtons.forEach((btn, index) => {
            console.error('   Button ' + (index + 1) + ': ' + btn.textContent.trim());
        });
    }

    // Form submission handling
    const form = document.getElementById('documentForm');
    const saveButton = document.getElementById('saveButton');

    if (form && saveButton) {
        form.addEventListener('submit', function(e) {
            console.log('📤 FORM SUBMISSION: Validating...');

            const title = document.querySelector('input[name="title"]').value.trim();
            const docType = docTypeSelect ? docTypeSelect.value : '';

            if (!title) {
                e.preventDefault();
                alert('❌ يرجى إدخال عنوان الوثيقة');
                return false;
            }

            if (!docType) {
                e.preventDefault();
                alert('❌ يرجى اختيار نوع الوثيقة');
                return false;
            }

            // Disable button and show loading
            saveButton.disabled = true;
            saveButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الحفظ...';

            console.log('✅ FORM SUBMISSION: Validation passed, submitting...');
        });
    }

    // FINAL VERIFICATION SUMMARY
    console.log('');
    console.log('🎯 CRITICAL VERIFICATION SUMMARY:');
    console.log('================================');
    console.log('📊 Document Types: ' + (docTypeSelect ? docTypeSelect.options.length - 1 : 0));
    console.log('📊 Submit Buttons: ' + submitButtonsCount);
    console.log('📊 Form Present: ' + (form ? 'YES' : 'NO'));

    const allChecksPass = (
        docTypeSelect &&
        docTypeSelect.options.length >= 25 && // 24 + default option
        submitButtonsCount === 1 &&
        form
    );

    if (allChecksPass) {
        console.log('🎉 ALL CRITICAL CHECKS PASSED!');
        console.log('✅ Page is ready and compliant with requirements');
    } else {
        console.error('❌ CRITICAL CHECKS FAILED!');
        console.error('⚠️ Page does not meet requirements');
    }

    console.log('================================');
});
</script>
{% endblock %}
