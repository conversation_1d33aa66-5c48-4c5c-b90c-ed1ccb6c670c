<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة التحكم - نظام إدارة الأرشيف العام</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
            direction: rtl;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1rem 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .header h1 {
            font-size: 1.5rem;
        }
        
        .user-info {
            display: flex;
            align-items: center;
            gap: 1rem;
        }
        
        .logout-btn {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 5px;
            text-decoration: none;
            transition: background 0.3s;
        }
        
        .logout-btn:hover {
            background: rgba(255, 255, 255, 0.3);
        }
        
        .container {
            max-width: 1200px;
            margin: 2rem auto;
            padding: 0 1rem;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .stat-card {
            background: white;
            padding: 1.5rem;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            text-align: center;
        }
        
        .stat-card h3 {
            color: #333;
            margin-bottom: 0.5rem;
        }
        
        .stat-card .number {
            font-size: 2rem;
            font-weight: bold;
            color: #667eea;
        }
        
        .content-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 1.5rem;
        }
        
        .content-card {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        
        .content-card h3 {
            background: #f8f9fa;
            padding: 1rem;
            margin: 0;
            color: #333;
            border-bottom: 1px solid #e9ecef;
        }
        
        .content-card .content {
            padding: 1rem;
        }
        
        .item-list {
            list-style: none;
        }
        
        .item-list li {
            padding: 0.5rem 0;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .item-list li:last-child {
            border-bottom: none;
        }
        
        .item-title {
            font-weight: 500;
            color: #333;
        }
        
        .item-meta {
            font-size: 0.85rem;
            color: #666;
            margin-top: 0.25rem;
        }
        
        .alert {
            padding: 0.75rem;
            margin-bottom: 1rem;
            border-radius: 5px;
            text-align: center;
        }
        
        .alert-success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .welcome-message {
            background: white;
            padding: 1.5rem;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            margin-bottom: 2rem;
            text-align: center;
        }
        
        .welcome-message h2 {
            color: #333;
            margin-bottom: 0.5rem;
        }
        
        .welcome-message p {
            color: #666;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>نظام إدارة الأرشيف العام</h1>
        <div class="user-info">
            <span>مرحباً، {{ current_user.full_name }}</span>
            <a href="{{ url_for('logout') }}" class="logout-btn">تسجيل الخروج</a>
        </div>
    </div>
    
    <div class="container">
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ category }}">
                        {{ message }}
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}
        
        <div class="welcome-message">
            <h2>مرحباً بك في نظام إدارة الأرشيف العام</h2>
            <p>النسخة المبسطة - يمكنك الآن إدارة الوثائق والمراسلات بسهولة</p>
        </div>
        
        <div class="stats-grid">
            <div class="stat-card">
                <h3>إجمالي الوثائق</h3>
                <div class="number">{{ stats.total_documents }}</div>
            </div>
            <div class="stat-card">
                <h3>الكتب الواردة</h3>
                <div class="number">{{ stats.total_incoming }}</div>
            </div>
            <div class="stat-card">
                <h3>الكتب الصادرة</h3>
                <div class="number">{{ stats.total_outgoing }}</div>
            </div>
            <div class="stat-card">
                <h3>المستخدمين</h3>
                <div class="number">{{ stats.total_users }}</div>
            </div>
        </div>
        
        <div class="content-grid">
            <div class="content-card">
                <h3>أحدث الوثائق</h3>
                <div class="content">
                    {% if recent_documents %}
                        <ul class="item-list">
                            {% for doc in recent_documents %}
                                <li>
                                    <div class="item-title">{{ doc.title }}</div>
                                    <div class="item-meta">{{ doc.created_at.strftime('%Y/%m/%d') }} - {{ doc.creator.full_name if doc.creator else 'غير محدد' }}</div>
                                </li>
                            {% endfor %}
                        </ul>
                    {% else %}
                        <p>لا توجد وثائق حتى الآن</p>
                    {% endif %}
                </div>
            </div>
            
            <div class="content-card">
                <h3>أحدث الكتب الواردة</h3>
                <div class="content">
                    {% if recent_incoming %}
                        <ul class="item-list">
                            {% for doc in recent_incoming %}
                                <li>
                                    <div class="item-title">{{ doc.subject }}</div>
                                    <div class="item-meta">من: {{ doc.sender_name }} - {{ doc.received_date.strftime('%Y/%m/%d') }}</div>
                                </li>
                            {% endfor %}
                        </ul>
                    {% else %}
                        <p>لا توجد كتب واردة حتى الآن</p>
                    {% endif %}
                </div>
            </div>
            
            <div class="content-card">
                <h3>أحدث الكتب الصادرة</h3>
                <div class="content">
                    {% if recent_outgoing %}
                        <ul class="item-list">
                            {% for doc in recent_outgoing %}
                                <li>
                                    <div class="item-title">{{ doc.subject }}</div>
                                    <div class="item-meta">إلى: {{ doc.recipient_name }} - {{ doc.sent_date.strftime('%Y/%m/%d') }}</div>
                                </li>
                            {% endfor %}
                        </ul>
                    {% else %}
                        <p>لا توجد كتب صادرة حتى الآن</p>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</body>
</html>
