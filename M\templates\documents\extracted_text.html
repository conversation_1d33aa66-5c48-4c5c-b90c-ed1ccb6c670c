{% extends "base.html" %}

{% block title %}النص المستخرج: {{ document.title }} - نظام إدارة الأرشيف العام{% endblock %}

{% block extra_css %}
<style>
    .extracted-text-header {
        background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
        color: white;
        border-radius: var(--border-radius);
        padding: 2rem;
        margin-bottom: 2rem;
        text-align: center;
    }
    
    .extracted-text-title {
        font-size: 2rem;
        font-weight: 700;
        margin: 0;
    }
    
    .extracted-text-subtitle {
        opacity: 0.9;
        margin: 0.5rem 0 0 0;
    }
    
    .document-info {
        background: white;
        border-radius: var(--border-radius);
        padding: 1.5rem;
        margin-bottom: 2rem;
        box-shadow: var(--shadow-light);
        border-left: 4px solid #e74c3c;
    }
    
    .text-container {
        background: white;
        border-radius: var(--border-radius);
        padding: 2rem;
        box-shadow: var(--shadow-light);
        margin-bottom: 2rem;
    }
    
    .text-content {
        background: #f8f9fa;
        border: 1px solid #e9ecef;
        border-radius: var(--border-radius);
        padding: 2rem;
        font-family: 'Courier New', monospace;
        line-height: 1.8;
        white-space: pre-wrap;
        word-wrap: break-word;
        max-height: 600px;
        overflow-y: auto;
        direction: rtl;
        text-align: right;
    }
    
    .text-stats {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 1rem;
        margin-bottom: 2rem;
    }
    
    .stat-item {
        background: white;
        border-radius: var(--border-radius);
        padding: 1rem;
        text-align: center;
        box-shadow: var(--shadow-light);
        border-left: 4px solid #e74c3c;
    }
    
    .stat-number {
        font-size: 1.5rem;
        font-weight: 700;
        color: #e74c3c;
        margin-bottom: 0.5rem;
    }
    
    .stat-label {
        color: #7f8c8d;
        font-weight: 600;
        font-size: 0.875rem;
    }
    
    .action-buttons {
        display: flex;
        gap: 1rem;
        flex-wrap: wrap;
        margin-bottom: 2rem;
    }
    
    .btn-action {
        padding: 0.75rem 1.5rem;
        border: none;
        border-radius: var(--border-radius);
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
    }
    
    .btn-copy {
        background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
        color: white;
    }
    
    .btn-copy:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(52, 152, 219, 0.3);
        color: white;
    }
    
    .btn-download {
        background: linear-gradient(135deg, #2ecc71 0%, #27ae60 100%);
        color: white;
    }
    
    .btn-download:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(46, 204, 113, 0.3);
        color: white;
    }
    
    .btn-back {
        background: linear-gradient(135deg, #95a5a6 0%, #7f8c8d 100%);
        color: white;
    }
    
    .btn-back:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(149, 165, 166, 0.3);
        color: white;
    }
    
    .search-box {
        background: white;
        border-radius: var(--border-radius);
        padding: 1.5rem;
        margin-bottom: 2rem;
        box-shadow: var(--shadow-light);
    }
    
    .search-input {
        width: 100%;
        padding: 0.75rem;
        border: 2px solid #e9ecef;
        border-radius: var(--border-radius);
        font-size: 1rem;
        transition: all 0.3s ease;
    }
    
    .search-input:focus {
        outline: none;
        border-color: #e74c3c;
        box-shadow: 0 0 0 3px rgba(231, 76, 60, 0.1);
    }
    
    .highlight {
        background-color: #fff3cd;
        padding: 2px 4px;
        border-radius: 3px;
        font-weight: 600;
    }
    
    .copy-notification {
        position: fixed;
        top: 20px;
        right: 20px;
        background: #2ecc71;
        color: white;
        padding: 1rem 1.5rem;
        border-radius: var(--border-radius);
        box-shadow: var(--shadow-medium);
        transform: translateX(100%);
        transition: transform 0.3s ease;
        z-index: 1000;
    }
    
    .copy-notification.show {
        transform: translateX(0);
    }
    
    @media (max-width: 768px) {
        .action-buttons {
            flex-direction: column;
        }
        
        .text-stats {
            grid-template-columns: repeat(2, 1fr);
        }
        
        .text-container {
            padding: 1rem;
        }
        
        .text-content {
            padding: 1rem;
            font-size: 0.875rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<!-- Header -->
<div class="extracted-text-header">
    <h1 class="extracted-text-title">
        <i class="fas fa-eye me-3"></i>
        النص المستخرج
    </h1>
    <p class="extracted-text-subtitle">النص المستخرج من الوثيقة باستخدام تقنية OCR</p>
</div>

<!-- Document Info -->
<div class="document-info">
    <h5><i class="fas fa-file-alt me-2"></i>معلومات الوثيقة</h5>
    <div class="row">
        <div class="col-md-6">
            <strong>العنوان:</strong> {{ document.title }}
        </div>
        <div class="col-md-6">
            <strong>النوع:</strong> {{ document.document_type }}
        </div>
        <div class="col-md-6">
            <strong>تاريخ الإنشاء:</strong> {{ document.created_at.strftime('%Y/%m/%d %H:%M') }}
        </div>
        <div class="col-md-6">
            <strong>المنشئ:</strong> {{ document.creator.full_name or document.creator.username }}
        </div>
    </div>
</div>

<!-- Text Statistics -->
<div class="text-stats">
    <div class="stat-item">
        <div class="stat-number" id="charCount">{{ document.extracted_text|length }}</div>
        <div class="stat-label">عدد الأحرف</div>
    </div>
    
    <div class="stat-item">
        <div class="stat-number" id="wordCount">{{ document.extracted_text.split()|length }}</div>
        <div class="stat-label">عدد الكلمات</div>
    </div>
    
    <div class="stat-item">
        <div class="stat-number" id="lineCount">{{ document.extracted_text.split('\n')|length }}</div>
        <div class="stat-label">عدد الأسطر</div>
    </div>
    
    <div class="stat-item">
        <div class="stat-number" id="paragraphCount">{{ document.extracted_text.split('\n\n')|length }}</div>
        <div class="stat-label">عدد الفقرات</div>
    </div>
</div>

<!-- Search Box -->
<div class="search-box">
    <h6><i class="fas fa-search me-2"></i>البحث في النص</h6>
    <input type="text" class="search-input" id="searchInput" placeholder="ابحث عن كلمة أو عبارة في النص...">
</div>

<!-- Action Buttons -->
<div class="action-buttons">
    <button class="btn-action btn-copy" onclick="copyText()">
        <i class="fas fa-copy"></i>
        نسخ النص
    </button>
    
    <button class="btn-action btn-download" onclick="downloadText()">
        <i class="fas fa-download"></i>
        تحميل كملف نصي
    </button>
    
    <a href="{{ url_for('view_document', doc_id=document.id) }}" class="btn-action btn-back">
        <i class="fas fa-arrow-right"></i>
        العودة للوثيقة
    </a>
</div>

<!-- Text Content -->
<div class="text-container">
    <h5><i class="fas fa-file-text me-2"></i>النص المستخرج</h5>
    <div class="text-content" id="textContent">{{ document.extracted_text }}</div>
</div>

<!-- Copy Notification -->
<div class="copy-notification" id="copyNotification">
    <i class="fas fa-check me-2"></i>
    تم نسخ النص بنجاح!
</div>
{% endblock %}

{% block extra_js %}
<script>
    function copyText() {
        const textContent = document.getElementById('textContent').textContent;
        
        navigator.clipboard.writeText(textContent).then(function() {
            showCopyNotification();
        }).catch(function(err) {
            // Fallback for older browsers
            const textArea = document.createElement('textarea');
            textArea.value = textContent;
            document.body.appendChild(textArea);
            textArea.select();
            document.execCommand('copy');
            document.body.removeChild(textArea);
            showCopyNotification();
        });
    }
    
    function showCopyNotification() {
        const notification = document.getElementById('copyNotification');
        notification.classList.add('show');
        
        setTimeout(() => {
            notification.classList.remove('show');
        }, 3000);
    }
    
    function downloadText() {
        const textContent = document.getElementById('textContent').textContent;
        const blob = new Blob([textContent], { type: 'text/plain;charset=utf-8' });
        const url = window.URL.createObjectURL(blob);
        
        const a = document.createElement('a');
        a.href = url;
        a.download = `extracted_text_{{ document.id }}.txt`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        window.URL.revokeObjectURL(url);
    }
    
    // Search functionality
    document.getElementById('searchInput').addEventListener('input', function() {
        const searchTerm = this.value.trim();
        const textContent = document.getElementById('textContent');
        const originalText = '{{ document.extracted_text|safe }}';
        
        if (searchTerm === '') {
            textContent.innerHTML = originalText;
            return;
        }
        
        // Highlight search terms
        const regex = new RegExp(`(${searchTerm})`, 'gi');
        const highlightedText = originalText.replace(regex, '<span class="highlight">$1</span>');
        textContent.innerHTML = highlightedText;
        
        // Scroll to first match
        const firstMatch = textContent.querySelector('.highlight');
        if (firstMatch) {
            firstMatch.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }
    });
    
    // Animation on load
    document.addEventListener('DOMContentLoaded', function() {
        const elements = document.querySelectorAll('.stat-item, .text-container, .search-box');
        elements.forEach((element, index) => {
            setTimeout(() => {
                element.style.opacity = '0';
                element.style.transform = 'translateY(20px)';
                element.style.transition = 'all 0.6s ease';
                
                setTimeout(() => {
                    element.style.opacity = '1';
                    element.style.transform = 'translateY(0)';
                }, 50);
            }, index * 100);
        });
    });
</script>
{% endblock %}
