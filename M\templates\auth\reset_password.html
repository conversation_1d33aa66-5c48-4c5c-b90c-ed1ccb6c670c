{% extends "base.html" %}

{% block title %}إعادة تعيين كلمة المرور - نظام إدارة الأرشيف العام{% endblock %}

{% block extra_css %}
<style>
    .auth-container {
        min-height: 100vh;
        display: flex;
        align-items: center;
        justify-content: center;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        padding: 2rem 0;
    }
    
    .auth-card {
        background: white;
        border-radius: 20px;
        box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        padding: 3rem;
        width: 100%;
        max-width: 450px;
        position: relative;
        overflow: hidden;
    }
    
    .auth-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 5px;
        background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
    }
    
    .auth-header {
        text-align: center;
        margin-bottom: 2rem;
    }
    
    .auth-logo {
        width: 80px;
        height: 80px;
        background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 1rem;
        color: white;
        font-size: 2rem;
    }
    
    .auth-title {
        color: #2c3e50;
        font-size: 1.8rem;
        font-weight: 700;
        margin-bottom: 0.5rem;
    }
    
    .auth-subtitle {
        color: #7f8c8d;
        font-size: 1rem;
        margin: 0;
    }
    
    .form-group {
        margin-bottom: 1.5rem;
    }
    
    .form-label {
        display: block;
        margin-bottom: 0.5rem;
        color: #2c3e50;
        font-weight: 600;
    }
    
    .form-control {
        width: 100%;
        padding: 1rem;
        border: 2px solid #e9ecef;
        border-radius: 10px;
        font-size: 1rem;
        transition: all 0.3s ease;
        background: #f8f9fa;
    }
    
    .form-control:focus {
        outline: none;
        border-color: #27ae60;
        background: white;
        box-shadow: 0 0 0 3px rgba(39, 174, 96, 0.1);
    }
    
    .btn-primary {
        width: 100%;
        padding: 1rem;
        background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
        border: none;
        border-radius: 10px;
        color: white;
        font-size: 1.1rem;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        margin-bottom: 1rem;
    }
    
    .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 10px 20px rgba(39, 174, 96, 0.3);
    }
    
    .auth-links {
        text-align: center;
        margin-top: 2rem;
    }
    
    .auth-link {
        color: #27ae60;
        text-decoration: none;
        font-weight: 600;
        transition: color 0.3s ease;
    }
    
    .auth-link:hover {
        color: #2ecc71;
    }
    
    .invalid-feedback {
        color: #e74c3c;
        font-size: 0.875rem;
        margin-top: 0.25rem;
        display: block;
    }
    
    .password-strength {
        margin-top: 0.5rem;
        font-size: 0.875rem;
    }
    
    .strength-weak {
        color: #e74c3c;
    }
    
    .strength-medium {
        color: #f39c12;
    }
    
    .strength-strong {
        color: #27ae60;
    }
    
    .password-requirements {
        background: #f8f9fa;
        border-radius: 10px;
        padding: 1rem;
        margin-bottom: 1.5rem;
        font-size: 0.875rem;
    }
    
    .requirement {
        display: flex;
        align-items: center;
        margin-bottom: 0.5rem;
    }
    
    .requirement:last-child {
        margin-bottom: 0;
    }
    
    .requirement i {
        margin-left: 0.5rem;
        width: 16px;
    }
    
    .requirement.valid {
        color: #27ae60;
    }
    
    .requirement.invalid {
        color: #e74c3c;
    }
    
    @media (max-width: 768px) {
        .auth-card {
            margin: 1rem;
            padding: 2rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="auth-container">
    <div class="auth-card">
        <div class="auth-header">
            <div class="auth-logo">
                <i class="fas fa-lock"></i>
            </div>
            <h1 class="auth-title">إعادة تعيين كلمة المرور</h1>
            <p class="auth-subtitle">أدخل كلمة المرور الجديدة لحسابك</p>
        </div>
        
        <div class="password-requirements">
            <strong>متطلبات كلمة المرور:</strong>
            <div class="requirement" id="length-req">
                <i class="fas fa-times"></i>
                على الأقل 6 أحرف
            </div>
            <div class="requirement" id="match-req">
                <i class="fas fa-times"></i>
                تطابق كلمتي المرور
            </div>
        </div>
        
        <form method="POST" id="resetForm">
            {{ form.hidden_tag() }}
            
            <div class="form-group">
                {{ form.password.label(class="form-label") }}
                {{ form.password(class="form-control", placeholder="أدخل كلمة المرور الجديدة", id="password") }}
                {% if form.password.errors %}
                    {% for error in form.password.errors %}
                        <div class="invalid-feedback">{{ error }}</div>
                    {% endfor %}
                {% endif %}
                <div class="password-strength" id="passwordStrength"></div>
            </div>
            
            <div class="form-group">
                {{ form.confirm_password.label(class="form-label") }}
                {{ form.confirm_password(class="form-control", placeholder="أعد إدخال كلمة المرور", id="confirmPassword") }}
                {% if form.confirm_password.errors %}
                    {% for error in form.confirm_password.errors %}
                        <div class="invalid-feedback">{{ error }}</div>
                    {% endfor %}
                {% endif %}
            </div>
            
            {{ form.submit(class="btn btn-primary", id="submitBtn", disabled=true) }}
        </form>
        
        <div class="auth-links">
            <a href="{{ url_for('login') }}" class="auth-link">
                <i class="fas fa-arrow-right me-2"></i>
                العودة لتسجيل الدخول
            </a>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const passwordInput = document.getElementById('password');
        const confirmPasswordInput = document.getElementById('confirmPassword');
        const submitBtn = document.getElementById('submitBtn');
        const lengthReq = document.getElementById('length-req');
        const matchReq = document.getElementById('match-req');
        const strengthDiv = document.getElementById('passwordStrength');
        
        function checkPasswordStrength(password) {
            let strength = 0;
            let feedback = '';
            
            if (password.length >= 6) strength += 1;
            if (password.match(/[a-z]/)) strength += 1;
            if (password.match(/[A-Z]/)) strength += 1;
            if (password.match(/[0-9]/)) strength += 1;
            if (password.match(/[^a-zA-Z0-9]/)) strength += 1;
            
            switch (strength) {
                case 0:
                case 1:
                    feedback = '<span class="strength-weak">ضعيفة جداً</span>';
                    break;
                case 2:
                    feedback = '<span class="strength-weak">ضعيفة</span>';
                    break;
                case 3:
                    feedback = '<span class="strength-medium">متوسطة</span>';
                    break;
                case 4:
                    feedback = '<span class="strength-strong">قوية</span>';
                    break;
                case 5:
                    feedback = '<span class="strength-strong">قوية جداً</span>';
                    break;
            }
            
            return feedback;
        }
        
        function validateForm() {
            const password = passwordInput.value;
            const confirmPassword = confirmPasswordInput.value;
            
            // التحقق من طول كلمة المرور
            if (password.length >= 6) {
                lengthReq.classList.add('valid');
                lengthReq.classList.remove('invalid');
                lengthReq.querySelector('i').className = 'fas fa-check';
            } else {
                lengthReq.classList.add('invalid');
                lengthReq.classList.remove('valid');
                lengthReq.querySelector('i').className = 'fas fa-times';
            }
            
            // التحقق من تطابق كلمتي المرور
            if (password && confirmPassword && password === confirmPassword) {
                matchReq.classList.add('valid');
                matchReq.classList.remove('invalid');
                matchReq.querySelector('i').className = 'fas fa-check';
            } else {
                matchReq.classList.add('invalid');
                matchReq.classList.remove('valid');
                matchReq.querySelector('i').className = 'fas fa-times';
            }
            
            // تفعيل/تعطيل زر الإرسال
            const isValid = password.length >= 6 && password === confirmPassword;
            submitBtn.disabled = !isValid;
            
            if (isValid) {
                submitBtn.style.opacity = '1';
                submitBtn.style.cursor = 'pointer';
            } else {
                submitBtn.style.opacity = '0.6';
                submitBtn.style.cursor = 'not-allowed';
            }
        }
        
        passwordInput.addEventListener('input', function() {
            strengthDiv.innerHTML = checkPasswordStrength(this.value);
            validateForm();
        });
        
        confirmPasswordInput.addEventListener('input', validateForm);
        
        // تأثير الرسوم المتحركة
        const authCard = document.querySelector('.auth-card');
        authCard.style.opacity = '0';
        authCard.style.transform = 'translateY(20px)';
        
        setTimeout(() => {
            authCard.style.transition = 'all 0.6s ease';
            authCard.style.opacity = '1';
            authCard.style.transform = 'translateY(0)';
        }, 100);
    });
</script>
{% endblock %}
