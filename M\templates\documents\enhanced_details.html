{% extends "base.html" %}

{% block title %}التفاصيل المحسنة: {{ document.title }} - نظام إدارة الأرشيف العام{% endblock %}

{% block extra_css %}
<style>
    body {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        font-family: 'Cairo', sans-serif;
    }

    .enhanced-details-container {
        max-width: 1200px;
        margin: 2rem auto;
        padding: 0 1rem;
    }

    .document-header {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        border-radius: 20px;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: 0 20px 40px rgba(0,0,0,0.1);
    }

    .document-title {
        color: #2c3e50;
        font-size: 2rem;
        font-weight: 700;
        margin-bottom: 1rem;
        display: flex;
        align-items: center;
        gap: 1rem;
    }

    .completion-badge {
        padding: 0.5rem 1rem;
        border-radius: 20px;
        font-weight: 600;
        font-size: 0.9rem;
    }

    .completion-high { background: #d4edda; color: #155724; }
    .completion-medium { background: #fff3cd; color: #856404; }
    .completion-low { background: #f8d7da; color: #721c24; }

    .enhanced-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 2rem;
        margin-bottom: 2rem;
    }

    .enhanced-card {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        border-radius: 20px;
        padding: 2rem;
        box-shadow: 0 20px 40px rgba(0,0,0,0.1);
    }

    .card-header {
        display: flex;
        align-items: center;
        gap: 1rem;
        margin-bottom: 1.5rem;
    }

    .card-icon {
        width: 50px;
        height: 50px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1.3rem;
    }

    .card-title {
        color: #2c3e50;
        font-size: 1.3rem;
        font-weight: 600;
        margin: 0;
    }

    .metadata-fields {
        display: grid;
        gap: 1rem;
    }

    .field-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0.75rem;
        background: #f8f9fa;
        border-radius: 10px;
        border-left: 4px solid #667eea;
    }

    .field-label {
        font-weight: 600;
        color: #2c3e50;
        font-size: 0.9rem;
    }

    .field-value {
        color: #6c757d;
        font-size: 0.9rem;
        text-align: left;
    }

    .field-empty {
        color: #dc3545;
        font-style: italic;
    }

    .tracking-status {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        margin-bottom: 1rem;
    }

    .status-indicator {
        width: 12px;
        height: 12px;
        border-radius: 50%;
    }

    .status-fully-tracked { background: #28a745; }
    .status-partially-tracked { background: #ffc107; }
    .status-not-tracked { background: #dc3545; }

    .progress-section {
        margin-bottom: 1.5rem;
    }

    .progress-label {
        display: flex;
        justify-content: space-between;
        margin-bottom: 0.5rem;
        font-size: 0.9rem;
        font-weight: 600;
        color: #2c3e50;
    }

    .progress-bar-container {
        height: 10px;
        background: #e9ecef;
        border-radius: 5px;
        overflow: hidden;
    }

    .progress-bar {
        height: 100%;
        background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
        border-radius: 5px;
        transition: width 0.3s ease;
    }

    .recommendations {
        margin-top: 1rem;
    }

    .recommendation-item {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        padding: 0.75rem;
        background: #e3f2fd;
        border-radius: 8px;
        margin-bottom: 0.5rem;
        border-left: 4px solid #2196f3;
    }

    .recommendation-icon {
        color: #2196f3;
        font-size: 1.1rem;
    }

    .recommendation-text {
        color: #1565c0;
        font-size: 0.9rem;
        font-weight: 500;
    }

    .actions-section {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        border-radius: 20px;
        padding: 2rem;
        box-shadow: 0 20px 40px rgba(0,0,0,0.1);
    }

    .actions-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 1.5rem;
        margin-top: 1.5rem;
    }

    .action-form {
        border: 1px solid #e9ecef;
        border-radius: 15px;
        padding: 1.5rem;
        transition: all 0.3s ease;
    }

    .action-form:hover {
        border-color: #667eea;
        box-shadow: 0 5px 15px rgba(102, 126, 234, 0.1);
    }

    .form-header {
        display: flex;
        align-items: center;
        gap: 1rem;
        margin-bottom: 1rem;
    }

    .form-icon {
        width: 40px;
        height: 40px;
        border-radius: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1.1rem;
    }

    .icon-metadata { background: #667eea; }
    .icon-storage { background: #28a745; }
    .icon-number { background: #17a2b8; }
    .icon-location { background: #ffc107; color: #212529; }

    .form-title {
        color: #2c3e50;
        font-size: 1.1rem;
        font-weight: 600;
        margin: 0;
    }

    .form-group {
        margin-bottom: 1rem;
    }

    .form-label {
        font-weight: 600;
        color: #2c3e50;
        margin-bottom: 0.5rem;
        display: block;
        font-size: 0.9rem;
    }

    .form-control, .form-select {
        width: 100%;
        padding: 0.5rem;
        border: 1px solid #ddd;
        border-radius: 8px;
        font-size: 0.9rem;
    }

    .form-control:focus, .form-select:focus {
        outline: none;
        border-color: #667eea;
        box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.1);
    }

    .action-btn {
        width: 100%;
        padding: 0.75rem;
        border: none;
        border-radius: 8px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.5rem;
    }

    .btn-primary {
        background: #667eea;
        color: white;
    }

    .btn-success {
        background: #28a745;
        color: white;
    }

    .btn-info {
        background: #17a2b8;
        color: white;
    }

    .btn-warning {
        background: #ffc107;
        color: #212529;
    }

    .action-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0,0,0,0.2);
    }

    .back-section {
        text-align: center;
        margin-top: 2rem;
    }

    .btn-back {
        padding: 0.75rem 2rem;
        background: #6c757d;
        color: white;
        text-decoration: none;
        border-radius: 10px;
        font-weight: 600;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        transition: all 0.3s ease;
    }

    .btn-back:hover {
        background: #5a6268;
        color: white;
        text-decoration: none;
        transform: translateY(-2px);
    }

    @media (max-width: 768px) {
        .enhanced-details-container {
            margin: 1rem;
            padding: 0;
        }

        .enhanced-grid {
            grid-template-columns: 1fr;
        }

        .actions-grid {
            grid-template-columns: 1fr;
        }

        .document-title {
            font-size: 1.5rem;
            flex-direction: column;
            align-items: flex-start;
            gap: 0.5rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="enhanced-details-container">
    <!-- Document Header -->
    <div class="document-header">
        <div class="document-title">
            <i class="fas fa-file-alt"></i>
            {{ document.title }}
            <span class="completion-badge completion-{{ 'high' if enhanced_info.metadata_completion >= 80 else 'medium' if enhanced_info.metadata_completion >= 50 else 'low' }}">
                {{ enhanced_info.metadata_completion }}% مكتمل
            </span>
        </div>
        
        <div class="tracking-status">
            <span class="status-indicator status-{{ enhanced_info.tracking_status }}"></span>
            <span>
                {% if enhanced_info.tracking_status == 'fully_tracked' %}
                    متتبع بالكامل
                {% elif enhanced_info.tracking_status == 'partially_tracked' %}
                    متتبع جزئياً
                {% else %}
                    غير متتبع
                {% endif %}
            </span>
        </div>
    </div>

    <!-- Enhanced Information Grid -->
    <div class="enhanced-grid">
        <!-- Metadata Information -->
        <div class="enhanced-card">
            <div class="card-header">
                <div class="card-icon icon-metadata">
                    <i class="fas fa-tags"></i>
                </div>
                <h3 class="card-title">البيانات الوصفية المحسنة</h3>
            </div>

            <div class="metadata-fields">
                <div class="field-item">
                    <span class="field-label">رقم الوثيقة:</span>
                    <span class="field-value {{ 'field-empty' if not enhanced_info.document_number }}">
                        {{ enhanced_info.document_number or 'غير محدد' }}
                    </span>
                </div>
                
                <div class="field-item">
                    <span class="field-label">رقم التخزين:</span>
                    <span class="field-value {{ 'field-empty' if not enhanced_info.storage_number }}">
                        {{ enhanced_info.storage_number or 'غير محدد' }}
                    </span>
                </div>
                
                <div class="field-item">
                    <span class="field-label">رقم الخزانة:</span>
                    <span class="field-value {{ 'field-empty' if not enhanced_info.cabinet_number }}">
                        {{ enhanced_info.cabinet_number or 'غير محدد' }}
                    </span>
                </div>
                
                <div class="field-item">
                    <span class="field-label">رقم الرف:</span>
                    <span class="field-value {{ 'field-empty' if not enhanced_info.shelf_number }}">
                        {{ enhanced_info.shelf_number or 'غير محدد' }}
                    </span>
                </div>
            </div>

            <div class="progress-section">
                <div class="progress-label">
                    <span>اكتمال البيانات الوصفية</span>
                    <span>{{ enhanced_info.metadata_completion }}%</span>
                </div>
                <div class="progress-bar-container">
                    <div class="progress-bar" style="width: {{ enhanced_info.metadata_completion }}%"></div>
                </div>
            </div>
        </div>

        <!-- Physical Location Information -->
        <div class="enhanced-card">
            <div class="card-header">
                <div class="card-icon icon-location">
                    <i class="fas fa-map-marker-alt"></i>
                </div>
                <h3 class="card-title">الموقع الفيزيائي</h3>
            </div>

            <div class="metadata-fields">
                <div class="field-item">
                    <span class="field-label">الموقع العام:</span>
                    <span class="field-value {{ 'field-empty' if not enhanced_info.physical_location }}">
                        {{ enhanced_info.physical_location or 'غير محدد' }}
                    </span>
                </div>
                
                <div class="field-item">
                    <span class="field-label">الطابق:</span>
                    <span class="field-value {{ 'field-empty' if not enhanced_info.floor }}">
                        {{ enhanced_info.floor or 'غير محدد' }}
                    </span>
                </div>
                
                <div class="field-item">
                    <span class="field-label">الغرفة:</span>
                    <span class="field-value {{ 'field-empty' if not enhanced_info.room }}">
                        {{ enhanced_info.room or 'غير محدد' }}
                    </span>
                </div>
                
                <div class="field-item">
                    <span class="field-label">القسم:</span>
                    <span class="field-value {{ 'field-empty' if not enhanced_info.section }}">
                        {{ enhanced_info.section or 'غير محدد' }}
                    </span>
                </div>
            </div>

            {% if enhanced_info.storage_notes %}
            <div class="field-item" style="margin-top: 1rem;">
                <span class="field-label">ملاحظات التخزين:</span>
                <span class="field-value">{{ enhanced_info.storage_notes }}</span>
            </div>
            {% endif %}
        </div>
    </div>

    <!-- Recommendations -->
    {% if enhanced_info.recommendations %}
    <div class="enhanced-card" style="margin-bottom: 2rem;">
        <div class="card-header">
            <div class="card-icon" style="background: #2196f3;">
                <i class="fas fa-lightbulb"></i>
            </div>
            <h3 class="card-title">التوصيات</h3>
        </div>

        <div class="recommendations">
            {% for recommendation in enhanced_info.recommendations %}
            <div class="recommendation-item">
                <i class="fas fa-arrow-left recommendation-icon"></i>
                <span class="recommendation-text">{{ recommendation.message }}</span>
            </div>
            {% endfor %}
        </div>
    </div>
    {% endif %}

    <!-- Actions Section -->
    <div class="actions-section">
        <h2>
            <i class="fas fa-cogs me-2"></i>
            إجراءات التحسين
        </h2>

        <div class="actions-grid">
            <!-- Update Enhanced Metadata -->
            <div class="action-form">
                <div class="form-header">
                    <div class="form-icon icon-metadata">
                        <i class="fas fa-tags"></i>
                    </div>
                    <h4 class="form-title">تحديث البيانات المحسنة</h4>
                </div>

                <form method="POST">
                    <input type="hidden" name="action" value="update_metadata">
                    
                    <div class="form-group">
                        <label class="form-label">رقم الوثيقة</label>
                        <input type="text" name="document_number" class="form-control" 
                               value="{{ enhanced_info.document_number or '' }}" 
                               placeholder="مثال: DOC-2024-0001">
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">رقم التخزين</label>
                        <input type="text" name="storage_number" class="form-control" 
                               value="{{ enhanced_info.storage_number or '' }}" 
                               placeholder="مثال: STG-001">
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">رقم الخزانة</label>
                        <input type="text" name="cabinet_number" class="form-control" 
                               value="{{ enhanced_info.cabinet_number or '' }}" 
                               placeholder="مثال: CAB-01">
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">رقم الرف</label>
                        <input type="text" name="shelf_number" class="form-control" 
                               value="{{ enhanced_info.shelf_number or '' }}" 
                               placeholder="مثال: SHF-A1">
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">الموقع الفيزيائي</label>
                        <input type="text" name="physical_location" class="form-control" 
                               value="{{ enhanced_info.physical_location or '' }}" 
                               placeholder="مثال: الطابق الأول - القسم الإداري">
                    </div>
                    
                    <button type="submit" class="action-btn btn-primary">
                        <i class="fas fa-save"></i>
                        تحديث البيانات
                    </button>
                </form>
            </div>

            <!-- Assign Physical Storage -->
            <div class="action-form">
                <div class="form-header">
                    <div class="form-icon icon-storage">
                        <i class="fas fa-archive"></i>
                    </div>
                    <h4 class="form-title">تخصيص موقع التخزين</h4>
                </div>

                <form method="POST">
                    <input type="hidden" name="action" value="assign_storage">
                    
                    <div class="form-group">
                        <label class="form-label">رقم الخزانة</label>
                        <select name="cabinet_id" class="form-select">
                            <option value="">اختر الخزانة</option>
                            {% for i in range(1, 11) %}
                            <option value="{{ i }}" {{ 'selected' if enhanced_info.cabinet_number == i|string }}>
                                خزانة {{ i }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">رقم الرف</label>
                        <select name="shelf_id" class="form-select">
                            <option value="">اختر الرف</option>
                            {% for i in range(1, 6) %}
                            <option value="{{ i }}">رف {{ i }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">ملاحظات الموقع</label>
                        <input type="text" name="location_notes" class="form-control" 
                               placeholder="ملاحظات إضافية...">
                    </div>
                    
                    <button type="submit" class="action-btn btn-success">
                        <i class="fas fa-map-marker-alt"></i>
                        تخصيص الموقع
                    </button>
                </form>
            </div>

            <!-- Generate Document Number -->
            <div class="action-form">
                <div class="form-header">
                    <div class="form-icon icon-number">
                        <i class="fas fa-hashtag"></i>
                    </div>
                    <h4 class="form-title">إنشاء رقم الوثيقة</h4>
                </div>

                <form method="POST">
                    <input type="hidden" name="action" value="generate_number">
                    
                    <div class="form-group">
                        <label class="form-label">بادئة الرقم</label>
                        <select name="prefix" class="form-select">
                            <option value="DOC">DOC - وثائق عامة</option>
                            <option value="ARCH">ARCH - أرشيف</option>
                            <option value="ADMIN">ADMIN - إدارية</option>
                            <option value="FIN">FIN - مالية</option>
                            <option value="LEGAL">LEGAL - قانونية</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">معاينة الرقم</label>
                        <input type="text" class="form-control" 
                               value="DOC-{{ "now"|date('Y') }}-{{ '%04d'|format(1) }}" 
                               readonly style="background: #f8f9fa;">
                    </div>
                    
                    <button type="submit" class="action-btn btn-info">
                        <i class="fas fa-hashtag"></i>
                        إنشاء الرقم
                    </button>
                </form>
            </div>

            <!-- Update Physical Location -->
            <div class="action-form">
                <div class="form-header">
                    <div class="form-icon icon-location">
                        <i class="fas fa-map-marker-alt"></i>
                    </div>
                    <h4 class="form-title">تحديث الموقع الفيزيائي</h4>
                </div>

                <form method="POST">
                    <input type="hidden" name="action" value="update_location">
                    
                    <div class="form-group">
                        <label class="form-label">الموقع العام</label>
                        <select name="location_id" class="form-select">
                            <option value="">اختر الموقع</option>
                            <option value="1">الطابق الأول - الإدارة</option>
                            <option value="2">الطابق الأول - المالية</option>
                            <option value="3">الطابق الثاني - القانونية</option>
                            <option value="4">الطابق الثاني - الأرشيف</option>
                            <option value="5">الطابق الثالث - التاريخية</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">الطابق</label>
                        <input type="text" name="floor" class="form-control" 
                               value="{{ enhanced_info.floor or '' }}" 
                               placeholder="مثال: الطابق الأول">
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">الغرفة</label>
                        <input type="text" name="room" class="form-control" 
                               value="{{ enhanced_info.room or '' }}" 
                               placeholder="مثال: غرفة 101">
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">القسم</label>
                        <input type="text" name="section" class="form-control" 
                               value="{{ enhanced_info.section or '' }}" 
                               placeholder="مثال: القسم الإداري">
                    </div>
                    
                    <button type="submit" class="action-btn btn-warning">
                        <i class="fas fa-map-marker-alt"></i>
                        تحديث الموقع
                    </button>
                </form>
            </div>
        </div>
    </div>

    <!-- Back Button -->
    <div class="back-section">
        <a href="{{ url_for('view_document', id=document.id) }}" class="btn-back">
            <i class="fas fa-arrow-right"></i>
            العودة إلى الوثيقة
        </a>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 ENHANCED: Enhanced document details page loaded');
    
    // Initialize progress bars animation
    const progressBars = document.querySelectorAll('.progress-bar');
    progressBars.forEach(bar => {
        const width = bar.style.width;
        bar.style.width = '0%';
        setTimeout(() => {
            bar.style.width = width;
        }, 500);
    });
    
    // Add form validation
    const forms = document.querySelectorAll('form');
    forms.forEach(form => {
        form.addEventListener('submit', function(e) {
            const submitBtn = this.querySelector('button[type="submit"]');
            if (submitBtn) {
                submitBtn.disabled = true;
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري المعالجة...';
            }
        });
    });
    
    // Dynamic number preview
    const prefixSelect = document.querySelector('select[name="prefix"]');
    if (prefixSelect) {
        const previewInput = prefixSelect.closest('form').querySelector('input[readonly]');
        prefixSelect.addEventListener('change', function() {
            const currentYear = new Date().getFullYear();
            const prefix = this.value;
            previewInput.value = `${prefix}-${currentYear}-0001`;
        });
    }
    
    console.log('✅ ENHANCED: Interactive features initialized');
});
</script>
{% endblock %}
